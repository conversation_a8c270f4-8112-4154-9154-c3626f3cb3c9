#!/usr/bin/env python3
"""
创建管理员账户的命令行脚本

使用方法:
python scripts/create_admin.py
python scripts/create_admin.py --username admin --email <EMAIL> --password mypassword
"""

import sys
import argparse
from pathlib import Path

# 将项目根目录添加到 sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.session import SessionLocal
from app.crud.crud_user import user as crud_user
from app.schemas.user import AdminUserCreate
from app import models
from app.core.config import settings


def create_admin_user(
    username: str,
    email: str,
    password: str,
    nickname: str = None
) -> bool:
    """
    创建管理员用户
    
    Args:
        username: 用户名
        email: 邮箱
        password: 密码
        nickname: 昵称（可选）
    
    Returns:
        bool: 创建成功返回 True，否则返回 False
    """
    db = SessionLocal()
    try:
        # 检查用户名是否已存在
        existing_user = crud_user.get_user_by_username(db, username=username)
        if existing_user:
            print(f"错误: 用户名 '{username}' 已存在")
            return False
        
        # 检查邮箱是否已存在
        if email:
            existing_email = crud_user.get_user_by_email(db, email=email)
            if existing_email:
                print(f"错误: 邮箱 '{email}' 已被使用")
                return False
        
        # 创建管理员账户
        admin_user = AdminUserCreate(
            username=username,
            email=email,
            password=password,
            role=models.UserRole.admin,
            status=models.UserStatus.active,
            nickname=nickname or "管理员"
        )
        
        db_admin = crud_user.create_user_by_admin(db=db, obj_in=admin_user)
        
        print("✅ 管理员账户创建成功!")
        print(f"   用户名: {db_admin.username}")
        print(f"   邮箱: {db_admin.email}")
        print(f"   昵称: {db_admin.nickname}")
        print(f"   角色: {db_admin.role}")
        print(f"   状态: {db_admin.status}")
        print(f"   创建时间: {db_admin.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建管理员账户失败: {e}")
        return False
    finally:
        db.close()


def interactive_create_admin():
    """
    交互式创建管理员账户
    """
    print("=== 创建管理员账户 ===")
    print()
    
    # 获取用户输入
    username = input(f"请输入用户名 (默认: {settings.DEFAULT_ADMIN_USERNAME}): ").strip()
    if not username:
        username = settings.DEFAULT_ADMIN_USERNAME
    
    email = input(f"请输入邮箱 (默认: {settings.DEFAULT_ADMIN_EMAIL}): ").strip()
    if not email:
        email = settings.DEFAULT_ADMIN_EMAIL
    
    password = input(f"请输入密码 (默认: {settings.DEFAULT_ADMIN_PASSWORD}): ").strip()
    if not password:
        password = settings.DEFAULT_ADMIN_PASSWORD
    
    nickname = input("请输入昵称 (默认: 系统管理员): ").strip()
    if not nickname:
        nickname = "系统管理员"
    
    print()
    print("=== 确认信息 ===")
    print(f"用户名: {username}")
    print(f"邮箱: {email}")
    print(f"密码: {'*' * len(password)}")
    print(f"昵称: {nickname}")
    print()
    
    confirm = input("确认创建管理员账户? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        return create_admin_user(username, email, password, nickname)
    else:
        print("已取消创建")
        return False


def main():
    parser = argparse.ArgumentParser(description="创建管理员账户")
    parser.add_argument("--username", "-u", help="管理员用户名")
    parser.add_argument("--email", "-e", help="管理员邮箱")
    parser.add_argument("--password", "-p", help="管理员密码")
    parser.add_argument("--nickname", "-n", help="管理员昵称")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式创建")
    
    args = parser.parse_args()
    
    # 如果没有提供参数或指定了交互模式，则使用交互式创建
    if args.interactive or not any([args.username, args.email, args.password]):
        success = interactive_create_admin()
    else:
        # 使用命令行参数创建
        username = args.username or settings.DEFAULT_ADMIN_USERNAME
        email = args.email or settings.DEFAULT_ADMIN_EMAIL
        password = args.password or settings.DEFAULT_ADMIN_PASSWORD
        nickname = args.nickname or "管理员"
        
        success = create_admin_user(username, email, password, nickname)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
