# app/core/openapi.py
"""
OpenAPI 配置和自定义
"""
from typing import Dict, Any
from fastapi import Fast<PERSON><PERSON>
from fastapi.openapi.utils import get_openapi


def custom_openapi(app: FastAPI) -> Dict[str, Any]:
    """
    自定义 OpenAPI schema 生成
    """
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # 添加全局响应示例
    openapi_schema["components"]["responses"] = {
        "ValidationError": {
            "description": "数据验证错误",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "loc": {
                                            "type": "array",
                                            "items": {"type": "string"}
                                        },
                                        "msg": {"type": "string"},
                                        "type": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "examples": {
                        "validation_error": {
                            "summary": "字段验证错误",
                            "value": {
                                "detail": [
                                    {
                                        "loc": ["body", "username"],
                                        "msg": "field required",
                                        "type": "value_error.missing"
                                    },
                                    {
                                        "loc": ["body", "password"],
                                        "msg": "ensure this value has at least 6 characters",
                                        "type": "value_error.any_str.min_length"
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        },
        "Unauthorized": {
            "description": "未认证",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string"}
                        }
                    },
                    "examples": {
                        "missing_token": {
                            "summary": "缺少认证令牌",
                            "value": {
                                "detail": "Not authenticated"
                            }
                        },
                        "invalid_token": {
                            "summary": "无效的认证令牌",
                            "value": {
                                "detail": "Could not validate credentials"
                            }
                        },
                        "expired_token": {
                            "summary": "令牌已过期",
                            "value": {
                                "detail": "Token has expired"
                            }
                        }
                    }
                }
            }
        },
        "Forbidden": {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string"}
                        }
                    },
                    "examples": {
                        "insufficient_permissions": {
                            "summary": "权限不足",
                            "value": {
                                "detail": "权限不足，无法访问此资源"
                            }
                        },
                        "account_disabled": {
                            "summary": "账户被禁用",
                            "value": {
                                "detail": "用户账户已被禁用或未激活"
                            }
                        }
                    }
                }
            }
        },
        "NotFound": {
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string"}
                        }
                    },
                    "examples": {
                        "resource_not_found": {
                            "summary": "资源不存在",
                            "value": {
                                "detail": "请求的资源不存在"
                            }
                        },
                        "user_not_found": {
                            "summary": "用户不存在",
                            "value": {
                                "detail": "用户不存在"
                            }
                        },
                        "score_not_found": {
                            "summary": "谱曲不存在",
                            "value": {
                                "detail": "谱曲不存在"
                            }
                        }
                    }
                }
            }
        },
        "Conflict": {
            "description": "资源冲突",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string"}
                        }
                    },
                    "examples": {
                        "username_exists": {
                            "summary": "用户名已存在",
                            "value": {
                                "detail": "用户名已被注册，请尝试其他用户名。"
                            }
                        },
                        "email_exists": {
                            "summary": "邮箱已存在",
                            "value": {
                                "detail": "电子邮箱已被注册，请尝试其他邮箱。"
                            }
                        }
                    }
                }
            }
        },
        "InternalServerError": {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string"}
                        }
                    },
                    "examples": {
                        "server_error": {
                            "summary": "服务器内部错误",
                            "value": {
                                "detail": "服务器内部错误，请稍后重试"
                            }
                        }
                    }
                }
            }
        }
    }

    # 添加安全方案
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT Bearer Token 认证。在请求头中添加：Authorization: Bearer <your_access_token>"
        }
    }

    # 添加全局安全要求（可选，也可以在具体路由中指定）
    # openapi_schema["security"] = [{"BearerAuth": []}]

    # 添加标签描述
    openapi_schema["tags"] = [
        {
            "name": "认证模块",
            "description": "用户认证相关接口，包括登录、注册、密码重置等"
        },
        {
            "name": "用户接口 - 当前用户",
            "description": "当前登录用户的个人信息管理接口"
        },
        {
            "name": "谱曲信息模块",
            "description": "谱曲的增删改查、搜索、分类等功能"
        },
        {
            "name": "谱曲分类模块",
            "description": "谱曲分类的管理和查询"
        },
        {
            "name": "排行榜模块",
            "description": "各种排行榜数据查询"
        },
        {
            "name": "工单模块",
            "description": "用户反馈和客服支持系统"
        },
        {
            "name": "通用附件模块",
            "description": "文件上传和附件管理"
        },
        {
            "name": "代理模块",
            "description": "代理商相关功能"
        },
        {
            "name": "代理认证模块",
            "description": "代理商认证相关接口"
        },
        {
            "name": "管理端",
            "description": "管理员后台功能"
        },
        {
            "name": "Admin - Auth",
            "description": "管理员认证"
        },
        {
            "name": "Admin - Users",
            "description": "用户管理"
        },
        {
            "name": "Admin - Scores",
            "description": "谱曲管理"
        },
        {
            "name": "Admin - License Keys",
            "description": "卡密管理"
        },
        {
            "name": "Admin - Reseller Levels",
            "description": "代理级别管理"
        },
        {
            "name": "Admin - Resellers",
            "description": "代理账户管理"
        },
        {
            "name": "Admin - Financial Transactions",
            "description": "财务管理"
        },
        {
            "name": "Admin - Tickets",
            "description": "工单管理"
        },
        {
            "name": "Admin - Categories",
            "description": "分类管理"
        },
        {
            "name": "Admin - Comments",
            "description": "评论管理"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


def setup_openapi(app: FastAPI) -> None:
    """
    设置自定义 OpenAPI
    """
    app.openapi = lambda: custom_openapi(app)
