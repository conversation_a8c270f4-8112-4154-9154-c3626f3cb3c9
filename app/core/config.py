# app/core/config.py
import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from typing import Optional

# 项目根目录的路径，或者 .env 文件所在的目录路径
# 如果 .env 文件在 TanZouDaShiAPI 目录下，则应该是 ../../.env.example (相对于当前文件)
# 更稳妥的方式是使用绝对路径或基于项目根目录的相对路径
# 例如，如果 TanZouDaShiAPI 是项目根目录：
# BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# load_dotenv(os.path.join(BASE_DIR, ".env"))
# 或者，如果 .env 在 TanZouDaShiAPI 目录下:
# load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env'))

# 假设 .env 文件与 pyproject.toml 同级，即在 TanZouDaShiAPI 目录下
# __file__ 是 app/core/config.py
# os.path.dirname(__file__) 是 app/core
# os.path.dirname(os.path.dirname(__file__)) 是 app
# os.path.dirname(os.path.dirname(os.path.dirname(__file__))) 是 TanZouDaShiAPI
env_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), ".env"
)
load_dotenv(dotenv_path=env_path)  # 加载 .env 文件，如果存在


class Settings(BaseSettings):
    PROJECT_NAME: str = "弹奏大师API"
    PROJECT_VERSION: str = "1.0.0"  # 项目版本
    SECRET_KEY: str = "your_super_secret_key_for_jwt_please_change_in_production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7  # 刷新令牌过期天数
    APP_PORT: int = 8001  # 应用程序运行的默认端口

    # 默认管理员账户配置
    DEFAULT_ADMIN_USERNAME: str = "admin"
    DEFAULT_ADMIN_PASSWORD: str = "admin123456"
    DEFAULT_ADMIN_EMAIL: str = "<EMAIL>"
    CREATE_DEFAULT_ADMIN: bool = True  # 是否在初始化时创建默认管理员

    # MySQL 配置
    MYSQL_USER: Optional[str] = None
    MYSQL_PASSWORD: Optional[str] = None
    MYSQL_HOST: Optional[str] = None
    MYSQL_PORT: int = 3306
    MYSQL_DB: Optional[str] = None
    MYSQL_CHARSET: str = "utf8mb4"

    # 数据库连接字符串（如果直接设置）
    DB_URL: Optional[str] = None

    @property
    def database_url(self) -> str:
        """
        获取数据库连接URL
        """
        # 如果配置了 DB_URL，优先使用
        if self.DB_URL:
            return self.DB_URL

        # 否则尝试使用 MySQL 配置
        if all([self.MYSQL_USER, self.MYSQL_PASSWORD, self.MYSQL_HOST, self.MYSQL_DB]):
            return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DB}?charset={self.MYSQL_CHARSET}"

        # 默认使用 SQLite
        return "sqlite:///./tanzoudashi.db"

    # SQLALCHEMY_DATABASE_URL 属性用于兼容现有代码
    @property
    def SQLALCHEMY_DATABASE_URL(self) -> str:
        """
        数据库连接URL，保持与之前代码的兼容性
        """
        return self.database_url

    # DATABASE_URL 属性用于兼容现有代码
    @property
    def DATABASE_URL(self) -> str:
        """
        数据库连接URL，保持与之前代码的兼容性
        """
        return self.database_url

    # API 版本前缀 (可选，如果 main.py 中使用了)
    # API_V1_STR: str = "/api/v1"

    # 邮件配置 (如果需要发送邮件)
    # MAIL_USERNAME: Optional[str] = None
    # MAIL_PASSWORD: Optional[str] = None
    # MAIL_FROM: Optional[str] = None
    # MAIL_PORT: Optional[int] = None
    # MAIL_SERVER: Optional[str] = None
    # MAIL_FROM_NAME: Optional[str] = None
    # MAIL_TLS: bool = True
    # MAIL_SSL: bool = False
    # USE_CREDENTIALS: bool = True
    # VALIDATE_CERTS: bool = True

    # CORS 跨域配置
    CORS_ALLOW_ORIGINS: list[str] = [
        "http://localhost",
        "http://localhost:3000",
        "http://localhost:5173",  # Vite 默认端口
        "http://localhost:8080",  # Vue CLI 默认端口
        "http://localhost:8081",
        "http://127.0.0.1",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",  # Vite 默认端口
        "http://127.0.0.1:8080",  # Vue CLI 默认端口
        "http://127.0.0.1:8081",
    ]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = ["*"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]
    CORS_ALLOW_ALL_ORIGINS: bool = False  # 是否允许所有源（开发时可设为True）

    # 附件上传相关配置
    ATTACHMENT_ALLOWED_EXTENSIONS: set[str] = {
        "png",
        "jpg",
        "jpeg",
        "gif",
        "pdf",
        "mid",
        "midi",
        "txt",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "ppt",
        "pptx",
    }
    ATTACHMENT_MAX_SIZE_MB: int = 20  # 单位 MB
    ATTACHMENT_BASE_UPLOAD_DIR: str = "static/uploads"  # 相对于项目根目录
    AVATARS_UPLOAD_DIR: str = "static/uploads/avatars"  # 用户头像上传目录

    class Config:
        env_file = ".env"  # 指定 .env 文件名，Pydantic-settings 会尝试加载
        env_file_encoding = "utf-8"
        # case_sensitive = True # pydantic v1 的设置, v2 中默认不区分大小写，但推荐环境变量大写


settings = Settings()

# 可以在这里添加一些对配置的校验逻辑
# 例如，检查 SECRET_KEY 是否足够复杂等
if settings.SECRET_KEY == "your_secret_key_here_please_change_it":
    print("警告: SECRET_KEY 未修改，请在 .env 文件中设置一个安全的密钥！")
