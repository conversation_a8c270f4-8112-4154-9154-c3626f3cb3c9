# app/core/security.py
import shutil
import random
import string
from datetime import datetime, timedelta, timezone
from typing import Optional, Any
from pathlib import Path

from jose import jwt, JWTError
from passlib.context import CryptContext
from fastapi import UploadFile

from app.core.config import settings

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

ALGORITHM = settings.ALGORITHM
JWT_SECRET_KEY = settings.SECRET_KEY  # 从配置中读取密钥
ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES


def create_access_token(subject: Any, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建 JWT access token。

    :param subject: token 的主题，可以是用户ID或其他唯一标识符。
    :param expires_delta: token 的可选过期时间增量。如果未提供，则使用默认配置。
    :return: JWT 字符串。
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证明文密码和哈希密码是否匹配。

    :param plain_password: 明文密码。
    :param hashed_password: 哈希后的密码。
    :return: 如果匹配则返回 True，否则返回 False。
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    对明文密码进行哈希处理。

    :param password: 明文密码。
    :return: 哈希后的密码字符串。
    """
    return pwd_context.hash(password)


# JWT 令牌解码和验证函数 (后续用于 get_current_user)
# 这是一个初步的结构，完整的实现会更复杂，需要处理各种异常和用户查找
def decode_access_token(token: str) -> Optional[dict]:
    """
    解码并验证 access token。

    :param token: JWT token 字符串。
    :return: 如果 token 有效，则返回 payload；否则返回 None。
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        return payload
        # 后续可以从 payload 中提取用户ID: payload.get("sub")
    except JWTError:
        # JWTError 包括 ExpiredSignatureError, InvalidTokenError 等
        return None


# 示例：如何从 token 中获取 subject (通常是 user_id)
# def get_subject_from_token(token: str) -> Optional[str]:
#     payload = decode_access_token(token)
#     if payload:
#         return payload.get("sub")
#     return None


def save_upload_file_locally(
    upload_dir: str | Path, file: UploadFile, filename: str
) -> Path:
    # upload_dir 可以是字符串或Path对象，确保转换为Path对象
    save_path_dir = Path(upload_dir)
    save_path_dir.mkdir(parents=True, exist_ok=True)  # 确保目标目录存在
    save_path = save_path_dir / filename
    with open(save_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return save_path


def generate_random_string(length: int = 8) -> str:
    return "".join(random.choices(string.ascii_lowercase + string.digits, k=length))
