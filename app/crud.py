# app/crud.py
"""
这个文件用于从各个crud模块导出crud对象，方便在其他地方统一导入
"""

# 导入基础CRUD类
from app.crud.base import CRUDBase

# 从各模块导入crud对象
from app.crud.crud_user import user
from app.crud.crud_license import license_type, license_key
from app.crud.crud_score import score
from app.crud.crud_category import category
from app.crud.crud_device import user_bound_device
from app.crud.crud_session_log import user_session_log
from app.crud.crud_ticket import ticket, ticket_message
from app.crud.crud_user_interaction import user_favorite, user_score_rating, score_comment
from app.crud.crud_reseller import crud_reseller_level, crud_reseller
from app.crud.crud_reseller_finance import crud_commission_record, crud_withdrawal_record
