# app/main.py
import sys
import os
from pathlib import Path

# 将项目根目录添加到 sys.path
# __file__ 是 app/main.py
# os.path.dirname(__file__) 是 app
# os.path.dirname(os.path.dirname(__file__)) 是项目根目录 (弹奏大师 API)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles  # 导入 StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from app.apis.v1.api import api_router_v1  # 修正导入的变量名
from app.apis.admin.api import api_router_admin  # 引入管理端API路由
from app.core.config import settings
from app.core.openapi import setup_openapi  # 导入自定义 OpenAPI 配置
from app.db.init_db import init_db, create_initial_data
from app.db.session import SessionLocal

# 确保 static 目录存在
static_dir = Path("static")
static_dir.mkdir(parents=True, exist_ok=True)
# 确保 static/uploads 目录也存在 (附件上传的基础目录)
uploads_dir = static_dir / "uploads"
uploads_dir.mkdir(parents=True, exist_ok=True)

# 确保 static/uploads/avatars 目录也存在 (用户头像上传目录)
avatars_upload_dir = Path(settings.AVATARS_UPLOAD_DIR)
avatars_upload_dir.mkdir(parents=True, exist_ok=True)


app = FastAPI(
    title=settings.PROJECT_NAME,
    description="""
## 弹奏大师 API

这是弹奏大师应用的后端 API 文档。

### 主要功能模块

- **认证模块**：用户注册、登录、密码重置等
- **用户管理**：用户信息管理、设备绑定等
- **谱曲管理**：谱曲上传、查看、搜索、分类等
- **用户交互**：收藏、评分、评论等
- **工单系统**：用户反馈和客服支持
- **管理后台**：管理员功能

### 认证方式

API 使用 JWT Bearer Token 进行认证。在需要认证的接口中，请在请求头中添加：
```
Authorization: Bearer <your_access_token>
```

### 响应格式

API 统一使用 JSON 格式响应，包含以下标准字段：

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "错误描述信息",
  "data": null
}
```

### 分页响应

列表接口统一使用分页格式：
```json
{
  "meta": {
    "page": 1,
    "limit": 20,
    "total_items": 100,
    "total_pages": 5
  },
  "data": [...]
}
```

### 状态码说明

- **200 OK**：请求成功
- **201 Created**：资源创建成功
- **400 Bad Request**：请求参数错误
- **401 Unauthorized**：未认证或认证失败
- **403 Forbidden**：权限不足
- **404 Not Found**：资源不存在
- **409 Conflict**：资源冲突（如重复创建）
- **422 Unprocessable Entity**：请求格式正确但语义错误
- **500 Internal Server Error**：服务器内部错误
    """,
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",  # 根据接口设计文档，版本控制 /api/v1/...
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "弹奏大师开发团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
    },
)

# 设置自定义 OpenAPI 配置
setup_openapi(app)

# 初始化数据库
try:
    init_db()
    print("数据库表已成功创建或已存在")

    # 创建初始数据（包括默认管理员账户）
    db = SessionLocal()
    try:
        create_initial_data(db)
        print("初始数据创建完成")
    except Exception as e:
        print(f"创建初始数据失败: {e}")
    finally:
        db.close()

except Exception as e:
    print(f"数据库初始化失败: {e}")

# 配置 CORS 跨域中间件
if settings.CORS_ALLOW_ALL_ORIGINS:
    # 开发环境：允许所有源（注意：当允许所有源时，不能启用 credentials）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 允许所有源
        allow_credentials=False,  # 允许所有源时必须为 False
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )
else:
    # 生产环境：只允许指定的源
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ALLOW_ORIGINS,  # 使用配置文件中的源列表
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )


@app.get(
    "/",
    summary="根路径",
    description="API 根路径，返回欢迎信息和基本的 API 状态。",
    responses={
        200: {
            "description": "API 运行正常",
            "content": {
                "application/json": {
                    "examples": {
                        "welcome": {
                            "summary": "欢迎信息",
                            "value": {
                                "message": "欢迎来到 弹奏大师 API!",
                                "version": "1.0.0",
                                "status": "running",
                                "docs_url": "/docs",
                                "redoc_url": "/redoc"
                            }
                        }
                    }
                }
            }
        }
    }
)
async def root():
    """
    根路径端点，返回一个欢迎信息和 API 基本信息。
    """
    return {
        "message": "欢迎来到 弹奏大师 API!",
        "version": "1.0.0",
        "status": "running",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }


# 引入 API V1 路由
app.include_router(api_router_v1, prefix="/api/v1")  # 保持与接口文档一致的前缀

# 引入管理端 API 路由
app.include_router(
    api_router_admin, prefix="/api/admin", tags=["管理端"]
)  # 管理端API路由

# 挂载静态文件目录
# 这将使得 static 目录下的文件可以通过 /static/... URL 访问
# 例如 static/uploads/avatar.jpg 可以通过 /static/uploads/avatar.jpg 访问
app.mount("/static", StaticFiles(directory="static"), name="static")

# 后续可以添加其他全局配置，例如自定义异常处理器等

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=settings.APP_PORT)
