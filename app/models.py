# app/models.py
"""
这个文件用于从 app.db.models 导出模型以便在其他地方使用
"""

# Import all models and enums from app.db.models

# User models and enums
from app.db.models.user import (
    User,
    Reseller,
    ResellerLevel,
    UserRole,
    UserStatus,
    UserCreationSource,
    ResellerStatus,
)

# Score models and enums
from app.db.models.score import (
    Score,
    ScoreType,
    MidiStorageType,
    ScoreDifficulty,
    ScoreStatus,
)

# Category models
from app.db.models.category import Category

# Device models
from app.db.models.device import UserBoundDevice

# License models and enums
from app.db.models.license import (
    LicenseKey,
    LicenseType,
    LicenseKeyStatus,
    LicenseSettlementStatus,
    LicenseDurationUnit,
)

# Log models
from app.db.models.log import UserSessionLog

# Ticket models and enums
from app.db.models.ticket import (
    Ticket,
    TicketMessage,
    TicketStatus,
    TicketPriority,
    TicketMessageType,
)

# User interaction models and enums
from app.db.models.user_interaction import (
    UserFavorite,
    UserScoreRating,
    ScoreComment,
    ScoreCommentStatus,
)

# Reseller finance models and enums
from app.db.models.reseller_finance import (
    CommissionRecord,
    WithdrawalRecord,
    CommissionType,
    CommissionStatus,
    WithdrawalStatus,
)

# Export all models and enums for easy importing
__all__ = [
    # User models and enums
    "User",
    "Reseller",
    "ResellerLevel",
    "UserRole",
    "UserStatus",
    "UserCreationSource",
    "ResellerStatus",
    # Score models and enums
    "Score",
    "ScoreType",
    "MidiStorageType",
    "ScoreDifficulty",
    "ScoreStatus",
    # Category models
    "Category",
    # Device models
    "UserBoundDevice",
    # License models and enums
    "LicenseKey",
    "LicenseType",
    "LicenseKeyStatus",
    "LicenseSettlementStatus",
    "LicenseDurationUnit",
    # Log models
    "UserSessionLog",
    # Ticket models and enums
    "Ticket",
    "TicketMessage",
    "TicketStatus",
    "TicketPriority",
    "TicketMessageType",
    # User interaction models and enums
    "UserFavorite",
    "UserScoreRating",
    "ScoreComment",
    "ScoreCommentStatus",
    # Reseller finance models and enums
    "CommissionRecord",
    "WithdrawalRecord",
    "CommissionType",
    "CommissionStatus",
    "WithdrawalStatus",
]
