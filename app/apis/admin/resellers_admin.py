from typing import List, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session

from app.schemas.reseller import (
    ResellerLevelResponse,
    ResellerLevelCreate,
    ResellerLevelUpdate,
    ResellerInfoResponse,
    AdminResellerCreateRequest,
    ResellerCreate,
    AdminResellerUpdateRequest,
    ResellerStatusUpdate,
)
from app.schemas.msg import Msg
from app.schemas.transaction import WithdrawalRecordResponse, WithdrawalProcessRequest

from app import models  # <--- 确保 models 从 app 导入
from app.crud.crud_reseller import crud_reseller, crud_reseller_level
from app.crud.crud_reseller_finance import crud_withdrawal_record
from app.crud.crud_user import user as crud_user  # crud_user 在管理员设置代理时仍需要

from app.apis import deps as api_deps


router_reseller_levels = APIRouter()
router_resellers = APIRouter()


@router_reseller_levels.get(
    "/",
    response_model=List[ResellerLevelResponse],
    summary="管理员获取代理级别列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_get_reseller_levels(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    is_active: Optional[bool] = Query(None, description="按激活状态筛选"),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    filters = {}
    if is_active is not None:
        filters["is_active"] = is_active

    levels = crud_reseller_level.get_multi_with_filters(
        db, skip=skip, limit=limit, filters=filters
    )
    return levels


@router_reseller_levels.post(
    "/",
    response_model=ResellerLevelResponse,
    status_code=status.HTTP_201_CREATED,
    summary="管理员创建代理级别",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_create_reseller_level(
    level_in: ResellerLevelCreate,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    existing_level_by_value = crud_reseller_level.get_by_level_value(
        db, level_value=level_in.level_value
    )
    if existing_level_by_value:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"级别值 '{level_in.level_value}' 已存在。",
        )

    existing_level_by_name = crud_reseller_level.get_by_name(db, name=level_in.name)
    if existing_level_by_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"级别名称 '{level_in.name}' 已存在。",
        )

    new_level = crud_reseller_level.create(db, obj_in=level_in)
    return new_level


@router_reseller_levels.get(
    "/{level_id}",
    response_model=ResellerLevelResponse,
    summary="获取特定代理级别详情",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_reseller_level_by_id(
    *,
    db: Session = Depends(api_deps.get_db),
    level_id: int = Path(..., description="代理级别的ID"),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> models.ResellerLevel:
    level = crud_reseller_level.get(db, id=level_id)
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="代理级别未找到"
        )
    return level


@router_reseller_levels.put(
    "/{level_id}",
    response_model=ResellerLevelResponse,
    summary="管理员更新代理级别",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_update_reseller_level(
    level_id: int = Path(..., description="要更新的代理级别ID", ge=1),
    *,
    level_update_data: ResellerLevelUpdate,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    level_obj = crud_reseller_level.get(db, id=level_id)
    if not level_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="未找到指定的代理级别"
        )

    update_data_dict = level_update_data.model_dump(exclude_unset=True)

    if (
        "level_value" in update_data_dict
        and update_data_dict["level_value"] != level_obj.level_value
    ):
        existing = crud_reseller_level.get_by_level_value(
            db, level_value=update_data_dict["level_value"]
        )
        if existing and existing.id != level_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"级别值 '{update_data_dict['level_value']}' 已被其他级别使用。",
            )

    if "name" in update_data_dict and update_data_dict["name"] != level_obj.name:
        existing = crud_reseller_level.get_by_name(db, name=update_data_dict["name"])
        if existing and existing.id != level_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"级别名称 '{update_data_dict['name']}' 已被其他级别使用。",
            )

    updated_level = crud_reseller_level.update(
        db, db_obj=level_obj, obj_in=update_data_dict
    )
    return updated_level


@router_reseller_levels.delete(
    "/{level_id}",
    response_model=Msg,
    summary="删除代理级别",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def delete_reseller_level(
    *,
    db: Session = Depends(api_deps.get_db),
    level_id: int = Path(..., description="要删除的代理级别ID"),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> Any:
    level = crud_reseller_level.get(db, id=level_id)
    if not level:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="代理级别未找到"
        )

    can_delete, message = True, ""
    if not can_delete:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=message)

    crud_reseller_level.remove(db, id=level_id)
    return Msg(message="代理级别删除成功")


@router_resellers.get(
    "/",
    response_model=List[ResellerInfoResponse],
    summary="管理员获取代理列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_get_all_resellers(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    username_filter: Optional[str] = Query(None, description="按用户名筛选 (模糊匹配)"),
    level_value_filter: Optional[int] = Query(None, description="按代理级别值筛选"),
    status_filter: Optional[models.ResellerStatus] = Query(
        None, description="按代理状态筛选"
    ),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    filters = {}
    if username_filter:
        filters["user_username_ilike"] = username_filter
    if level_value_filter is not None:
        filters["reseller_level_value"] = level_value_filter
    if status_filter:
        filters["status"] = status_filter

    resellers = crud_reseller.get_multi_detailed(
        db, skip=skip, limit=limit, filters=filters
    )
    return resellers


@router_resellers.post(
    "/",
    response_model=ResellerInfoResponse,
    status_code=status.HTTP_201_CREATED,
    summary="管理员设置用户为代理",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_set_user_as_reseller(
    reseller_data: AdminResellerCreateRequest,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    target_user = crud_user.get(db, id=reseller_data.user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"用户ID {reseller_data.user_id} 未找到。",
        )

    existing_reseller = crud_reseller.get_by_user_id(db, user_id=reseller_data.user_id)
    if existing_reseller:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户 {target_user.username} (ID: {target_user.id}) 已经是代理了。",
        )

    if reseller_data.reseller_level_value:
        level = crud_reseller_level.get_by_level_value(
            db, level_value=reseller_data.reseller_level_value
        )
        if not level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别值 {reseller_data.reseller_level_value} 无效或未找到。",
            )
        if not level.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别 '{level.name}' 未激活。",
            )

    if reseller_data.parent_reseller_user_id:
        parent_reseller_user = crud_user.get(
            db, id=reseller_data.parent_reseller_user_id
        )
        if not parent_reseller_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {reseller_data.parent_reseller_user_id} 未找到。",
            )
        parent_reseller_profile = crud_reseller.get_by_user_id(
            db, user_id=reseller_data.parent_reseller_user_id
        )
        if not parent_reseller_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {reseller_data.parent_reseller_user_id} 不是一个有效的代理。",
            )

    reseller_create_schema = ResellerCreate(
        **reseller_data.model_dump(exclude_unset=True)
    )
    new_reseller_profile = crud_reseller.create(db, obj_in=reseller_create_schema)

    if target_user.role != models.UserRole.admin:  # 假设 admin 不应该被降级为 reseller
        crud_user.update(
            db, db_obj=target_user, obj_in={"role": models.UserRole.reseller}
        )

    return new_reseller_profile


@router_resellers.get(
    "/{reseller_user_id}",
    response_model=ResellerInfoResponse,
    summary="获取特定代理账户详情 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_reseller_by_user_id_admin(
    *,
    db: Session = Depends(api_deps.get_db),
    reseller_user_id: int = Path(..., description="代理账户关联的用户ID"),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> models.Reseller:
    reseller = crud_reseller.get_by_user_id(db, user_id=reseller_user_id)
    if not reseller:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="代理账户未找到"
        )
    return reseller


@router_resellers.put(
    "/{reseller_user_id}",
    response_model=ResellerInfoResponse,
    summary="管理员更新代理信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_update_reseller_profile(
    reseller_user_id: int = Path(..., description="要更新的代理的用户ID", ge=1),
    *,
    reseller_update_data: AdminResellerUpdateRequest,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    reseller_profile = crud_reseller.get_by_user_id(db, user_id=reseller_user_id)
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID为 {reseller_user_id} 的代理信息。",
        )

    update_data_dict = reseller_update_data.model_dump(exclude_unset=True)

    if (
        "reseller_level_value" in update_data_dict
        and update_data_dict["reseller_level_value"] is not None
    ):
        level = crud_reseller_level.get_by_level_value(
            db, level_value=update_data_dict["reseller_level_value"]
        )
        if not level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别值 {update_data_dict['reseller_level_value']} 无效或未找到。",
            )
        if not level.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别 '{level.name}' 未激活。",
            )

    if (
        "parent_reseller_user_id" in update_data_dict
        and update_data_dict["parent_reseller_user_id"] is not None
    ):
        if update_data_dict["parent_reseller_user_id"] == reseller_user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能将自己设置为自己的上级代理。",
            )
        parent_reseller_user = crud_user.get(
            db, id=update_data_dict["parent_reseller_user_id"]
        )
        if not parent_reseller_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {update_data_dict['parent_reseller_user_id']} 未找到。",
            )
        parent_reseller_profile = crud_reseller.get_by_user_id(
            db, user_id=update_data_dict["parent_reseller_user_id"]
        )
        if not parent_reseller_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {update_data_dict['parent_reseller_user_id']} 不是一个有效的代理。",
            )

    updated_profile = crud_reseller.update(
        db, db_obj=reseller_profile, obj_in=update_data_dict
    )
    return updated_profile


@router_resellers.patch(
    "/{reseller_user_id}/status",
    response_model=ResellerInfoResponse,
    summary="管理员更新代理账户状态 (启用/停用/冻结)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_reseller_status_admin(
    *,
    db: Session = Depends(api_deps.get_db),
    reseller_user_id: int = Path(..., description="要更新状态的代理账户的用户ID"),
    status_in: ResellerStatusUpdate = Body(...),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> models.Reseller:
    reseller = crud_reseller.get_by_user_id(db, user_id=reseller_user_id)
    if not reseller:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="代理账户未找到"
        )

    reseller_update_data = AdminResellerUpdateRequest(status=status_in.status)
    updated_reseller = crud_reseller.update(
        db, db_obj=reseller, obj_in=reseller_update_data.model_dump(exclude_unset=True)
    )
    return updated_reseller


@router_resellers.get(
    "/withdrawals",
    response_model=List[WithdrawalRecordResponse],
    summary="管理员获取提现申请列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_get_withdrawal_requests(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    status_filter: Optional[models.WithdrawalStatus] = Query(
        None, description="按提现状态筛选"
    ),
    reseller_user_id_filter: Optional[int] = Query(
        None, description="按代理用户ID筛选"
    ),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    filters = {}
    if status_filter:
        filters["status"] = status_filter.value
    if reseller_user_id_filter is not None:
        filters["reseller_user_id"] = reseller_user_id_filter

    withdrawals = crud_withdrawal_record.get_multi(
        db, skip=skip, limit=limit, filters=filters
    )
    return withdrawals


@router_resellers.patch(
    "/withdrawals/{withdrawal_id}/process",
    response_model=WithdrawalRecordResponse,
    summary="管理员处理提现申请",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_process_withdrawal_request(
    withdrawal_id: int = Path(..., description="要处理的提现申请ID", ge=1),
    process_data: WithdrawalProcessRequest = Body(...),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
):
    withdrawal_request = crud_withdrawal_record.get(db, id=withdrawal_id)
    if not withdrawal_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="未找到指定的提现申请。"
        )

    if withdrawal_request.status not in [models.WithdrawalStatus.pending]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"此提现申请状态为 {withdrawal_request.status.value}，无法处理。",
        )

    reseller_profile = crud_reseller.get_by_user_id(
        db, user_id=withdrawal_request.reseller_user_id
    )
    if not reseller_profile:
        crud_withdrawal_record.update_status(
            db,
            db_obj=withdrawal_request,
            status=models.WithdrawalStatus.failed,
            remarks=f"处理失败：未找到关联的代理ID {withdrawal_request.reseller_user_id}。管理员(ID:{current_admin.id})。原备注: {process_data.remarks or ''}",
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到提现申请关联的代理信息。提现已标记失败。",
        )

    if process_data.status == models.WithdrawalStatus.approved:
        if reseller_profile.balance < withdrawal_request.amount:
            crud_withdrawal_record.update_status(
                db,
                db_obj=withdrawal_request,
                status=models.WithdrawalStatus.failed,
                remarks=f"管理员(ID:{current_admin.id})尝试批准但余额不足({reseller_profile.balance} < {withdrawal_request.amount})。原备注: {process_data.remarks or ''}",
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="代理余额不足以完成此次提现。提现申请已标记为失败。",
            )

        new_balance = reseller_profile.balance - withdrawal_request.amount
        crud_reseller.update(
            db, db_obj=reseller_profile, obj_in={"balance": new_balance}
        )

        updated_withdrawal = crud_withdrawal_record.update_status(
            db,
            db_obj=withdrawal_request,
            status=models.WithdrawalStatus.approved,
            remarks=f"管理员(ID:{current_admin.id})批准。备注: {process_data.remarks or ''}",
        )
    elif process_data.status == models.WithdrawalStatus.rejected:
        updated_withdrawal = crud_withdrawal_record.update_status(
            db,
            db_obj=withdrawal_request,
            status=models.WithdrawalStatus.rejected,
            remarks=f"管理员(ID:{current_admin.id})拒绝。备注: {process_data.remarks or ''}",
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的处理状态。只能是 'approved' 或 'rejected'。",
        )

    return updated_withdrawal
