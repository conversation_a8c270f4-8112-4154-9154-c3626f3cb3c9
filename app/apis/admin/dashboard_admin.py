# app/apis/admin/dashboard_admin.py
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app import models, crud
from app.apis import deps as api_deps
from app.schemas.dashboard import (
    DashboardOverviewResponse,
    UserStatisticsResponse,
    RevenueStatisticsResponse,
    ScoreStatisticsResponse,
    TicketStatisticsResponse,
    RecentActivitiesResponse,
)

router = APIRouter()


@router.get(
    "/overview",
    response_model=DashboardOverviewResponse,
    summary="管理员仪表盘概览",
    description="获取系统整体概览数据，包括用户、收入、谱曲、工单等关键指标",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_dashboard_overview(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数，默认30天"),
) -> Any:
    """
    获取管理员仪表盘概览数据
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # 用户统计
    total_users = db.query(models.User).filter(
        models.User.deleted_at.is_(None)
    ).count()
    
    new_users_count = db.query(models.User).filter(
        and_(
            models.User.created_at >= start_date,
            models.User.deleted_at.is_(None)
        )
    ).count()
    
    active_users_count = db.query(models.User).filter(
        and_(
            models.User.last_login_at >= start_date,
            models.User.deleted_at.is_(None)
        )
    ).count()
    
    # 代理统计
    total_resellers = db.query(models.Reseller).filter(
        models.Reseller.status == models.ResellerStatus.active
    ).count()
    
    # 卡密统计
    total_license_keys = db.query(models.LicenseKey).count()
    used_license_keys = db.query(models.LicenseKey).filter(
        models.LicenseKey.status == models.LicenseKeyStatus.used
    ).count()
    
    new_activations = db.query(models.LicenseKey).filter(
        and_(
            models.LicenseKey.activated_at >= start_date,
            models.LicenseKey.status == models.LicenseKeyStatus.used
        )
    ).count()
    
    # 谱曲统计
    total_scores = db.query(models.Score).filter(
        models.Score.status == models.ScoreStatus.published
    ).count()
    
    new_scores = db.query(models.Score).filter(
        and_(
            models.Score.created_at >= start_date,
            models.Score.status == models.ScoreStatus.published
        )
    ).count()
    
    # 工单统计
    total_tickets = db.query(models.Ticket).count()
    pending_tickets = db.query(models.Ticket).filter(
        models.Ticket.status.in_([
            models.TicketStatus.open,
            models.TicketStatus.in_progress
        ])
    ).count()
    
    new_tickets = db.query(models.Ticket).filter(
        models.Ticket.created_at >= start_date
    ).count()
    
    # 收入统计（基于卡密激活）
    revenue_query = db.query(
        func.sum(models.LicenseType.price).label('total_revenue')
    ).join(
        models.LicenseKey, models.LicenseKey.license_type_id == models.LicenseType.id
    ).filter(
        and_(
            models.LicenseKey.activated_at >= start_date,
            models.LicenseKey.status == models.LicenseKeyStatus.used
        )
    )
    
    period_revenue = revenue_query.scalar() or 0
    
    # 佣金统计
    total_commissions = db.query(
        func.sum(models.CommissionRecord.commission_amount)
    ).filter(
        models.CommissionRecord.created_at >= start_date
    ).scalar() or 0
    
    pending_withdrawals = db.query(
        func.sum(models.WithdrawalRecord.amount)
    ).filter(
        models.WithdrawalRecord.status == models.WithdrawalStatus.pending
    ).scalar() or 0
    
    return DashboardOverviewResponse(
        users={
            "total": total_users,
            "new_in_period": new_users_count,
            "active_in_period": active_users_count,
        },
        resellers={
            "total": total_resellers,
        },
        license_keys={
            "total": total_license_keys,
            "used": used_license_keys,
            "new_activations": new_activations,
            "activation_rate": round((used_license_keys / total_license_keys * 100) if total_license_keys > 0 else 0, 2)
        },
        scores={
            "total": total_scores,
            "new_in_period": new_scores,
        },
        tickets={
            "total": total_tickets,
            "pending": pending_tickets,
            "new_in_period": new_tickets,
        },
        revenue={
            "period_revenue": float(period_revenue),
            "total_commissions": float(total_commissions),
            "pending_withdrawals": float(pending_withdrawals),
        },
        period_days=days
    )


@router.get(
    "/user-statistics",
    response_model=UserStatisticsResponse,
    summary="用户统计数据",
    description="获取详细的用户统计数据，包括注册趋势、活跃度等",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_user_statistics(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> Any:
    """
    获取用户统计数据
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # 按角色统计用户数量
    role_stats = db.query(
        models.User.role,
        func.count(models.User.id).label('count')
    ).filter(
        models.User.deleted_at.is_(None)
    ).group_by(models.User.role).all()
    
    # 按状态统计用户数量
    status_stats = db.query(
        models.User.status,
        func.count(models.User.id).label('count')
    ).filter(
        models.User.deleted_at.is_(None)
    ).group_by(models.User.status).all()
    
    # 按创建来源统计
    source_stats = db.query(
        models.User.creation_source,
        func.count(models.User.id).label('count')
    ).filter(
        models.User.deleted_at.is_(None)
    ).group_by(models.User.creation_source).all()
    
    # 每日注册趋势（最近30天）
    daily_registrations = []
    for i in range(min(days, 30)):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)
        
        count = db.query(models.User).filter(
            and_(
                models.User.created_at >= day_start,
                models.User.created_at < day_end,
                models.User.deleted_at.is_(None)
            )
        ).count()
        
        daily_registrations.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "count": count
        })
    
    return UserStatisticsResponse(
        role_distribution={str(role): count for role, count in role_stats},
        status_distribution={str(status): count for status, count in status_stats},
        source_distribution={str(source): count for source, count in source_stats},
        daily_registrations=daily_registrations,
        period_days=days
    )


@router.get(
    "/revenue-statistics",
    response_model=RevenueStatisticsResponse,
    summary="收入统计数据",
    description="获取详细的收入统计数据，包括卡密销售、佣金等",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_revenue_statistics(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> Any:
    """
    获取收入统计数据
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # 按卡密类型统计收入
    license_type_revenue = db.query(
        models.LicenseType.name,
        models.LicenseType.price,
        func.count(models.LicenseKey.id).label('activations'),
        (models.LicenseType.price * func.count(models.LicenseKey.id)).label('total_revenue')
    ).join(
        models.LicenseKey, models.LicenseKey.license_type_id == models.LicenseType.id
    ).filter(
        and_(
            models.LicenseKey.activated_at >= start_date,
            models.LicenseKey.status == models.LicenseKeyStatus.used
        )
    ).group_by(models.LicenseType.id, models.LicenseType.name, models.LicenseType.price).all()
    
    # 每日收入趋势
    daily_revenue = []
    for i in range(min(days, 30)):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)
        
        revenue = db.query(
            func.sum(models.LicenseType.price)
        ).join(
            models.LicenseKey, models.LicenseKey.license_type_id == models.LicenseType.id
        ).filter(
            and_(
                models.LicenseKey.activated_at >= day_start,
                models.LicenseKey.activated_at < day_end,
                models.LicenseKey.status == models.LicenseKeyStatus.used
            )
        ).scalar() or 0
        
        daily_revenue.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "revenue": float(revenue)
        })
    
    # 佣金统计
    commission_stats = db.query(
        models.CommissionRecord.commission_type,
        func.sum(models.CommissionRecord.commission_amount).label('total'),
        func.count(models.CommissionRecord.id).label('count')
    ).filter(
        models.CommissionRecord.created_at >= start_date
    ).group_by(models.CommissionRecord.commission_type).all()
    
    return RevenueStatisticsResponse(
        license_type_revenue=[
            {
                "license_type": name,
                "unit_price": float(price),
                "activations": activations,
                "total_revenue": float(total_revenue)
            }
            for name, price, activations, total_revenue in license_type_revenue
        ],
        daily_revenue=daily_revenue,
        commission_statistics={
            str(comm_type): {"total": float(total), "count": count}
            for comm_type, total, count in commission_stats
        },
        period_days=days
    )


@router.get(
    "/score-statistics",
    response_model=ScoreStatisticsResponse,
    summary="谱曲统计数据",
    description="获取详细的谱曲统计数据，包括分类分布、难度分布等",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_score_statistics(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> Any:
    """
    获取谱曲统计数据
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # 按分类统计
    category_stats = db.query(
        models.Category.name,
        func.count(models.Score.id).label('count')
    ).join(
        models.Score, models.Score.category_id == models.Category.id
    ).filter(
        models.Score.status == models.ScoreStatus.published
    ).group_by(models.Category.id, models.Category.name).all()

    # 按难度统计
    difficulty_stats = db.query(
        models.Score.difficulty,
        func.count(models.Score.id).label('count')
    ).filter(
        models.Score.status == models.ScoreStatus.published
    ).group_by(models.Score.difficulty).all()

    # 按状态统计
    status_stats = db.query(
        models.Score.status,
        func.count(models.Score.id).label('count')
    ).group_by(models.Score.status).all()

    # 每日上传趋势
    daily_uploads = []
    for i in range(min(days, 30)):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)

        count = db.query(models.Score).filter(
            and_(
                models.Score.created_at >= day_start,
                models.Score.created_at < day_end,
                models.Score.status == models.ScoreStatus.published
            )
        ).count()

        daily_uploads.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "count": count
        })

    # 评分最高的谱曲（前10）
    top_rated_scores = db.query(
        models.Score.id,
        models.Score.title,
        func.avg(models.UserScoreRating.rating).label('avg_rating'),
        func.count(models.UserScoreRating.id).label('rating_count')
    ).join(
        models.UserScoreRating, models.UserScoreRating.score_id == models.Score.id
    ).filter(
        models.Score.status == models.ScoreStatus.published
    ).group_by(
        models.Score.id, models.Score.title
    ).having(
        func.count(models.UserScoreRating.id) >= 5  # 至少5个评分
    ).order_by(
        func.avg(models.UserScoreRating.rating).desc()
    ).limit(10).all()

    # 收藏最多的谱曲（前10）
    most_favorited_scores = db.query(
        models.Score.id,
        models.Score.title,
        func.count(models.UserFavorite.id).label('favorite_count')
    ).join(
        models.UserFavorite, models.UserFavorite.score_id == models.Score.id
    ).filter(
        models.Score.status == models.ScoreStatus.published
    ).group_by(
        models.Score.id, models.Score.title
    ).order_by(
        func.count(models.UserFavorite.id).desc()
    ).limit(10).all()

    return ScoreStatisticsResponse(
        category_distribution={name: count for name, count in category_stats},
        difficulty_distribution={str(difficulty): count for difficulty, count in difficulty_stats},
        status_distribution={str(status): count for status, count in status_stats},
        daily_uploads=daily_uploads,
        top_rated_scores=[
            {
                "id": score_id,
                "title": title,
                "avg_rating": float(avg_rating),
                "rating_count": rating_count
            }
            for score_id, title, avg_rating, rating_count in top_rated_scores
        ],
        most_favorited_scores=[
            {
                "id": score_id,
                "title": title,
                "favorite_count": favorite_count
            }
            for score_id, title, favorite_count in most_favorited_scores
        ],
        period_days=days
    )


@router.get(
    "/ticket-statistics",
    response_model=TicketStatisticsResponse,
    summary="工单统计数据",
    description="获取详细的工单统计数据，包括状态分布、响应时间等",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_ticket_statistics(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> Any:
    """
    获取工单统计数据
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # 按状态统计
    status_stats = db.query(
        models.Ticket.status,
        func.count(models.Ticket.id).label('count')
    ).group_by(models.Ticket.status).all()

    # 按优先级统计
    priority_stats = db.query(
        models.Ticket.priority,
        func.count(models.Ticket.id).label('count')
    ).group_by(models.Ticket.priority).all()

    # 按分类统计
    category_stats = db.query(
        models.Ticket.category,
        func.count(models.Ticket.id).label('count')
    ).filter(
        models.Ticket.category.isnot(None)
    ).group_by(models.Ticket.category).all()

    # 每日工单趋势
    daily_tickets = []
    for i in range(min(days, 30)):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)

        count = db.query(models.Ticket).filter(
            and_(
                models.Ticket.created_at >= day_start,
                models.Ticket.created_at < day_end
            )
        ).count()

        daily_tickets.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "count": count
        })

    # 计算平均响应时间和解决时间
    # 这里简化处理，实际应该从工单消息表计算
    avg_response_time = 24.0  # 默认24小时
    avg_resolution_time = 72.0  # 默认72小时

    return TicketStatisticsResponse(
        status_distribution={str(status): count for status, count in status_stats},
        priority_distribution={str(priority): count for priority, count in priority_stats},
        category_distribution={category: count for category, count in category_stats},
        daily_tickets=daily_tickets,
        avg_response_time=avg_response_time,
        avg_resolution_time=avg_resolution_time,
        period_days=days
    )


@router.get(
    "/recent-activities",
    response_model=RecentActivitiesResponse,
    summary="最近活动",
    description="获取系统最近的活动记录",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_recent_activities(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(50, ge=1, le=200, description="返回记录数"),
) -> Any:
    """
    获取最近的系统活动
    """
    activities = []

    # 最近的用户注册
    recent_users = db.query(models.User).filter(
        models.User.deleted_at.is_(None)
    ).order_by(models.User.created_at.desc()).limit(10).all()

    for user in recent_users:
        activities.append({
            "id": user.id,
            "type": "user_registration",
            "title": "新用户注册",
            "description": f"用户 {user.username} 注册了账户",
            "user_id": user.id,
            "username": user.username,
            "created_at": user.created_at,
            "metadata": {"role": str(user.role)}
        })

    # 最近的卡密激活
    recent_activations = db.query(models.LicenseKey).filter(
        and_(
            models.LicenseKey.status == models.LicenseKeyStatus.used,
            models.LicenseKey.activated_at.isnot(None)
        )
    ).order_by(models.LicenseKey.activated_at.desc()).limit(10).all()

    for license_key in recent_activations:
        activities.append({
            "id": license_key.id,
            "type": "license_activation",
            "title": "卡密激活",
            "description": f"卡密 {license_key.key_string} 被激活",
            "user_id": license_key.user_id,
            "username": license_key.user.username if license_key.user else None,
            "created_at": license_key.activated_at,
            "metadata": {"license_type": license_key.license_type.name if license_key.license_type else None}
        })

    # 最近的工单
    recent_tickets = db.query(models.Ticket).order_by(
        models.Ticket.created_at.desc()
    ).limit(10).all()

    for ticket in recent_tickets:
        activities.append({
            "id": ticket.id,
            "type": "ticket_created",
            "title": "新工单",
            "description": f"工单 #{ticket.id}: {ticket.title}",
            "user_id": ticket.user_id,
            "username": ticket.user.username if ticket.user else None,
            "created_at": ticket.created_at,
            "metadata": {"priority": str(ticket.priority), "status": str(ticket.status)}
        })

    # 按时间排序并限制数量
    activities.sort(key=lambda x: x["created_at"], reverse=True)
    activities = activities[:limit]

    return RecentActivitiesResponse(
        activities=activities,
        total_count=len(activities)
    )
