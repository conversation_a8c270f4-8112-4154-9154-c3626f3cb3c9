# TanZouDaShiAPI/app/apis/admin/users_admin.py
from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud
from app.schemas.user import (
    PaginatedResponse,
    UserResponse,
    AdminUserCreate,
    AdminUserUpdate,
)
from app.schemas.msg import Msg

from app.apis import deps as api_deps
from app import models  # MODIFIED: Added unified models import
# MODIFIED: Removed specific import from app.db.models.user

router = APIRouter()


# 中文注释：获取用户列表
@router.get(
    "/",
    response_model=PaginatedResponse[UserResponse],
    summary="管理员获取用户列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_users(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页的最大记录数"),
    search: Optional[str] = Query(None, description="按用户名或邮箱搜索"),
    role: Optional[models.UserRole] = Query(
        None, description="按用户角色筛选"
    ),  # MODIFIED: UserRole to models.UserRole
    status: Optional[models.UserStatus] = Query(
        None, description="按账户状态筛选"
    ),  # MODIFIED: UserStatus to models.UserStatus
) -> Any:
    """
    管理员获取用户列表，支持分页和筛选。
    - **skip**: 跳过的记录数，用于分页。
    - **limit**: 返回的最大记录数，用于分页。
    - **search**: 按用户名或邮箱进行模糊搜索。
    - **role**: 按用户角色精确筛选。
    - **status**: 按账户状态精确筛选。
    """
    users_db = crud.user.get_multi_by_filters(
        db, skip=skip, limit=limit, search=search, role=role, status=status
    )
    total_users = crud.user.count_by_filters(
        db, search=search, role=role, status=status
    )
    return PaginatedResponse(
        total=total_users,
        items=users_db,
        page=(skip // limit) + 1 if limit > 0 else 1,
        limit=limit,
    )


# 中文注释：管理员创建用户
@router.post(
    "/",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="管理员创建新用户",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def create_user_by_admin(
    *,
    db: Session = Depends(api_deps.get_db),
    user_in: AdminUserCreate,
) -> Any:
    """
    管理员创建新用户。
    允许设置用户名、邮箱、手机号、密码、角色、状态等。
    """
    # 检查用户名是否已存在
    existing_user_by_username = crud.user.get_user_by_username(
        db, username=user_in.username
    )
    if existing_user_by_username:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="用户名已存在。",
        )
    # 检查邮箱是否已存在 (如果提供了邮箱)
    if user_in.email:
        existing_user_by_email = crud.user.get_user_by_email(db, email=user_in.email)
        if existing_user_by_email:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="电子邮箱已存在。",
            )
    # 更多唯一性检查可以按需添加，例如手机号

    user = crud.user.create_user_by_admin(db=db, obj_in=user_in)
    return user


# 中文注释：获取特定用户信息
@router.get(
    "/{user_id}",
    response_model=UserResponse,
    summary="管理员获取特定用户信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_user_by_admin(
    user_id: int,
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员获取指定 ID 的用户信息。
    """
    user = crud.user.get(db, id=user_id)
    if not user or user.deleted_at is not None:  # 检查用户是否存在且未被软删除
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户未找到")
    return user


# 中文注释：管理员更新用户信息
@router.put(
    "/{user_id}",
    response_model=UserResponse,
    summary="管理员更新用户信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_user_by_admin(
    *,
    db: Session = Depends(api_deps.get_db),
    user_id: int,
    user_in: AdminUserUpdate,
) -> Any:
    """
    管理员更新指定 ID 的用户信息。
    允许修改用户名、邮箱、角色、状态等。
    """
    db_user = crud.user.get(db, id=user_id)
    if not db_user or db_user.deleted_at is not None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户未找到",
        )

    # 唯一性检查：如果尝试更新用户名或邮箱，需要检查新值是否已被其他用户占用
    if user_in.username and user_in.username != db_user.username:
        existing_user = crud.user.get_user_by_username(db, username=user_in.username)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="新用户名已被其他用户使用"
            )

    if user_in.email and user_in.email != db_user.email:
        existing_user = crud.user.get_user_by_email(db, email=user_in.email)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="新邮箱已被其他用户使用"
            )

    user = crud.user.update_user_by_admin(db=db, db_obj=db_user, obj_in=user_in)
    return user


# 中文注释：管理员逻辑删除用户
@router.delete(
    "/{user_id}",
    response_model=Msg,
    summary="管理员逻辑删除用户",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    status_code=status.HTTP_200_OK,
)
def delete_user_by_admin(
    *,
    db: Session = Depends(api_deps.get_db),
    user_id: int,
) -> Any:
    """
    管理员逻辑删除指定 ID 的用户。
    实际操作是将用户状态标记为不活跃 (inactive) 并记录删除时间。
    """
    user = crud.user.get(db, id=user_id)
    if not user:  # 即使用户已被软删除 (deleted_at is not None), get仍能获取到，所以主要判断是否存在
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户未找到")
    if user.deleted_at is not None:  # 如果用户已经被软删除了
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户已被删除"
        )

    # 防止管理员删除自己
    # current_superuser: User = Depends(deps.get_current_active_superuser) # 不能在函数内部这样用Depends
    # 实际的 current_superuser 需要从依赖中获取，但这里我们不直接比较，因为依赖已经保护了接口
    # if user.id == current_superuser.id: # 假设能拿到 current_superuser
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="不能删除自己")
    # 此检查可以在更高级别或通过get_current_active_superuser的逻辑来处理，如果它返回的是当前操作者

    crud.user.deactivate_user(db=db, db_obj=user)
    return Msg(msg="用户已成功删除 (逻辑删除)")
