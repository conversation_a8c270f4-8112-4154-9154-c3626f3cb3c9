# TanZouDaShiAPI/app/apis/admin/scores_admin.py
from typing import Any, List, Optional
from datetime import datetime, timezone  # 新增导入

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app import crud, models  # schemas 将被单独导入

# 修改导入方式
from app.schemas.score import (
    ScorePaginatedResponse,
    PageMeta,
    ScoreDetailResponse,
    AdminScoreUpdate,
    ScoreStatusUpdate,
    ScoreResponse,  # ScoreResponse 用于 /admin/scores/{score_id} PUT 的响应
)
from app.schemas.msg import Msg

from app.apis import deps as api_deps
# MODIFIED: Removed specific model imports, will use app.models

router = APIRouter()


@router.get(
    "/",
    response_model=ScorePaginatedResponse,  # 直接使用
    summary="管理员获取谱曲列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_scores_admin(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=200, description="每页记录数"),
    sort_by: Optional[str] = Query(
        None, description="排序字段 (例如: created_at, title)"
    ),
    order: Optional[str] = Query("desc", description="排序顺序 (asc 或 desc)"),
    title: Optional[str] = Query(None, description="按标题模糊搜索"),
    uploader_user_id: Optional[int] = Query(None, description="按上传者用户ID筛选"),
    category_id: Optional[int] = Query(None, description="按分类ID筛选"),
    status: Optional[models.ScoreStatus] = Query(
        None, description="按谱曲状态筛选"
    ),  # MODIFIED: ScoreStatus to models.ScoreStatus
    score_type: Optional[models.ScoreType] = Query(
        None, description="按谱曲类型筛选"
    ),  # MODIFIED: ScoreType to models.ScoreType
    difficulty: Optional[models.ScoreDifficulty] = Query(
        None, description="按难度筛选"
    ),  # MODIFIED: ScoreDifficulty to models.ScoreDifficulty
    is_premium_only: Optional[bool] = Query(None, description="是否仅限付费用户筛选"),
    tags: Optional[List[str]] = Query(
        None, description="按标签筛选 (逗号分隔或多次指定)"
    ),  # FastAPI 会自动处理 Query(None) 为 list
) -> Any:
    """
    管理员获取谱曲列表，支持分页、排序和多种筛选条件。
    默认显示所有状态的谱曲。
    """
    scores, total_count = crud.score.get_multi_with_filtering_and_sorting(
        db,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        order=order,
        category_id=category_id,
        uploader_user_id=uploader_user_id,
        search_keyword=title,
        score_type=score_type,
        difficulty=difficulty,
        status=status,
        is_premium_only=is_premium_only,
        tags_list=tags,
    )

    page_meta = PageMeta(  # 直接使用
        page=(skip // limit) + 1,
        limit=limit,
        total_items=total_count,
        total_pages=(total_count + limit - 1) // limit if limit > 0 else 0,
    )
    return ScorePaginatedResponse(meta=page_meta, data=scores)  # 直接使用


@router.get(
    "/{score_id}",
    response_model=ScoreDetailResponse,  # 直接使用
    summary="管理员获取特定谱曲详情",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_score_admin(
    score_id: int = Path(..., description="要获取详情的谱曲ID", ge=1),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员获取特定谱曲的详细信息。
    可以查看所有状态的谱曲详情。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=404, detail="谱曲未找到")
    return score


@router.put(
    "/{score_id}",
    response_model=ScoreResponse,  # 使用 ScoreResponse
    summary="管理员更新谱曲信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_score_admin(
    score_id: int = Path(..., description="要更新的谱曲ID", ge=1),
    *,
    score_in: AdminScoreUpdate,  # 直接使用
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # 获取当前管理员
) -> Any:
    """
    管理员更新谱曲的任何信息，包括标题、分类、内容、状态等。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=404, detail="谱曲未找到")

    # 如果 score_in 中包含 status，并且 score.status 将要改变
    # 并且 approved_by_user_id 和 approved_at 需要根据状态变化更新
    # 这里的逻辑是：如果 AdminScoreUpdate 中包含了 status 字段，
    # 并且这个 status 是一个会导致审核信息（如 approved_by, approved_at）变化的，
    # 那么应该考虑这些字段的更新。
    # 然而，通常状态的审核和其伴随的备注、审核人、审核时间是通过 PATCH /status 接口处理的。
    # PUT 接口主要用于更新谱曲内容本身。
    # 如果 PUT 接口也允许修改 status，那么需要明确如何处理审核相关的字段。
    # 在当前的 CRUD update 方法中，它会更新所有传入的字段。
    # 如果 AdminScoreUpdate.status 被设置，它将被直接更新。
    # 如果希望在 PUT 更新 status 时也自动记录审核信息，可以在 CRUD.update 中添加逻辑，
    # 或者在这里调用一个类似 update_status 的辅助方法。
    # 为了保持职责分离，通常 PUT 更新内容，PATCH 更新状态和审核信息。
    # 此处我们依赖 CRUD.update 的行为，它会更新 score_in 中提供的所有字段。

    updated_score = crud.score.update(db=db, db_obj=score, obj_in=score_in)
    return updated_score


@router.patch(
    "/{score_id}/status",
    response_model=ScoreResponse,  # 使用 ScoreResponse
    summary="管理员审核/更新谱曲状态",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_score_status_admin(
    score_id: int = Path(..., description="要更新状态的谱曲ID", ge=1),
    *,
    status_in: ScoreStatusUpdate,  # 直接使用
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # 注入当前管理员
) -> Any:
    """
    管理员审核或更新谱曲的状态。
    同时记录审核操作的管理员ID、审核时间以及审核备注。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=404, detail="谱曲未找到")

    # 调用 crud.score.update_status 方法，传入 status_in 和 current_admin.id
    updated_score = crud.score.update_status(
        db=db, db_obj=score, status_in=status_in, approver_id=current_admin.id
    )

    # 从 v1 迁移过来的逻辑：手动设置 approved_at
    # 假设 crud.score.update_status 本身不处理这个字段的提交
    # 或者即使处理了，这里的显式设置会覆盖它（取决于模型和CRUD实现）
    if (
        status_in.status == models.ScoreStatus.approved
        or status_in.status == models.ScoreStatus.rejected
    ):
        if updated_score:  # 确保 updated_score 不是 None
            updated_score.approved_at = datetime.now(timezone.utc)
            db.add(updated_score)  # 标记对象已更改
            db.commit()  # 提交更改到数据库
            db.refresh(updated_score)  # 刷新对象以获取最新状态

    return updated_score


@router.delete(
    "/{score_id}",
    response_model=Msg,  # 直接使用
    summary="管理员删除谱曲",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    status_code=200,  # 也可以是 204 No Content，但 Msg 需要 200
)
def delete_score_admin(
    score_id: int = Path(..., description="要删除的谱曲ID", ge=1),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员删除谱曲。
    可以是物理删除或逻辑删除（例如，标记为 'deleted' 状态）。
    当前实现为物理删除。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=404, detail="谱曲未找到")

    # 考虑是否需要检查谱曲是否可以被删除（例如，是否有依赖）

    crud.score.remove(db=db, id=score_id)
    return Msg(message="谱曲已成功删除")  # 直接使用
