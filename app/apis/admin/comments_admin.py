# TanZouDaShiAPI/app/apis/admin/comments_admin.py
from typing import Optional, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session

from app.schemas.user_interaction import (
    PaginatedAdminScoreCommentResponse,
    AdminScoreCommentResponse,
    ScoreCommentStatusUpdate,
    ScoreCommentUpdate,  # 用于 update_comment_status_admin
)
from app.schemas.msg import Msg

from app import crud, models  # models 是 app.db.models
from app.apis import deps as api_deps

router = APIRouter()


@router.get(
    "/",
    response_model=PaginatedAdminScoreCommentResponse,
    summary="管理员获取评论列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_comments_admin(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    score_id: Optional[int] = Query(None, description="按谱曲ID筛选"),
    user_id: Optional[int] = Query(None, description="按用户ID筛选"),
    content_keyword: Optional[str] = Query(None, description="按评论内容模糊搜索"),
    start_time: Optional[datetime] = Query(
        None, description="评论时间范围开始 (ISO 8601)"
    ),
    end_time: Optional[datetime] = Query(
        None, description="评论时间范围结束 (ISO 8601)"
    ),
    comment_status: Optional[models.ScoreCommentStatus] = Query(
        None, description="按评论状态筛选"
    ),
    username_keyword: Optional[str] = Query(None, description="按用户名模糊搜索"),
    score_title_keyword: Optional[str] = Query(None, description="按谱曲标题模糊搜索"),
) -> Any:
    """
    管理员获取评论列表，支持分页和多种筛选条件。
    - **skip**: 跳过的记录数
    - **limit**: 每页记录数
    - **score_id**: 筛选特定谱曲的评论
    - **user_id**: 筛选特定用户的评论
    - **content_keyword**: 评论内容包含的关键词
    - **start_time**: 评论创建时间的开始点
    - **end_time**: 评论创建时间的结束点
    - **comment_status**: 评论的状态 (visible, hidden, pending_review)
    - **username_keyword**: 发布评论的用户名包含的关键词
    - **score_title_keyword**: 评论所属谱曲标题包含的关键词
    """
    comments, total = crud.score_comment.get_multi_for_admin(
        db,
        skip=skip,
        limit=limit,
        score_id=score_id,
        user_id=user_id,
        content_keyword=content_keyword,
        start_time=start_time,
        end_time=end_time,
        status=comment_status,
        username_keyword=username_keyword,
        score_title_keyword=score_title_keyword,
    )
    return PaginatedAdminScoreCommentResponse(
        total=total,
        items=comments,
        page=(skip // limit) + 1 if limit > 0 else 1,
        limit=limit,
    )


@router.get(
    "/{comment_id}",
    response_model=AdminScoreCommentResponse,
    summary="管理员获取特定评论详情",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_comment_admin(
    comment_id: int = Path(..., description="要获取详情的评论ID"),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员获取指定ID的评论详细信息。
    会预加载评论者和关联谱曲的信息。
    """
    db_comment = crud.score_comment.get(db, id=comment_id)
    if not db_comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论未找到")
    return db_comment


@router.delete(
    "/{comment_id}",
    response_model=Msg,
    summary="管理员删除评论",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    status_code=status.HTTP_200_OK,
)
def delete_comment_admin(
    comment_id: int = Path(..., description="要删除的评论ID"),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员删除指定ID的评论。
    这将物理删除评论及其所有子回复（如果存在）。
    """
    db_comment = crud.score_comment.get(db, id=comment_id)
    if not db_comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论未找到")

    crud.score_comment.remove(db, id=comment_id)
    return Msg(message="评论已成功删除")


@router.patch(
    "/{comment_id}/status",
    response_model=AdminScoreCommentResponse,
    summary="管理员更新评论状态",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_comment_status_admin(
    comment_id: int = Path(..., description="要更新状态的评论ID"),
    status_update: ScoreCommentStatusUpdate = Body(..., description="新的评论状态"),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    管理员更新指定评论的状态 (e.g., visible, hidden, pending_review)。
    """
    db_comment = crud.score_comment.get(db, id=comment_id)
    if not db_comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论未找到")

    update_data = ScoreCommentUpdate(status=status_update.status)

    updated_comment = crud.score_comment.update(
        db, db_obj=db_comment, obj_in=update_data
    )
    return updated_comment
