# TanZouDaShiAPI/app/apis/admin/categories_admin.py
from typing import List, Optional, Any  # Added Any

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    Path,
    status,
)  # Added status
from sqlalchemy.orm import Session

from app.schemas.category import (
    CategoryResponse,
    CategoryCreate,
    CategoryDetailResponse,
    CategoryUpdate,
)  # Removed CategoryType

from app.crud import (
    crud_category,
)  # Ensured direct import for consistency if needed, or use existing

# from app.crud.crud_category import crud_category # Original import
from app.apis import deps as api_deps
from app import models

router = APIRouter()


@router.post(
    "/",
    response_model=CategoryDetailResponse,  # MODIFIED from CategoryResponse
    status_code=status.HTTP_201_CREATED,  # ADDED status_code
    summary="[Admin] 创建新分类",  # MODIFIED summary
    description="创建一个新的谱曲分类。需要管理员权限。",  # ADDED description
)
def create_category(  # Function name kept, signature updated
    *,
    db: Session = Depends(api_deps.get_db),
    category_in: CategoryCreate,
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # Kept current_admin for consistency
) -> Any:  # MODIFIED from CategoryResponse to Any to match v1
    """
    [Admin] 创建新分类。

    - **name**: 分类名称。
    - **slug**: URL友好型名称，必须唯一。
    - **parent_category_id**: 父分类ID，可选。
    - **description**: 分类描述，可选。
    - **icon_url**: 分类图标URL，可选。
    - **sort_order**: 排序值，默认为0。
    - **is_active**: 是否激活，默认为True。
    """
    # 检查 slug 是否已存在
    existing_slug_category = crud_category.get_by_slug(db, slug=category_in.slug)
    if existing_slug_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED status code
            detail=f"分类 slug '{category_in.slug}' 已存在。",
        )
    # 检查同父分类下名称是否已存在
    existing_name_category = crud_category.get_by_name(
        db, name=category_in.name, parent_category_id=category_in.parent_category_id
    )
    if existing_name_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED status code
            detail=f"在同一父分类下，分类名称 '{category_in.name}' 已存在。",
        )

    if category_in.parent_category_id:
        parent_category = crud_category.get(
            db, id=category_in.parent_category_id
        )  # MODIFIED to crud_category
        if not parent_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,  # MODIFIED status code
                detail=f"父分类 ID '{category_in.parent_category_id}' 不存在。",
            )

    category = crud_category.create(
        db=db, obj_in=category_in
    )  # MODIFIED to crud_category
    return category


@router.get(
    "/",
    response_model=List[CategoryResponse],
    summary="获取分类列表 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_categories(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页的记录数"),
    name: Optional[str] = Query(None, description="按分类名称模糊搜索"),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> List[CategoryResponse]:
    """
    获取分类列表，支持分页和按名称搜索。
    """
    # crud_category.get_multi_with_parent_info 不支持按名称搜索，所以我们在获取后进行过滤
    # 注意：对于大量数据，这种方式效率不高，应在数据库层面进行过滤
    categories = crud_category.get_multi_with_parent_info(db, skip=skip, limit=limit)
    if name:
        categories = [cat for cat in categories if name.lower() in cat.name.lower()]
        # 如果这样过滤后，分页可能不准确，因为是在获取了 limit 数量后再过滤
        # 更优方案是修改 CRUD 层

    # 临时修复：如果按名称搜索，则不使用数据库的 skip/limit，获取较多数据后在应用层分页
    # 这仍然不是最佳方案，但比上面更接近预期行为
    if name:
        all_categories_for_search = crud_category.get_multi_with_parent_info(
            db, skip=0, limit=1000
        )  # 获取更多数据
        filtered_categories = [
            cat for cat in all_categories_for_search if name.lower() in cat.name.lower()
        ]
        return filtered_categories[skip : skip + limit]

    return categories


@router.get(
    "/{category_id}",
    response_model=CategoryDetailResponse,
    summary="获取特定分类详情 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_category(
    *,
    db: Session = Depends(api_deps.get_db),
    category_id: int,
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> CategoryDetailResponse:
    """
    通过ID获取特定分类的详细信息，包括其子分类和父分类。
    """
    category = crud_category.get(db=db, id=category_id)
    if not category:
        raise HTTPException(status_code=404, detail="未找到指定的分类")

    # 手动加载子分类和父分类信息，确保它们在响应中
    # crud_category.get() 默认可能不加载这些，取决于 CRUDBase 实现
    # 如果 CategoryResponse/CategoryDetailResponse 配置了 orm_mode = True, 访问属性时会自动加载（如果关系存在）
    # 为确保，可以显式查询或配置 joinedload/selectinload 在 CRUD 层
    # 对于 CategoryDetailResponse，它期望 children 和 parent_category 字段
    # SQLAlchemy 模型中已经定义了 relationship，Pydantic 的 from_attributes=True (orm_mode) 会处理
    return category


@router.put(
    "/{category_id}",
    response_model=CategoryDetailResponse,  # MODIFIED from CategoryResponse
    summary="[Admin] 更新分类信息",  # MODIFIED summary
    description="通过分类ID更新现有分类的信息。需要管理员权限。",  # ADDED description
)
def update_category(  # Function name kept, signature updated
    *,
    db: Session = Depends(api_deps.get_db),
    category_id: int = Path(
        ..., description="要更新的分类ID", ge=1
    ),  # ADDED Path description
    category_in: CategoryUpdate,
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # Kept current_admin
) -> Any:  # MODIFIED from CategoryResponse to Any
    """
    [Admin] 更新分类信息。

    - **category_id**: 要更新的分类的ID。
    - 请求体中包含要更新的字段，所有字段均为可选。
    """
    category = crud_category.get(db, id=category_id)  # MODIFIED to crud_category
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,  # MODIFIED status code
            detail=f"ID为 '{category_id}' 的分类未找到。",
        )

    # 检查 slug 是否更改，如果更改，确保新 slug 唯一
    if category_in.slug and category_in.slug != category.slug:
        existing_slug_category = crud_category.get_by_slug(
            db, slug=category_in.slug
        )  # MODIFIED
        if existing_slug_category and existing_slug_category.id != category_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED
                detail=f"分类 slug '{category_in.slug}' 已被其他分类使用。",
            )

    # 检查名称是否更改，如果更改，确保在同父分类下唯一
    new_name = category_in.name if category_in.name is not None else category.name
    new_parent_id = (
        category_in.parent_category_id
        if category_in.parent_category_id is not None
        else category.parent_category_id
    )

    if (category_in.name and category_in.name != category.name) or (
        category_in.parent_category_id is not None
        and category_in.parent_category_id != category.parent_category_id
    ):
        existing_name_category = crud_category.get_by_name(  # MODIFIED
            db, name=new_name, parent_category_id=new_parent_id
        )
        if existing_name_category and existing_name_category.id != category_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED
                detail=f"在目标父分类下，分类名称 '{new_name}' 已被其他分类使用。",
            )

    if (
        category_in.parent_category_id is not None
        and category_in.parent_category_id != category.parent_category_id
    ):
        if category_in.parent_category_id == category_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED
                detail="不能将分类的父分类设置为其自身。",
            )
        if category_in.parent_category_id:
            parent_category = crud_category.get(
                db, id=category_in.parent_category_id
            )  # MODIFIED
            if not parent_category:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,  # MODIFIED
                    detail=f"目标父分类 ID '{category_in.parent_category_id}' 不存在。",
                )
            temp_parent = parent_category
            while temp_parent:
                if temp_parent.id == category_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED
                        detail="更新会导致循环依赖：不能将分类的父分类设置为其子分类。",
                    )
                temp_parent = temp_parent.parent_category  # type: ignore

    updated_category = crud_category.update(
        db=db, db_obj=category, obj_in=category_in
    )  # MODIFIED
    return updated_category


@router.delete(
    "/{category_id}",
    status_code=status.HTTP_204_NO_CONTENT,  # MODIFIED status_code, removed response_model
    summary="[Admin] 删除分类",  # MODIFIED summary
    description="通过分类ID删除一个分类。需要管理员权限。如果分类下有子分类或谱曲，可能需要特殊处理。",  # ADDED description
)
def delete_category(  # Function name kept, signature updated
    *,
    db: Session = Depends(api_deps.get_db),
    category_id: int = Path(
        ..., description="要删除的分类ID", ge=1
    ),  # ADDED Path description
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # Kept current_admin
) -> None:  # MODIFIED from Msg to None
    """
    [Admin] 删除一个分类。
    - **category_id**: 要删除的分类的ID。
    - **注意**: 行为基于v1的逻辑，谱曲关联和子分类处理。
    """
    category = crud_category.get(db, id=category_id)  # MODIFIED
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,  # MODIFIED
            detail=f"ID为 '{category_id}' 的分类未找到。",
        )

    if category.scores:  # type: ignore
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,  # MODIFIED
            detail=f"分类 '{category.name}' 下有关联的谱曲，无法直接删除。请先处理这些谱曲。",
        )

    # 子分类处理逻辑 (来自v1):
    # "由于我们 Category 模型的 children relationship 有 cascade="all, delete-orphan"
    # "并且 parent_category_id 外键是 ondelete="SET NULL"
    # "删除父分类时，其直接子分类的 parent_category_id 会被设为 NULL，它们会变成顶级分类。
    # "如果希望删除父分类时也删除所有子分类，则 ondelete 应该是 CASCADE，或者在代码中递归删除。
    # "当前行为是子分类提升为顶级。"
    # The v1 code did not explicitly check for children before removal for this reason.
    # The original admin code *did* check for children:
    # if category_to_delete.children: # type: ignore
    #     raise HTTPException(
    #         status_code=400,
    #         detail="无法删除：该分类下存在子分类。请先删除或移动子分类。",
    #     )
    # Sticking to v1's behavior which relies on DB constraints / ORM behavior for children.

    crud_category.remove(db=db, id=category_id)  # MODIFIED
    return None  # HTTP 204 No Content 不需要返回体
