# app/apis/admin/auth_admin.py
from typing import Any, Optional
from datetime import timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app import models
from app.crud.crud_user import user as crud_user
from app.crud.crud_license import license_key as crud_license_key
from app.schemas.auth import AdminLoginPasswordRequest, AdminResellerLoginResponse
from app.schemas.user import UserResponse
from app.schemas.license import LicenseInfoBase
from app.apis import deps as api_deps
from app.core import security
from app.core.config import settings

router = APIRouter()


@router.post(
    "/login",
    response_model=AdminResellerLoginResponse,
    summary="管理员登录",
    description="管理员用户名/密码登录，无需设备码。",
)
def admin_login(
    db: Session = Depends(api_deps.get_db),
    login_data: AdminLoginPasswordRequest = Body(..., description="管理员登录凭据"),
) -> Any:
    """
    管理员登录接口:
    - **login_type: "password"**: 使用用户名和密码登录。
    - **无需device_id**: 管理员登录不需要设备绑定检查。
    """
    # 用户名密码验证
    user = crud_user.authenticate_user(
        db, username=login_data.username, password=login_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 检查用户状态
    if user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用或未激活",
        )

    # 检查用户角色是否为管理员
    if user.role != models.UserRole.admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户没有管理员权限",
        )

    # 生成访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=str(user.id), expires_delta=access_token_expires
    )

    # 生成刷新令牌
    refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    refresh_token = security.create_access_token(
        subject=f"refresh_{user.id}", expires_delta=refresh_token_expires
    )

    # 构建用户信息响应
    user_info_response = UserResponse.model_validate(user)

    # 获取用户当前激活的卡密信息（如果有）
    active_license_info_response: Optional[LicenseInfoBase] = None
    user_active_license = crud_license_key.get_user_active_license(db, user_id=user.id)
    if user_active_license:
        active_license_info_response = LicenseInfoBase.model_validate(user_active_license)
        if user_active_license.license_type:
            active_license_info_response.license_type_name = (
                user_active_license.license_type.name
            )
            active_license_info_response.license_type_code = (
                user_active_license.license_type.code
            )

    return AdminResellerLoginResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="Bearer",
        expires_in=int(access_token_expires.total_seconds()),
        user_info=user_info_response,
        license_info=active_license_info_response,
    )
