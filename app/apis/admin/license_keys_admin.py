# TanZouDaShiAPI/app/apis/admin/license_keys_admin.py
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.schemas.license import (
    LicenseTypeResponseAdmin,
    LicenseKeyResponseAdmin,
    AdminLicenseKeyBatchCreate,
    AdminLicenseKeyUpdate,
)
from app.schemas.msg import Msg
from app.schemas.user import PaginatedResponse  # PaginatedResponse 来自 user schemas

from app.apis import deps as api_deps  # 导入依赖
from app.crud.crud_license import license_key as crud_license_key  # 导入卡密 CRUD
from app.crud.crud_license import license_type as crud_license_type  # 导入卡密类型 CRUD
from app import models  # MODIFIED: Added unified models import
# MODIFIED: Removed specific UserModel and LicenseKeyStatus imports

router = APIRouter()


# --- 卡密类型管理 (可选，如果需要在此处管理) ---
# 通常卡密类型的管理可能也放在这里，或者有单独的 license_types_admin.py
# 为了演示，这里简单包含一个获取所有卡密类型的接口
@router.get(
    "/types/",
    response_model=List[LicenseTypeResponseAdmin],
    summary="获取所有卡密类型 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_license_types_admin(
    db: Session = Depends(api_deps.get_db),
    skip: int = 0,
    limit: int = 100,
):
    """
    获取所有卡密类型列表。
    需要超级管理员权限。
    """
    types = crud_license_type.get_multi(db, skip=skip, limit=limit)
    return types


# --- 卡密管理 ---


@router.post(
    "/batch-generate/",
    response_model=List[LicenseKeyResponseAdmin],
    status_code=status.HTTP_201_CREATED,
    summary="批量生成卡密 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def batch_generate_license_keys(
    *,
    db: Session = Depends(api_deps.get_db),
    batch_create_in: AdminLicenseKeyBatchCreate,
    current_user: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    批量生成卡密。
    - **license_type_id**: 卡密类型ID (必需)。
    - **quantity**: 生成数量 (必需, 1-1000)。
    - **consignment_reseller_id**: (可选) 分配给某个代理商的用户ID。
    - **notes**: (可选) 备注。
    需要超级管理员权限。
    """
    try:
        created_keys = crud_license_key.create_batch_license_keys(
            db=db, batch_create_input=batch_create_in, issued_by_user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # 对于其他未预料的错误
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成卡密时发生错误: {str(e)}",
        )
    return created_keys


@router.get(
    "/",
    response_model=PaginatedResponse[LicenseKeyResponseAdmin],
    summary="获取卡密列表 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_license_keys_admin(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    key_string: Optional[str] = Query(None, description="按卡密密钥搜索 (模糊匹配)"),
    status: Optional[models.LicenseKeyStatus] = Query(
        None, description="按状态筛选"
    ),  # MODIFIED: LicenseKeyStatus to models.LicenseKeyStatus
    license_type_id: Optional[int] = Query(None, description="按卡密类型ID筛选"),
    is_assigned_to_reseller: Optional[bool] = Query(
        None, description="是否已分配给代理商"
    ),
    is_used_by_user: Optional[bool] = Query(None, description="是否已被用户激活使用"),
    batch_id: Optional[str] = Query(None, description="按批次号筛选"),
    issued_by_user_id: Optional[int] = Query(None, description="按发放者ID筛选"),
    consignment_reseller_id: Optional[int] = Query(
        None, description="按寄售代理ID筛选"
    ),
    user_id: Optional[int] = Query(None, description="按使用者ID筛选"),
    sort_by: Optional[str] = Query(
        "created_at", description="排序字段 (例如: created_at, expires_at, status)"
    ),
    sort_order: Optional[str] = Query("desc", description="排序顺序 ('asc' 或 'desc')"),
):
    """
    获取卡密列表，支持分页和多种筛选条件。
    需要超级管理员权限。
    """
    license_keys = crud_license_key.get_multi_with_filters(
        db,
        skip=skip,
        limit=limit,
        key_string=key_string,
        status=status,
        license_type_id=license_type_id,
        is_assigned_to_reseller=is_assigned_to_reseller,
        is_used_by_user=is_used_by_user,
        batch_id=batch_id,
        issued_by_user_id=issued_by_user_id,
        consignment_reseller_id=consignment_reseller_id,
        user_id=user_id,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    total_count = crud_license_key.count_with_filters(
        db,
        key_string=key_string,
        status=status,
        license_type_id=license_type_id,
        is_assigned_to_reseller=is_assigned_to_reseller,
        is_used_by_user=is_used_by_user,
        batch_id=batch_id,
        issued_by_user_id=issued_by_user_id,
        consignment_reseller_id=consignment_reseller_id,
        user_id=user_id,
    )
    return PaginatedResponse(
        total=total_count,
        items=license_keys,
        page=(skip // limit) + 1 if limit > 0 else 1,
        limit=limit,
    )


@router.get(
    "/{license_key_id}",
    response_model=LicenseKeyResponseAdmin,
    summary="获取特定卡密详情 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_license_key_admin(license_key_id: int, db: Session = Depends(api_deps.get_db)):
    """
    根据ID获取特定卡密的详细信息。
    需要超级管理员权限。
    """
    db_license_key = crud_license_key.get_with_details(db, id=license_key_id)
    if db_license_key is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="卡密不存在")
    return db_license_key


@router.put(
    "/{license_key_id}",
    response_model=LicenseKeyResponseAdmin,
    summary="管理员更新卡密信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_license_key_admin(
    license_key_id: int,
    license_key_in: AdminLicenseKeyUpdate,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User # 用于记录操作者或某些逻辑
):
    """
    管理员更新特定卡密的信息。
    允许修改状态、有效期、分配的代理商等。
    需要超级管理员权限。
    """
    db_license_key = crud_license_key.get(db, id=license_key_id)
    if db_license_key is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="卡密不存在")

    # 如果更新中包含 license_type_id，需要校验新的类型是否存在且有效
    if license_key_in.license_type_id is not None:
        new_type = crud_license_type.get(db, id=license_key_in.license_type_id)
        if not new_type or not new_type.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"新的卡密类型ID {license_key_in.license_type_id} 无效或未激活。",
            )

    # 如果更新中包含 user_id，需要校验用户是否存在
    if license_key_in.user_id is not None:
        from app.crud.crud_user import user as crud_user  # 避免循环导入，局部导入

        user_to_assign = crud_user.get(db, id=license_key_in.user_id)
        if not user_to_assign:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"指定的用户ID {license_key_in.user_id} 不存在。",
            )

    # 如果更新中包含 consignment_reseller_id，需要校验代理是否存在
    if license_key_in.consignment_reseller_id is not None:
        from app.crud.crud_user import user as crud_user

        reseller_to_assign = crud_user.get(
            db, id=license_key_in.consignment_reseller_id
        )
        if (
            not reseller_to_assign or reseller_to_assign.role != "reseller"
        ):  # 假设用户模型有 role 字段
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"指定的代理ID {license_key_in.consignment_reseller_id} 无效或不是代理。",
            )

    # 特殊逻辑：如果将卡密状态从未激活改为激活，或者从已分配给代理改为直接激活给用户
    if (
        license_key_in.status == models.LicenseKeyStatus.used
        and db_license_key.status != models.LicenseKeyStatus.used
    ):  # MODIFIED: LicenseKeyStatus
        if license_key_in.user_id is None:
            # 如果要将卡密标记为已使用，但没有提供 user_id
            if db_license_key.user_id is None:  # 且原卡密也没有 user_id
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="激活卡密时必须提供 user_id。",
                )
            # 如果 license_key_in.user_id 为 None，但 db_license_key.user_id 存在，则沿用旧的 user_id
            # 这种情况通常是管理员直接修改其他字段，而非激活操作

        # 如果提供了 user_id，且卡密之前未激活或未分配给此用户
        if license_key_in.user_id is not None and (
            db_license_key.user_id != license_key_in.user_id
            or db_license_key.status != models.LicenseKeyStatus.used
        ):  # MODIFIED: LicenseKeyStatus
            if db_license_key.activated_at is None:  # 首次激活
                # 更新 activated_at 字段，从 license_key_in 中取，如果没有则现在设置
                if (
                    license_key_in.activated_at is None
                ):  # AdminLicenseKeyUpdate 没有 activated_at
                    #  这里需要决定，管理员更新时，如果将卡密改为used，activated_at如何处理
                    #  一个选择是，如果之前没有激活过，就设置为当前时间。
                    #  另一个选择是，要求 AdminLicenseKeyUpdate 包含 activated_at，或者此操作不由这个通用update完成
                    #  当前 AdminLicenseKeyUpdate schema 没有 activated_at，所以这里我们不自动设置
                    #  如果需要管理员手动设置激活时间，schema需要调整。
                    #  或者，如果状态改为 used 且之前未激活，activated_at 应该由一个专门的"激活"动作（如果存在）或这里逻辑补充
                    pass  # 暂时不处理 activated_at，依赖于 schema 和业务决定
            if license_key_in.expires_at is None and db_license_key.expires_at is None:
                # 如果要激活卡，但没提供过期时间，且之前也没有过期时间，需要根据类型计算
                # 这部分逻辑在用户激活时处理，管理员直接改状态为 used，应确保 expires_at 已被合理设置
                # 或者，如果 license_type_id 变了，也需要重新计算 expires_at
                # 简化处理：假设管理员在将 status 改为 used 时，会同时提供合理的 expires_at
                # 否则，这个卡密可能变为一个没有过期时间的"已使用"卡密，这可能不是期望行为。
                # 此处不自动计算，依赖于调用者通过 license_key_in.expires_at 提供。
                pass

    updated_license_key = crud_license_key.update(
        db, db_obj=db_license_key, obj_in=license_key_in
    )
    # 更新后，重新加载关联数据以获得完整响应
    db.refresh(
        updated_license_key,
        attribute_names=["license_type", "user", "issuer", "consignment_reseller"],
    )
    return updated_license_key


@router.delete(
    "/{license_key_id}",
    response_model=Msg,
    summary="管理员作废/删除卡密",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def delete_license_key_admin(
    license_key_id: int,
    db: Session = Depends(api_deps.get_db),
    # current_user: UserModel = Depends(api_deps.get_current_active_superuser) # 可用于记录操作
):
    """
    管理员作废或删除特定卡密。
    当前实现为逻辑删除 (标记为 disabled)。
    需要超级管理员权限。
    """
    db_license_key = crud_license_key.get(db, id=license_key_id)
    if db_license_key is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="卡密不存在")

    if (
        db_license_key.status == models.LicenseKeyStatus.used
    ):  # MODIFIED: LicenseKeyStatus
        # 如果卡密已被激活使用，通常不应直接删除，而是禁用或使其过期
        # 这里我们选择标记为 disabled
        crud_license_key.update(
            db,
            db_obj=db_license_key,
            obj_in={"status": models.LicenseKeyStatus.disabled},
        )  # MODIFIED: LicenseKeyStatus
        return Msg(message="卡密已成功禁用。")
    elif (
        db_license_key.status == models.LicenseKeyStatus.disabled
    ):  # MODIFIED: LicenseKeyStatus
        return Msg(message="卡密已经是禁用状态。")
    else:
        # 对于未激活的卡密 (available, consigned_available, expired), 可以考虑物理删除或标记为 disabled
        # 此处统一标记为 disabled，如果需要物理删除，crud 层需要提供 remove 方法
        # crud_license_key.remove(db, id=license_key_id) # 物理删除的调用示例
        crud_license_key.update(
            db,
            db_obj=db_license_key,
            obj_in={"status": models.LicenseKeyStatus.disabled},
        )  # MODIFIED: LicenseKeyStatus
        return Msg(message="卡密已成功标记为禁用。")


# 如果需要物理删除的端点 (谨慎使用)
# @router.delete(
#     "/{license_key_id}/physical",
#     response_model=schemas.msg.Msg,
#     summary="管理员物理删除卡密 (谨慎操作)",
#     dependencies=[Depends(api_deps.get_current_active_superuser)]
# )
# def physical_delete_license_key_admin(
#     license_key_id: int,
#     db: Session = Depends(api_deps.get_db)
# ):
#     db_license_key = crud_license_key.get(db, id=license_key_id)
#     if db_license_key is None:
#         raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="卡密不存在")
#
#     # 添加额外检查，例如，不允许删除已激活的卡密
#     if db_license_key.status == LicenseKeyStatus.used:
#         raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="不能物理删除已激活的卡密，请先禁用。")
#
#     crud_license_key.remove(db, id=license_key_id) # 假设 CRUDBase 有 remove 方法
#     return schemas.msg.Msg(message=f"卡密 ID {license_key_id} 已被物理删除。")
