# app/apis/admin/system_admin.py
import os
import psutil
import platform
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import text

from app import models, crud
from app.apis import deps as api_deps
from app.schemas.dashboard import SystemHealthResponse, ExportTaskResponse
from app.schemas.msg import Msg
from app.core.config import settings

router = APIRouter()


@router.get(
    "/health",
    response_model=SystemHealthResponse,
    summary="系统健康检查",
    description="获取系统健康状态信息，包括数据库、内存、CPU等",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_system_health(
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    获取系统健康状态
    """
    # 检查数据库连接
    try:
        db.execute(text("SELECT 1"))
        database_status = "healthy"
    except Exception as e:
        database_status = f"error: {str(e)}"
    
    # 获取磁盘使用情况
    disk_usage = psutil.disk_usage('/')
    disk_info = {
        "total": disk_usage.total,
        "used": disk_usage.used,
        "free": disk_usage.free,
        "percent": round((disk_usage.used / disk_usage.total) * 100, 2)
    }
    
    # 获取内存使用情况
    memory = psutil.virtual_memory()
    memory_info = {
        "total": memory.total,
        "available": memory.available,
        "used": memory.used,
        "percent": memory.percent
    }
    
    # 获取CPU使用率
    cpu_usage = psutil.cpu_percent(interval=1)
    
    # 获取系统运行时间
    boot_time = psutil.boot_time()
    uptime_seconds = datetime.now().timestamp() - boot_time
    uptime_str = str(timedelta(seconds=int(uptime_seconds)))
    
    # Redis状态（如果使用）
    redis_status = None  # 如果使用Redis，在这里检查连接
    
    return SystemHealthResponse(
        database_status=database_status,
        redis_status=redis_status,
        disk_usage=disk_info,
        memory_usage=memory_info,
        cpu_usage=cpu_usage,
        uptime=uptime_str,
        version=settings.PROJECT_VERSION or "1.0.0"
    )


@router.get(
    "/info",
    summary="系统信息",
    description="获取系统基本信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_system_info(
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    获取系统基本信息
    """
    # 统计数据库表记录数
    table_counts = {}
    try:
        table_counts["users"] = db.query(models.User).count()
        table_counts["resellers"] = db.query(models.Reseller).count()
        table_counts["license_keys"] = db.query(models.LicenseKey).count()
        table_counts["scores"] = db.query(models.Score).count()
        table_counts["tickets"] = db.query(models.Ticket).count()
        table_counts["categories"] = db.query(models.Category).count()
        table_counts["comments"] = db.query(models.ScoreComment).count()
    except Exception as e:
        table_counts["error"] = str(e)
    
    # 系统信息
    system_info = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "architecture": platform.architecture()[0],
        "processor": platform.processor(),
        "hostname": platform.node(),
    }
    
    return {
        "system_info": system_info,
        "table_counts": table_counts,
        "settings": {
            "project_name": settings.PROJECT_NAME,
            "debug": settings.DEBUG,
            "environment": getattr(settings, 'ENVIRONMENT', 'production'),
            "database_url": settings.DATABASE_URL.replace(
                settings.DATABASE_URL.split('@')[0].split('://')[-1], 
                "***"
            ) if '@' in settings.DATABASE_URL else "***",
        }
    }


@router.post(
    "/cache/clear",
    response_model=Msg,
    summary="清理缓存",
    description="清理系统缓存（如果使用Redis等缓存系统）",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def clear_cache() -> Any:
    """
    清理系统缓存
    """
    # 这里可以添加清理Redis缓存的逻辑
    # 例如：redis_client.flushall()
    
    return Msg(message="缓存清理完成")


@router.post(
    "/maintenance/enable",
    response_model=Msg,
    summary="启用维护模式",
    description="启用系统维护模式，阻止普通用户访问",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def enable_maintenance_mode() -> Any:
    """
    启用维护模式
    """
    # 这里可以设置维护模式标志
    # 例如：在Redis中设置标志，或者创建维护文件
    
    return Msg(message="维护模式已启用")


@router.post(
    "/maintenance/disable",
    response_model=Msg,
    summary="禁用维护模式",
    description="禁用系统维护模式，恢复正常访问",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def disable_maintenance_mode() -> Any:
    """
    禁用维护模式
    """
    # 这里可以移除维护模式标志
    
    return Msg(message="维护模式已禁用")


@router.get(
    "/config",
    summary="获取系统配置",
    description="获取当前系统配置信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_system_config() -> Any:
    """
    获取系统配置
    """
    # 返回安全的配置信息（不包含敏感信息）
    config = {
        "project_name": settings.PROJECT_NAME,
        "debug": settings.DEBUG,
        "cors_allow_origins": settings.CORS_ALLOW_ORIGINS,
        "cors_allow_methods": settings.CORS_ALLOW_METHODS,
        "cors_allow_headers": settings.CORS_ALLOW_HEADERS,
        "access_token_expire_minutes": settings.ACCESS_TOKEN_EXPIRE_MINUTES,
        "refresh_token_expire_days": settings.REFRESH_TOKEN_EXPIRE_DAYS,
        "max_upload_size_mb": getattr(settings, 'MAX_UPLOAD_SIZE_MB', 10),
        "supported_file_types": getattr(settings, 'SUPPORTED_FILE_TYPES', ['.mid', '.midi']),
    }
    
    return {"config": config}


@router.put(
    "/config",
    response_model=Msg,
    summary="更新系统配置",
    description="更新系统配置（部分配置需要重启生效）",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_system_config(
    config_data: Dict[str, Any],
) -> Any:
    """
    更新系统配置
    """
    # 这里可以实现配置更新逻辑
    # 注意：某些配置可能需要重启应用才能生效
    
    # 验证配置数据
    allowed_keys = {
        "max_upload_size_mb",
        "supported_file_types",
        "cors_allow_origins",
        # 添加其他允许动态修改的配置
    }
    
    invalid_keys = set(config_data.keys()) - allowed_keys
    if invalid_keys:
        raise HTTPException(
            status_code=400,
            detail=f"不允许修改的配置项: {', '.join(invalid_keys)}"
        )
    
    # 这里可以将配置保存到数据库或配置文件
    # 实际实现取决于配置管理策略
    
    return Msg(message="配置更新成功，部分配置可能需要重启生效")


def export_data_task(export_type: str, filters: Dict[str, Any], task_id: str):
    """
    后台数据导出任务
    """
    # 这里实现实际的数据导出逻辑
    # 可以导出为CSV、Excel等格式
    pass


@router.post(
    "/export/users",
    response_model=ExportTaskResponse,
    summary="导出用户数据",
    description="导出用户数据到文件",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def export_users(
    background_tasks: BackgroundTasks,
    format: str = Query("csv", regex="^(csv|excel)$", description="导出格式"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
) -> Any:
    """
    导出用户数据
    """
    import uuid
    task_id = str(uuid.uuid4())
    
    filters = {
        "start_date": start_date,
        "end_date": end_date,
        "format": format
    }
    
    # 添加后台任务
    background_tasks.add_task(export_data_task, "users", filters, task_id)
    
    return ExportTaskResponse(
        task_id=task_id,
        status="pending",
        progress=0,
        created_at=datetime.utcnow()
    )


@router.get(
    "/export/status/{task_id}",
    response_model=ExportTaskResponse,
    summary="查询导出任务状态",
    description="查询数据导出任务的状态和进度",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_export_status(
    task_id: str,
) -> Any:
    """
    查询导出任务状态
    """
    # 这里应该从任务队列或数据库查询任务状态
    # 简化实现，返回模拟数据
    
    return ExportTaskResponse(
        task_id=task_id,
        status="completed",
        progress=100,
        download_url=f"/api/admin/system/export/download/{task_id}",
        created_at=datetime.utcnow() - timedelta(minutes=5),
        completed_at=datetime.utcnow()
    )
