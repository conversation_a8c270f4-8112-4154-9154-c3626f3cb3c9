# app/apis/admin/logs_admin.py
from typing import Any, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app import models, crud
from app.apis import deps as api_deps
from app.schemas.log import (
    UserSessionLogResponse,
    UserSessionLogListResponse,
    SystemLogResponse,
    SystemLogListResponse,
)
from app.schemas.user import PaginatedResponse

router = APIRouter()


@router.get(
    "/user-sessions",
    response_model=PaginatedResponse[UserSessionLogResponse],
    summary="获取用户会话日志",
    description="获取用户登录会话日志，支持筛选和分页",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_user_session_logs(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    user_id: Optional[int] = Query(None, description="按用户ID筛选"),
    username: Optional[str] = Query(None, description="按用户名筛选（模糊匹配）"),
    device_id: Optional[str] = Query(None, description="按设备ID筛选"),
    ip_address: Optional[str] = Query(None, description="按IP地址筛选"),
    start_time: Optional[datetime] = Query(None, description="会话开始时间范围（开始）"),
    end_time: Optional[datetime] = Query(None, description="会话开始时间范围（结束）"),
    is_active: Optional[bool] = Query(None, description="是否为活跃会话"),
) -> Any:
    """
    获取用户会话日志列表
    """
    # 构建查询条件
    query = db.query(models.UserSessionLog).join(
        models.User, models.UserSessionLog.user_id == models.User.id
    )
    
    # 应用筛选条件
    if user_id:
        query = query.filter(models.UserSessionLog.user_id == user_id)
    
    if username:
        query = query.filter(models.User.username.ilike(f"%{username}%"))
    
    if device_id:
        query = query.filter(models.UserSessionLog.device_id.ilike(f"%{device_id}%"))
    
    if ip_address:
        query = query.filter(models.UserSessionLog.ip_address.ilike(f"%{ip_address}%"))
    
    if start_time:
        query = query.filter(models.UserSessionLog.session_start_at >= start_time)
    
    if end_time:
        query = query.filter(models.UserSessionLog.session_start_at <= end_time)
    
    if is_active is not None:
        if is_active:
            query = query.filter(models.UserSessionLog.session_end_at.is_(None))
        else:
            query = query.filter(models.UserSessionLog.session_end_at.isnot(None))
    
    # 获取总数
    total = query.count()
    
    # 分页和排序
    logs = query.order_by(desc(models.UserSessionLog.session_start_at)).offset(skip).limit(limit).all()
    
    # 计算分页信息
    total_pages = (total + limit - 1) // limit
    
    return PaginatedResponse(
        data=[UserSessionLogResponse.model_validate(log) for log in logs],
        meta={
            "page": (skip // limit) + 1,
            "limit": limit,
            "total_items": total,
            "total_pages": total_pages
        }
    )


@router.get(
    "/user-sessions/{log_id}",
    response_model=UserSessionLogResponse,
    summary="获取特定用户会话日志",
    description="获取指定ID的用户会话日志详情",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_user_session_log(
    log_id: int,
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    获取特定用户会话日志
    """
    log = db.query(models.UserSessionLog).filter(
        models.UserSessionLog.id == log_id
    ).first()
    
    if not log:
        raise HTTPException(status_code=404, detail="会话日志未找到")
    
    return UserSessionLogResponse.model_validate(log)


@router.get(
    "/user-sessions/stats",
    summary="用户会话统计",
    description="获取用户会话的统计信息",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_user_session_stats(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> Any:
    """
    获取用户会话统计信息
    """
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # 总会话数
    total_sessions = db.query(models.UserSessionLog).filter(
        models.UserSessionLog.session_start_at >= start_date
    ).count()
    
    # 活跃会话数
    active_sessions = db.query(models.UserSessionLog).filter(
        and_(
            models.UserSessionLog.session_start_at >= start_date,
            models.UserSessionLog.session_end_at.is_(None)
        )
    ).count()
    
    # 平均会话时长（已结束的会话）
    from sqlalchemy import func
    avg_duration = db.query(
        func.avg(models.UserSessionLog.duration_seconds)
    ).filter(
        and_(
            models.UserSessionLog.session_start_at >= start_date,
            models.UserSessionLog.session_end_at.isnot(None),
            models.UserSessionLog.duration_seconds.isnot(None)
        )
    ).scalar()
    
    # 独立用户数
    unique_users = db.query(
        func.count(func.distinct(models.UserSessionLog.user_id))
    ).filter(
        models.UserSessionLog.session_start_at >= start_date
    ).scalar()
    
    # 独立设备数
    unique_devices = db.query(
        func.count(func.distinct(models.UserSessionLog.device_id))
    ).filter(
        models.UserSessionLog.session_start_at >= start_date
    ).scalar()
    
    # 每日会话趋势
    daily_sessions = []
    for i in range(min(days, 30)):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)
        
        count = db.query(models.UserSessionLog).filter(
            and_(
                models.UserSessionLog.session_start_at >= day_start,
                models.UserSessionLog.session_start_at < day_end
            )
        ).count()
        
        daily_sessions.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "count": count
        })
    
    return {
        "total_sessions": total_sessions,
        "active_sessions": active_sessions,
        "avg_duration_seconds": float(avg_duration) if avg_duration else 0,
        "unique_users": unique_users,
        "unique_devices": unique_devices,
        "daily_sessions": daily_sessions,
        "period_days": days
    }


@router.delete(
    "/user-sessions/cleanup",
    summary="清理过期会话日志",
    description="清理指定天数之前的会话日志",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def cleanup_old_session_logs(
    db: Session = Depends(api_deps.get_db),
    days: int = Query(90, ge=30, le=365, description="保留天数，超过此天数的日志将被删除"),
) -> Any:
    """
    清理过期的会话日志
    """
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    # 删除过期的会话日志
    deleted_count = db.query(models.UserSessionLog).filter(
        models.UserSessionLog.session_start_at < cutoff_date
    ).delete()
    
    db.commit()
    
    return {
        "message": f"已清理 {deleted_count} 条过期会话日志",
        "cutoff_date": cutoff_date.isoformat(),
        "deleted_count": deleted_count
    }


@router.get(
    "/system",
    summary="获取系统日志",
    description="获取系统操作日志（如果有实现）",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_system_logs(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    level: Optional[str] = Query(None, description="日志级别筛选"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
) -> Any:
    """
    获取系统日志
    注意：这需要实现系统日志记录功能
    """
    # 这里应该从日志文件或日志表中读取系统日志
    # 简化实现，返回模拟数据
    
    logs = [
        {
            "id": 1,
            "timestamp": datetime.utcnow(),
            "level": "INFO",
            "message": "系统启动",
            "module": "main",
            "user_id": None,
            "ip_address": None
        },
        {
            "id": 2,
            "timestamp": datetime.utcnow() - timedelta(minutes=30),
            "level": "WARNING",
            "message": "数据库连接池接近满载",
            "module": "database",
            "user_id": None,
            "ip_address": None
        }
    ]
    
    return {
        "data": logs,
        "meta": {
            "page": (skip // limit) + 1,
            "limit": limit,
            "total_items": len(logs),
            "total_pages": 1
        }
    }


@router.get(
    "/audit",
    summary="获取审计日志",
    description="获取用户操作审计日志",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def get_audit_logs(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    user_id: Optional[int] = Query(None, description="按用户ID筛选"),
    action: Optional[str] = Query(None, description="按操作类型筛选"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
) -> Any:
    """
    获取审计日志
    注意：这需要在关键操作中记录审计日志
    """
    # 这里应该从审计日志表中查询
    # 审计日志应该记录用户的关键操作，如：
    # - 登录/登出
    # - 卡密激活
    # - 重要设置修改
    # - 文件上传/删除
    # 等等
    
    # 简化实现，返回模拟数据
    audit_logs = [
        {
            "id": 1,
            "timestamp": datetime.utcnow(),
            "user_id": 1,
            "username": "admin",
            "action": "login",
            "resource": "system",
            "ip_address": "127.0.0.1",
            "user_agent": "Mozilla/5.0...",
            "details": {"login_method": "password"}
        }
    ]
    
    return {
        "data": audit_logs,
        "meta": {
            "page": (skip // limit) + 1,
            "limit": limit,
            "total_items": len(audit_logs),
            "total_pages": 1
        }
    }
