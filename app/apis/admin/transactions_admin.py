# TanZouDaShiAPI/app/apis/admin/transactions_admin.py
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.schemas.transaction import (
    CommissionRecordResponse,
    WithdrawalRecordResponse,
    WithdrawalRecordUpdateByAdmin,
)

from app import crud, models  # models是 app.db.models
from app.apis import deps as api_deps

router = APIRouter()


@router.get(
    "/commission-records",
    response_model=List[CommissionRecordResponse],
    summary="[Admin] 获取佣金记录列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_commission_records(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    reseller_id: Optional[int] = Query(None, description="按受益代理商ID筛选"),
    license_key_id: Optional[int] = Query(None, description="按关联卡密ID筛选"),
    status: Optional[models.CommissionStatus] = Query(
        None, description="按佣金状态筛选"
    ),
    start_time: Optional[datetime] = Query(
        None, description="按创建时间范围筛选 (起始)"
    ),
    end_time: Optional[datetime] = Query(None, description="按创建时间范围筛选 (结束)"),
):
    """
    管理员获取佣金记录列表，支持多种筛选条件和分页。
    """
    commission_records = (
        crud.crud_reseller_finance.crud_commission_record.get_multi_by_filters(
            db,
            skip=skip,
            limit=limit,
            beneficiary_reseller_user_id=reseller_id,
            source_license_id=license_key_id,
            status=status,
            start_time=start_time,
            end_time=end_time,
        )
    )
    response_data = []
    for record in commission_records:
        beneficiary_reseller_username = (
            record.beneficiary_reseller.username
            if record.beneficiary_reseller
            else None
        )
        source_license_key_string = (
            record.source_license.key_string if record.source_license else None
        )
        source_reseller_username = (
            record.source_reseller.username if record.source_reseller else None
        )
        related_username = record.related_user.username if record.related_user else None

        response_data.append(
            CommissionRecordResponse(
                **record.__dict__,  # type: ignore
                beneficiary_reseller_username=beneficiary_reseller_username,
                source_license_key_string=source_license_key_string,
                source_reseller_username=source_reseller_username,
                related_username=related_username,
            )
        )
    return response_data


@router.get(
    "/withdrawal-records",
    response_model=List[WithdrawalRecordResponse],
    summary="[Admin] 获取提现记录列表",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_withdrawal_records(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    reseller_id: Optional[int] = Query(None, description="按代理商ID筛选"),
    status: Optional[models.WithdrawalStatus] = Query(
        None, description="按提现状态筛选"
    ),  # MODIFIED: WithdrawalStatus to models.WithdrawalStatus
    start_time: Optional[datetime] = Query(
        None, description="按申请时间范围筛选 (起始)"
    ),
    end_time: Optional[datetime] = Query(None, description="按申请时间范围筛选 (结束)"),
):
    """
    管理员获取提现记录列表，支持多种筛选条件和分页。
    """
    withdrawal_records = (
        crud.crud_reseller_finance.crud_withdrawal_record.get_multi_by_filters(
            db,
            skip=skip,
            limit=limit,
            reseller_user_id=reseller_id,
            status=status,
            start_time=start_time,
            end_time=end_time,
        )
    )
    response_data = []
    for record in withdrawal_records:
        reseller_username = record.reseller.username if record.reseller else None
        response_data.append(
            WithdrawalRecordResponse(
                **record.__dict__,  # type: ignore
                reseller_username=reseller_username,
            )
        )
    return response_data


@router.get(
    "/withdrawal-records/{record_id}",
    response_model=WithdrawalRecordResponse,
    summary="[Admin] 获取特定提现记录详情",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_withdrawal_record_detail(
    record_id: int,
    db: Session = Depends(api_deps.get_db),
):
    """
    管理员获取单个提现记录的详细信息。
    """
    record = crud.crud_reseller_finance.crud_withdrawal_record.get(db, id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="提现记录未找到")

    reseller_username = record.reseller.username if record.reseller else None
    return WithdrawalRecordResponse(
        **record.__dict__,  # type: ignore
        reseller_username=reseller_username,
    )


@router.patch(
    "/withdrawal-records/{record_id}/process",
    response_model=WithdrawalRecordResponse,
    summary="[Admin] 管理员处理提现请求",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def process_withdrawal_record(
    record_id: int,
    update_data: WithdrawalRecordUpdateByAdmin,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # 获取当前管理员信息
):
    """
    管理员处理提现请求，更新状态 (批准/拒绝) 和备注。
    """
    record = crud.crud_reseller_finance.crud_withdrawal_record.get(db, id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="提现记录未找到")

    if record.status not in [
        models.WithdrawalStatus.pending
    ]:  # MODIFIED: WithdrawalStatus to models.WithdrawalStatus
        raise HTTPException(
            status_code=400, detail=f"该提现请求当前状态为 {record.status}, 不可处理。"
        )

    if update_data.status not in [
        models.WithdrawalStatus.approved,
        models.WithdrawalStatus.rejected,
    ]:  # MODIFIED: WithdrawalStatus to models.WithdrawalStatus
        raise HTTPException(
            status_code=400,
            detail="无效的提现处理状态。仅允许 'approved' 或 'rejected'。",
        )

    record.status = update_data.status
    record.admin_notes = update_data.admin_notes
    record.processed_at = datetime.now(datetime.timezone.utc)

    db.add(record)
    db.commit()
    db.refresh(record)

    reseller_username = record.reseller.username if record.reseller else None
    return WithdrawalRecordResponse(
        **record.__dict__,  # type: ignore
        reseller_username=reseller_username,
    )
