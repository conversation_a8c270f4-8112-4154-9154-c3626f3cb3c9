from fastapi import APIRouter

from app.apis.admin import auth_admin  # 导入管理员认证路由
from app.apis.admin import users_admin  # 导入新的用户管理路由
from app.apis.admin import scores_admin  # 导入新的谱曲管理路由
from app.apis.admin import license_keys_admin  # 导入新的卡密管理路由
from app.apis.admin import resellers_admin  # 导入代理管理路由
from app.apis.admin import transactions_admin  # 新增导入财务管理路由
from app.apis.admin import tickets_admin  # 导入工单管理路由
from app.apis.admin import categories_admin  # 导入分类管理路由
from app.apis.admin import comments_admin  # 导入评论管理路由
from app.apis.admin import dashboard_admin  # 导入统计仪表盘路由
from app.apis.admin import system_admin  # 导入系统管理路由
from app.apis.admin import logs_admin  # 导入日志管理路由

api_router_admin = APIRouter()

# 在这里可以引入并包含各个管理模块的子路由，例如：
# from .endpoints import items
api_router_admin.include_router(
    auth_admin.router, prefix="/auth", tags=["Admin - Auth"]
)  # 注册管理员认证路由
api_router_admin.include_router(
    users_admin.router, prefix="/users", tags=["Admin - Users"]
)
api_router_admin.include_router(
    scores_admin.router, prefix="/scores", tags=["Admin - Scores"]
)  # 注册谱曲管理路由
api_router_admin.include_router(
    license_keys_admin.router, prefix="/license-keys", tags=["Admin - License Keys"]
)  # 注册卡密管理路由
api_router_admin.include_router(
    resellers_admin.router_reseller_levels,
    prefix="/reseller-levels",
    tags=["Admin - Reseller Levels"],
)  # 注册代理级别管理路由
api_router_admin.include_router(
    resellers_admin.router_resellers, prefix="/resellers", tags=["Admin - Resellers"]
)  # 注册代理账户管理路由
api_router_admin.include_router(
    transactions_admin.router,
    prefix="/finance",
    tags=["Admin - Financial Transactions"],
)  # 注册新的财务管理路由
api_router_admin.include_router(
    tickets_admin.router, prefix="/tickets", tags=["Admin - Tickets"]
)  # 注册工单管理路由
api_router_admin.include_router(
    categories_admin.router, prefix="/categories", tags=["Admin - Categories"]
)  # 注册分类管理路由
api_router_admin.include_router(
    comments_admin.router, prefix="/comments", tags=["Admin - Comments"]
)  # 注册评论管理路由
api_router_admin.include_router(
    dashboard_admin.router, prefix="/dashboard", tags=["Admin - Dashboard"]
)  # 注册统计仪表盘路由
api_router_admin.include_router(
    system_admin.router, prefix="/system", tags=["Admin - System"]
)  # 注册系统管理路由
api_router_admin.include_router(
    logs_admin.router, prefix="/logs", tags=["Admin - Logs"]
)  # 注册日志管理路由
# api_router_admin.include_router(items.router, prefix="/items", tags=["admin-items"])


# 健康检查或根路径
@api_router_admin.get(
    "/", summary="管理端API根路径", tags=["Admin - Root"]
)  # 标签统一一下风格
def admin_root():
    return {"message": "欢迎来到管理端API"}
