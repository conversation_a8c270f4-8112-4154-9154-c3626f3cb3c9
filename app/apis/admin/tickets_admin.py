# TanZouDaShiAPI/app/apis/admin/tickets_admin.py
from typing import List, Optional, Any
from datetime import datetime

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    Body,
    Path,
    status,
)  # Added status
from sqlalchemy.orm import Session

from app import crud, models  # Added crud
from app.schemas.ticket import (
    TicketListResponse,
    TicketDetailResponse,
    TicketMessageResponse,
    TicketMessageCreate,
    TicketResponse,
    TicketUpdateByAdmin,
    # TicketCreateByAdmin, # Removed as it's not defined in schemas
    # TicketMessageCreateByAdmin # Removed as it's not defined in schemas and not used
)
from app.schemas.msg import Msg

from app.crud.crud_ticket import (
    ticket as crud_ticket,
    ticket_message as crud_ticket_message,
)
from app.apis import deps as api_deps

router = APIRouter()


@router.get(
    "/",
    response_model=TicketListResponse,
    summary="获取工单列表 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_tickets_admin(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    user_id: Optional[int] = Query(None, description="按提交用户ID筛选"),
    status: Optional[models.TicketStatus] = Query(None, description="按工单状态筛选"),
    priority: Optional[models.TicketPriority] = Query(
        None, description="按工单优先级筛选"
    ),
    ticket_type: Optional[str] = Query(
        None, description="按工单类型/分类筛选 (对应 category 字段)"
    ),
    title_search: Optional[str] = Query(None, description="按工单标题模糊搜索"),
    created_at_start: Optional[datetime] = Query(
        None, description="创建时间范围开始 (YYYY-MM-DDTHH:MM:SS)"
    ),
    created_at_end: Optional[datetime] = Query(
        None, description="创建时间范围结束 (YYYY-MM-DDTHH:MM:SS)"
    ),
    # current_user: models.User = Depends(deps.get_current_active_superuser) # 确保管理员权限
) -> Any:
    """
    管理员获取工单列表，支持多种筛选条件和分页。
    """
    tickets = crud_ticket.get_multi_with_filters(
        db,
        skip=skip,
        limit=limit,
        user_id=user_id,
        status=status,
        priority=priority,
        ticket_type=ticket_type,
        title_search=title_search,
        created_at_start=created_at_start,
        created_at_end=created_at_end,
    )
    total = crud_ticket.count_with_filters(
        db,
        user_id=user_id,
        status=status,
        priority=priority,
        ticket_type=ticket_type,
        title_search=title_search,
        created_at_start=created_at_start,
        created_at_end=created_at_end,
    )
    return TicketListResponse(
        total=total,
        items=tickets,
        page=(skip // limit) + 1 if limit > 0 else 1,
        limit=limit,
    )


@router.get(
    "/{ticket_id}",
    response_model=TicketDetailResponse,
    summary="获取特定工单详情 (管理员)",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def read_ticket_admin(
    ticket_id: int = Path(..., description="要获取详情的工单ID", ge=1),
    db: Session = Depends(api_deps.get_db),
    # current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    管理员获取特定工单的详细信息，包括所有消息。
    """
    db_ticket = crud_ticket.get_with_messages(db, id=ticket_id)
    if not db_ticket:
        raise HTTPException(status_code=404, detail="工单未找到")
    # TicketDetailResponse 会自动处理 messages 的序列化
    return db_ticket


@router.post(
    "/{ticket_id}/messages",
    response_model=TicketMessageResponse,
    summary="管理员回复工单",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def create_ticket_message_admin(
    ticket_id: int = Path(..., description="要回复的工单ID", ge=1),
    message_in: TicketMessageCreate = Body(...),
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_superuser),
) -> Any:
    """
    管理员回复工单。
    - `sender_id` 将自动设为当前管理员ID。
    - 工单状态会根据回复情况更新。
    """
    db_ticket = crud_ticket.get(db, id=ticket_id)
    if not db_ticket:
        raise HTTPException(status_code=404, detail="工单未找到，无法回复")

    if (
        db_ticket.status in [models.TicketStatus.closed, models.TicketStatus.resolved]
        and not message_in.is_internal
    ):
        raise HTTPException(
            status_code=400,
            detail=f"工单当前状态为 '{db_ticket.status.value}'，已关闭或已解决，如需回复请先重新打开或标记为处理中。",
        )

    # 管理员回复时，sender_is_admin_or_agent 为 True
    new_message = crud_ticket_message.create_with_ticket_update(
        db=db,
        obj_in=message_in,
        ticket=db_ticket,
        sender_id=current_user.id,
        sender_is_admin_or_agent=True,  # 管理员回复
    )
    return new_message


@router.put(
    "/{ticket_id}",
    response_model=TicketResponse,
    summary="管理员更新工单信息/状态",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def update_ticket_admin(
    ticket_id: int = Path(..., description="要更新的工单ID", ge=1),
    ticket_in: TicketUpdateByAdmin = Body(...),
    db: Session = Depends(api_deps.get_db),
    # current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    管理员更新工单信息，如状态、优先级、分类、指派人等。
    """
    db_ticket = crud_ticket.get(db, id=ticket_id)
    if not db_ticket:
        raise HTTPException(status_code=404, detail="工单未找到")

    # 检查指派的用户是否存在
    if ticket_in.assignee_user_id is not None:
        assignee_user = crud.user.user.get(db, id=ticket_in.assignee_user_id)
        if not assignee_user:
            raise HTTPException(
                status_code=404,
                detail=f"ID为 {ticket_in.assignee_user_id} 的指派用户未找到",
            )
        # 可以进一步检查指派的用户是否是管理员或客服角色，如果业务需要
        # if assignee_user.role not in [models.UserRole.admin, models.UserRole.staff]: # 假设有 staff 角色
        #     raise HTTPException(status_code=400, detail="只能指派给管理员或客服人员")

    updated_ticket = crud_ticket.update(db=db, db_obj=db_ticket, obj_in=ticket_in)
    return updated_ticket


# New admin endpoints migrated from v1
@router.get(
    "/list",  # Changed path from v1's /admin/all to /list to avoid conflict with more advanced /
    response_model=List[TicketResponse],
    summary="管理员获取所有工单列表 (简化版)",
    description="管理员获取系统中的所有工单，支持分页和基础筛选条件。",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
)
def admin_read_tickets_list(
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页的最大记录数"),
    user_id_filter: Optional[int] = Query(
        None, alias="user_id", description="按提交用户ID筛选"
    ),
    status_filter: Optional[models.TicketStatus] = Query(
        None, alias="status", description="按工单状态筛选"
    ),
    assignee_id_filter: Optional[int] = Query(
        None, alias="assignee_id", description="按指派负责人ID筛选 (0 for unassigned)"
    ),
    priority_filter: Optional[models.TicketPriority] = Query(
        None, alias="priority", description="按优先级筛选"
    ),
) -> Any:
    """
    管理员获取所有工单列表 (简化版)。
    - 支持按用户ID, 状态, 指派人, 优先级进行筛选。
    """
    query = db.query(models.Ticket)
    if user_id_filter is not None:
        query = query.filter(models.Ticket.user_id == user_id_filter)
    if status_filter:
        query = query.filter(models.Ticket.status == status_filter)
    if assignee_id_filter is not None:
        if assignee_id_filter == 0:
            query = query.filter(models.Ticket.assignee_user_id.is_(None))
        else:
            query = query.filter(models.Ticket.assignee_user_id == assignee_id_filter)
    if priority_filter:
        query = query.filter(models.Ticket.priority == priority_filter)

    tickets = (
        query.order_by(models.Ticket.updated_at.desc()).offset(skip).limit(limit).all()
    )
    return tickets


@router.patch(
    "/{ticket_id}/assign",
    response_model=TicketResponse,
    summary="管理员指派工单",
    description="管理员将特定工单指派给一个用户（通常是另一位管理员或客服）。",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    tags=["Admin - Tickets"],
)
def admin_assign_ticket(  # async def -> def
    ticket_id: int = Path(..., description="要指派的工单ID", ge=1),
    assignment_in: TicketUpdateByAdmin = Body(
        ...
    ),  # Uses assignee_user_id from this schema
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> Any:
    """
    管理员指派工单。
    - **ticket_id**: 要指派的工单ID。
    - **assignment_in**: 包含 `assignee_user_id` 的请求体。
    """
    # ticket = crud_ticket.get(db=db, id=ticket_id)
    # if not ticket:
    #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="工单未找到")

    # if assignment_in.assignee_user_id is None:
    #     # This field is optional in TicketUpdateByAdmin, so we must check
    #     raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="必须在请求体中提供 assignee_user_id")

    # assignee_user = crud.user.user.get(db, id=assignment_in.assignee_user_id)
    # if not assignee_user:
    #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"ID为 {assignment_in.assignee_user_id} 的指派用户未找到")
    # # Optional: Check if assignee_user role is appropriate (e.g., admin or staff)
    # # if not (assignee_user.role == UserModel.UserRole.admin or assignee_user.role == UserModel.UserRole.staff): # Example check
    # #     raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只能指派给管理员或客服人员")

    # updated_ticket = crud_ticket.assign_ticket(
    #     db=db,
    #     db_obj=ticket,
    #     assignee_user_id=assignment_in.assignee_user_id,
    #     # updated_by_user_id=current_admin.id # crud_ticket.assign_ticket can optionally take this
    # )
    # return updated_ticket
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="此功能尚未实现"
    )


@router.patch(
    "/{ticket_id}/status",
    response_model=TicketResponse,
    summary="管理员更新工单状态",
    description="管理员更新特定工单的状态。",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    tags=["Admin - Tickets"],
)
def admin_update_ticket_status(  # async def -> def
    ticket_id: int = Path(..., description="要更新状态的工单ID", ge=1),
    status_update_in: TicketUpdateByAdmin = Body(...),  # Uses status from this schema
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(api_deps.get_current_active_superuser),
) -> Any:
    """
    管理员更新工单状态。
    - **ticket_id**: 要更新状态的工单ID。
    - **status_update_in**: 包含新 `status` 的请求体。
    """
    # ticket = crud_ticket.get(db=db, id=ticket_id)
    # if not ticket:
    #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="工单未找到")

    # if status_update_in.status is None:
    #     # This field is optional in TicketUpdateByAdmin, so we must check
    #     raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="必须在请求体中提供新的工单状态")

    # updated_ticket = crud_ticket.update_status(
    #     db=db,
    #     db_obj=ticket,
    #     status=status_update_in.status,
    #     # updated_by_user_id=current_admin.id # crud_ticket.update_status can optionally take this
    # )
    # return updated_ticket
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED, detail="此功能尚未实现"
    )


@router.delete(
    "/{ticket_id}",
    response_model=Msg,
    summary="管理员删除工单",
    dependencies=[Depends(api_deps.get_current_active_superuser)],
    status_code=200,  # 也可以用 204 No Content，但 Msg schema 需要返回消息
)
def delete_ticket_admin(
    ticket_id: int = Path(..., description="要删除的工单ID", ge=1),
    db: Session = Depends(api_deps.get_db),
    # current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    管理员删除工单。
    - 工单及其所有关联消息将被删除 (通过 SQLAlchemy 的 cascade 设置)。
    """
    db_ticket = crud_ticket.get(db, id=ticket_id)
    if not db_ticket:
        raise HTTPException(status_code=404, detail="工单未找到")

    crud_ticket.remove(db=db, id=ticket_id)
    return Msg(message="工单已成功删除")
