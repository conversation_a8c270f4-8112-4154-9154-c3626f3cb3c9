# app/apis/deps.py
from typing import Generator

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer  # 引入 OAuth2PasswordBearer
from jose import (
    JWTError,
)  # JWTError 已被 security.decode_access_token 内部处理，这里可能不需要直接用
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.core import security
from app import models  # 统一从 app 导入 models
from app.crud.crud_user import user as crud_user  # 引入用户相关的 CRUD
from app.crud.crud_reseller import crud_reseller  # 导入 Reseller CRUD


# 数据库会话依赖
def get_db() -> Generator:
    """
    获取数据库会话的依赖项。
    确保会话在使用后被正确关闭。
    """
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


# OAuth2 密码 Bearer 方案
# 注意: tokenUrl 应该与 app/main.py 中注册 /api/v1/auth 路由后的完整路径匹配
# 如果 app.main.py 中是 app.include_router(api_v1_router, prefix="/api/v1")
# 且 api_v1_router 中是 router.include_router(auth_router, prefix="/auth", tags=["认证模块"])
# 且 auth_router 中有 @router.post("/login")
# 那么 tokenUrl 应该是 "/api/v1/auth/login"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


def get_current_user(
    token: str = Depends(
        oauth2_scheme
    ),  # token 从请求头 Authorization: Bearer <token> 获取
    db: Session = Depends(get_db),
) -> models.User:  # 返回类型应为 models.User
    """
    获取当前用户的依赖项。
    解码 JWT token，验证用户，并从数据库中获取用户信息。
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = security.decode_access_token(token)
        if payload is None:
            # security.decode_access_token 返回 None 代表解码失败或token无效
            raise credentials_exception

        user_id_str = payload.get("sub")
        if user_id_str is None:
            raise credentials_exception

        try:
            user_id = int(user_id_str)
        except ValueError:
            # 如果 sub 不是一个有效的整数ID字符串
            raise credentials_exception

        # 使用 TokenPayload 主要是为了结构化，如果仅用 user_id 也可以
        # token_data = TokenPayload(sub=user_id)

    except (
        JWTError
    ):  # decode_access_token 内部已处理 JWTError 并返回 None，理论上不会在这里捕获
        raise credentials_exception

    user_obj = crud_user.get(db, id=user_id)  # 通过 user_id 获取用户
    if user_obj is None:
        raise credentials_exception
    return user_obj


def get_current_active_user(
    current_user: models.User = Depends(get_current_user),  # 返回类型应为 models.User
) -> models.User:  # 返回类型应为 models.User
    """
    获取当前活动用户的依赖项。
    检查用户是否为 active 状态。
    """
    if not current_user:  # 理论上 get_current_user 失败会抛异常，这里是额外保护
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="用户未认证"
        )
    if (
        current_user.status != "active"
    ):  # 假设 User 模型有 status 字段且 "active" 是有效状态
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="非活动用户或用户已被禁用"
        )
    return current_user


# from app.db.models.user import Reseller as ResellerModel # 导入 Reseller 模型 - 改为通过 models.Reseller 访问


def get_current_active_reseller_user(
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> models.User:
    """
    获取当前活动的代理用户。
    检查用户是否为 active 状态、角色为 reseller，并且其代理记录也为 active。
    """
    if (
        not hasattr(current_user, "role")
        or current_user.role != models.UserRole.reseller
    ):  # type: ignore
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户无代理权限"
        )

    reseller_profile = crud_reseller.get_by_user_id(db, user_id=current_user.id)
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户不是有效的代理"
        )
    if (
        reseller_profile.status != "active"
    ):  # 假设 Reseller 模型有 status 字段，且 "active" 是有效状态
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="代理账户已被禁用或非活动状态"
        )
    return current_user


def get_current_active_superuser(
    current_user: models.User = Depends(
        get_current_active_user
    ),  # 依赖于 get_current_active_user 以确保用户是活动的
) -> models.User:
    """
    获取当前活动的超级用户。
    检查用户是否为 active 状态且角色为 admin。
    """
    if (
        not current_user or current_user.role != models.UserRole.admin
    ):  # 确保 current_user 存在且角色是管理员
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户没有足够的权限",  # 中文注释
        )
    return current_user


# （可选）如果需要检查职员用户，可以添加类似下面的函数：
# async def get_current_active_staff_user(
#     current_user: User = Depends(get_current_active_user)
# ) -> User:
#     """
#     获取当前活动的职员用户。
#     检查用户是否为 active 状态且 is_staff 字段为 True (或通过 reseller_level_id 判断)。
#     """
#     # 假设 is_staff 字段存在，或者通过 reseller_level_id > 0 判断
#     # if not current_user.is_staff and not (hasattr(current_user, 'reseller_level_id') and current_user.reseller_level_id and current_user.reseller_level_id > 0):
#     #     raise HTTPException(
#     #         status_code=status.HTTP_403_FORBIDDEN,
#     #         detail="用户不是职员或代理商" # 中文注释
#     #     )
#     return current_user
