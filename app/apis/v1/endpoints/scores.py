# TanZouDaShiAPI/app/apis/v1/endpoints/scores.py
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session, joinedload

from app import crud, models
from app.schemas.score import (
    ScoreResponse,
    ScoreCreate,
    ScorePaginatedResponse,
    PageMeta,
    ScoreSummaryResponse,
    ScoreDetailResponse,
    ScoreUpdate,
)
from app.schemas.msg import Msg
from app.schemas.user_interaction import (
    UserFavoriteResponse,
    UserFavoriteCreate,
    ScoreIsFavoritedResponse,
    UserScoreRatingResponse,
    UserScoreRatingCreate,
    ScoreRatingSummaryResponse,
    ScoreCommentResponse,
    ScoreCommentCreate,
    ScoreCommentUpdate,
)
from app.apis import deps as api_deps
# MODIFIED: Removed specific import for ScoreStatus, will use models.ScoreStatus

router = APIRouter()


@router.post(
    "/",
    response_model=ScoreResponse,  # API 设计文档中提议 ScoreResponse 或 ScoreDetailResponse
    status_code=status.HTTP_201_CREATED,
    summary="创建新谱曲",
    description="用户上传一个新的谱曲信息。上传者ID将自动设置为当前认证用户。",
)
def create_score(
    *,
    db: Session = Depends(api_deps.get_db),
    score_in: ScoreCreate,
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    创建新的谱曲。

    - **score_in**: 谱曲创建所需的数据。
    - **current_user**: 当前认证的用户，其ID将作为谱曲的上传者。
    """
    # 检查 category_id 是否有效 (可选，如果CRUD层面不处理)
    category = crud.category.get(db, id=score_in.category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"ID为 {score_in.category_id} 的分类不存在。",
        )

    # 将当前用户ID设置为上传者ID
    # ScoreCreate schema 中已有 uploader_user_id 字段，但这里要确保它被正确赋值
    # 实际上，更推荐的做法是在 ScoreCreate schema 中移除 uploader_user_id，
    # 或者使其可选，然后在服务端强制赋值。
    # 假设 ScoreCreate 仍有 uploader_user_id，我们在此覆盖它。

    # 创建一个新的 ScoreCreate 实例或字典，以确保 uploader_user_id 被正确设置
    # 同时 Pydantic 的 tags validator 已经在 ScoreCreate 中定义

    # 确保 score_in 中的 uploader_user_id 是当前用户
    # 如果 score_in 允许客户端传递 uploader_user_id，这里应该覆盖或验证
    # 按照需求 "上传者ID应自动设为当前用户", 我们需要确保这一点。
    # 最好的方式是创建一个新的数据对象传递给 CRUD

    score_create_data = score_in.model_dump()
    score_create_data["uploader_user_id"] = current_user.id

    # 重新用 ScoreCreate 验证数据，确保类型正确 (如果需要，但通常 model_dump 后直接用也可)
    # validated_score_in = ScoreCreate(**score_create_data)

    try:
        # 调用 crud.score.create 方法时，它期望一个 ScoreCreate 类型的 obj_in
        # 而 ScoreCreate schema 本身可能没有 uploader_user_id 字段，或者如果有，它应该由服务器填充
        # 修正：ScoreCreate schema 有 uploader_user_id，所以可以直接覆盖

        # 直接修改 score_in 对象的 uploader_user_id (如果 Pydantic 模型允许)
        # 或者创建一个新的字典传递给 CRUD

        # 假设 ScoreCreate schema 允许 uploader_user_id 且我们在这里设置
        score_to_create = ScoreCreate(**score_in.model_dump(exclude_unset=True))
        score_to_create.uploader_user_id = current_user.id  # 强制设置

        created_score = crud.score.create(db=db, obj_in=score_to_create)
    except Exception as e:
        # 更具体的异常捕获，例如数据库完整性错误
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建谱曲失败: {str(e)}",
        )

    # 为了返回嵌套的用户和分类信息，需要重新查询或确保CRUD返回的对象已加载关系
    # crud.score.create 返回的是 Score ORM 对象，Pydantic 的 orm_mode 会处理转换
    # 确保 ScoreResponse 的 orm_mode=True 且嵌套的 schema 也配置正确
    # 如果需要立即返回嵌套信息，可能需要这样：
    # return crud.score.get_with_details(db, id=created_score.id)
    # 或者在 crud.score.create 之后，手动加载关系（不推荐在API层做）
    # Pydantic 的 orm_mode 应该能处理 Score ORM 对象到 ScoreResponse 的转换，包括嵌套
    return created_score


@router.get(
    "/",
    response_model=ScorePaginatedResponse,  # 使用新的分页响应模型
    summary="获取谱曲列表",
    description="获取谱曲列表，支持分页、筛选和排序。默认只返回已批准的谱曲。",
    responses={
        200: {
            "description": "成功获取谱曲列表",
            "content": {
                "application/json": {
                    "examples": {
                        "default_list": {
                            "summary": "默认谱曲列表",
                            "value": {
                                "meta": {
                                    "page": 1,
                                    "limit": 20,
                                    "total_items": 150,
                                    "total_pages": 8
                                },
                                "data": [
                                    {
                                        "id": 1,
                                        "title": "卡农",
                                        "description_snippet": "经典古典音乐作品，适合钢琴演奏。这是一首非常优美的曲子...",
                                        "uploader_info": {
                                            "id": 1,
                                            "username": "musiclover",
                                            "nickname": "音乐爱好者",
                                            "avatar_url": "https://example.com/avatar1.jpg"
                                        },
                                        "type": "txt",
                                        "difficulty": "intermediate",
                                        "view_count": 1250,
                                        "favorite_count": 89,
                                        "average_rating": 4.5,
                                        "comment_count": 23,
                                        "cover_image_url": "https://example.com/cover1.jpg",
                                        "is_premium_only": False,
                                        "created_at": "2024-01-01T10:00:00Z",
                                        "tags": ["古典", "钢琴", "经典"]
                                    },
                                    {
                                        "id": 2,
                                        "title": "月光奏鸣曲",
                                        "description_snippet": "贝多芬的经典作品，第一乐章。难度较高，适合有一定基础的演奏者...",
                                        "uploader_info": {
                                            "id": 2,
                                            "username": "pianist",
                                            "nickname": "钢琴家",
                                            "avatar_url": "https://example.com/avatar2.jpg"
                                        },
                                        "type": "midi",
                                        "difficulty": "advanced",
                                        "view_count": 2100,
                                        "favorite_count": 156,
                                        "average_rating": 4.8,
                                        "comment_count": 45,
                                        "cover_image_url": "https://example.com/cover2.jpg",
                                        "is_premium_only": True,
                                        "created_at": "2024-01-02T14:30:00Z",
                                        "tags": ["古典", "贝多芬", "奏鸣曲"]
                                    }
                                ]
                            }
                        },
                        "filtered_list": {
                            "summary": "按分类筛选的谱曲列表",
                            "value": {
                                "meta": {
                                    "page": 1,
                                    "limit": 10,
                                    "total_items": 25,
                                    "total_pages": 3
                                },
                                "data": [
                                    {
                                        "id": 5,
                                        "title": "夜的钢琴曲五",
                                        "description_snippet": "石进的经典钢琴曲，旋律优美动人...",
                                        "uploader_info": {
                                            "id": 3,
                                            "username": "modernpiano",
                                            "nickname": "现代钢琴",
                                            "avatar_url": "https://example.com/avatar3.jpg"
                                        },
                                        "type": "txt",
                                        "difficulty": "beginner",
                                        "view_count": 890,
                                        "favorite_count": 67,
                                        "average_rating": 4.3,
                                        "comment_count": 18,
                                        "cover_image_url": "https://example.com/cover5.jpg",
                                        "is_premium_only": False,
                                        "created_at": "2024-01-05T09:15:00Z",
                                        "tags": ["现代", "钢琴", "抒情"]
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "examples": {
                        "invalid_params": {
                            "summary": "无效的查询参数",
                            "value": {
                                "detail": "Invalid query parameters"
                            }
                        }
                    }
                }
            }
        }
    }
)
def read_scores(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的项目数 (用于分页)"),
    limit: int = Query(20, ge=1, le=100, description="每页返回的项目数上限"),
    sort_by: Optional[str] = Query(
        None,
        description="排序字段 (例如: 'created_at', 'view_count', 'title', 'difficulty')",
    ),
    order: Optional[str] = Query(
        "desc", enum=["asc", "desc"], description="排序方式 ('asc' 或 'desc')"
    ),
    category_id: Optional[int] = Query(None, description="按分类ID筛选"),
    # category_slug: Optional[str] = Query(None, description="按分类slug筛选 (暂未在CRUD实现)"), # CRUD中未实现slug筛选
    uploader_id: Optional[int] = Query(None, description="按上传者用户ID筛选"),
    search: Optional[str] = Query(
        None, alias="title", description="关键词搜索 (标题、描述、标签)"
    ),  # API文档用search, schema用title
    score_type: Optional[models.ScoreType] = Query(
        None, description="按谱曲类型筛选"
    ),  # 使用模型中的Enum
    difficulty: Optional[models.ScoreDifficulty] = Query(
        None, description="按难度筛选"
    ),  # 使用模型中的Enum
    status: Optional[models.ScoreStatus] = Query(
        models.ScoreStatus.approved, description="按谱曲状态筛选 (默认为 approved)"
    ),  # MODIFIED: ScoreStatus to models.ScoreStatus # 默认approved
    is_premium_only: Optional[bool] = Query(None, description="是否仅筛选付费曲谱"),
    tags: Optional[str] = Query(
        None, description="按标签筛选 (逗号分隔的字符串, 例如 'pop,piano')"
    ),
    # current_user: models.User = Depends(deps.get_current_active_user), # 根据权限需求，列表接口可能不需要强制登录
) -> Any:
    """
    获取谱曲列表，支持多种查询参数进行筛选和排序。
    """
    tags_list: Optional[List[str]] = None
    if tags:
        tags_list = [tag.strip() for tag in tags.split(",") if tag.strip()]

    scores, total_count = crud.score.get_multi_with_filtering_and_sorting(
        db,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        order=order,
        category_id=category_id,
        uploader_user_id=uploader_id,
        search_keyword=search,
        score_type=score_type,
        difficulty=difficulty,
        status=status,
        is_premium_only=is_premium_only,
        tags_list=tags_list,
    )

    page = (skip // limit) + 1
    total_pages = (total_count + limit - 1) // limit  # 向上取整计算总页数

    return ScorePaginatedResponse(
        meta=PageMeta(
            page=page,
            limit=limit,
            total_items=total_count,
            total_pages=total_pages,
        ),
        data=[
            ScoreSummaryResponse.model_validate(score) for score in scores
        ],  # 确保转换为 ScoreSummaryResponse
    )


@router.get(
    "/{score_id}",
    response_model=ScoreDetailResponse,  # API 设计文档建议 ScoreDetailResponse
    summary="获取特定谱曲详情",
    description="根据谱曲ID获取其详细信息。如果谱曲未公开或未审核，则可能需要特定权限才能访问。",
)
def read_score_details(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: Optional[models.User] = Depends(
        api_deps.get_current_user
    ),  # 可选用户，用于权限判断
) -> Any:
    """
    获取指定ID的谱曲详情。
    - 访问此接口会增加谱曲的浏览次数。
    - 权限控制：
        - 已批准 (`approved`) 的谱曲通常对所有用户可见。
        - 其他状态 (如 `pending`, `private`, `draft`, `rejected`) 的谱曲，
          通常只对上传者或管理员可见。
    """
    # 使用基础的 get 方法，Pydantic 的 orm_mode 会处理关系字段的序列化
    # 如果需要显式加载，可以在 CRUD 层提供一个 get_with_relations 方法
    # 或者在这里手动查询：
    score = (
        db.query(models.Score)
        .options(
            joinedload(models.Score.uploader),
            joinedload(models.Score.category),
            joinedload(models.Score.approver),
        )
        .filter(models.Score.id == score_id)
        .first()
    )

    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    # 权限检查逻辑
    is_owner = current_user and score.uploader_user_id == current_user.id
    is_admin = (
        current_user and current_user.role == models.UserRole.admin
    )  # 假设 UserRole 定义在 models.user

    if score.status != models.ScoreStatus.approved and not is_owner and not is_admin:
        # 如果谱曲未批准，且当前用户不是所有者也不是管理员，则拒绝访问
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此谱曲的详细信息。",
        )

    # 增加浏览次数 (无论用户是谁，只要能访问到详情就增加)
    # 考虑是否应该在用户实际获取到内容后再增加，或者是否有其他条件
    # 简单处理：只要成功获取到详情就增加
    # 注意: increment_view_count 返回的是更新后的 score 对象，但我们已经有了 score 对象。
    # 可以选择直接使用 score，或者使用返回的 score_with_incremented_view。
    # 为确保数据一致性，最好使用 crud 操作返回的对象。
    score_with_incremented_view = crud.score.increment_view_count(
        db=db, score_id=score_id
    )
    if not score_with_incremented_view:
        # 这种情况理论上不应发生，因为前面已经检查过 score 是否存在
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在 (在增加浏览量时)"
        )

    # 返回更新了浏览次数的谱曲对象
    return score_with_incremented_view


@router.put(
    "/{score_id}",
    response_model=ScoreResponse,  # 或者 ScoreDetailResponse
    summary="更新谱曲信息",
    description="更新指定ID的谱曲信息。需要谱曲上传者或管理员权限。",
)
def update_score(
    score_id: int,
    score_in: ScoreUpdate,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    更新谱曲信息。

    - **score_id**: 要更新的谱曲ID。
    - **score_in**: 包含更新字段的谱曲数据。
    - **current_user**: 当前认证用户，用于权限检查。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    is_owner = score.uploader_user_id == current_user.id
    # is_admin = current_user.role == models.UserRole.admin # 管理员权限检查已移除

    if not is_owner:  # 普通用户只能修改自己的谱曲
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权修改此谱曲"
        )

    # 检查 category_id 是否有效 (如果 score_in 中包含 category_id)
    if score_in.category_id is not None:
        category = crud.category.get(db, id=score_in.category_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为 {score_in.category_id} 的目标分类不存在。",
            )

    # tags 字段的处理已在 ScoreUpdate schema 的 validator 中完成
    updated_score = crud.score.update(db=db, db_obj=score, obj_in=score_in)

    # 为了确保返回的响应包含最新的关联数据（如果分类被更改），
    # Pydantic 的 orm_mode 会处理。如果关系字段未自动刷新，可能需要重新查询或刷新对象。
    # db.refresh(updated_score, attribute_names=['category', 'uploader']) # 示例
    return updated_score


@router.delete(
    "/{score_id}",
    response_model=Msg,  # API 设计文档建议成功消息或 204
    status_code=status.HTTP_200_OK,  # 或者 status.HTTP_204_NO_CONTENT
    summary="删除谱曲",
    description="删除指定ID的谱曲。需要谱曲上传者或管理员权限。",
)
def delete_score(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    删除谱曲。

    - **score_id**: 要删除的谱曲ID。
    - **current_user**: 当前认证用户，用于权限检查。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    is_owner = score.uploader_user_id == current_user.id
    # is_admin = current_user.role == models.UserRole.admin # 管理员权限检查已移除

    if not is_owner:  # 普通用户只能删除自己的谱曲
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权删除此谱曲"
        )

    try:
        crud.score.remove(db=db, id=score_id)
    except Exception as e:  # 例如，如果有关联的约束导致删除失败
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除谱曲失败: {str(e)}",
        )

    # 如果选择返回 204 No Content, 则直接 return Response(status_code=status.HTTP_204_NO_CONTENT)
    return Msg(message="谱曲删除成功")


# 谱曲状态更新端点已迁移到 admin 模块


# --- 用户交互相关端点 (收藏、评分、评论) ---

# --- 谱曲收藏 (UserFavorite) ---


@router.post(
    "/{score_id}/favorite",
    response_model=UserFavoriteResponse,
    status_code=status.HTTP_201_CREATED,
    summary="收藏指定谱曲",
    description="用户收藏一个谱曲。如果已收藏，则不执行任何操作并返回现有收藏信息或错误。",
)
def favorite_score(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    收藏谱曲。
    - **score_id**: 要收藏的谱曲ID。
    - **current_user**: 当前认证用户。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    existing_favorite = crud.user_favorite.get_by_user_and_score(
        db, user_id=current_user.id, score_id=score_id
    )
    if existing_favorite:
        # 根据 API 设计文档 2.9.1 POST /me/favorites/scores/{score_id}：收藏指定谱曲。响应 (成功 201 Created 或 200 OK 如果已收藏)。
        # 此处返回 200 OK 和已有的收藏信息
        return existing_favorite
        # 或者可以抛出 409 Conflict
        # raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="您已收藏此谱曲")

    favorite_create = UserFavoriteCreate(user_id=current_user.id, score_id=score_id)
    created_favorite = crud.user_favorite.create(db, obj_in=favorite_create)
    return created_favorite


@router.delete(
    "/{score_id}/favorite",
    response_model=Msg,  # 或者返回 204 No Content
    status_code=status.HTTP_200_OK,  # 或 status.HTTP_204_NO_CONTENT
    summary="取消收藏指定谱曲",
    description="用户取消对一个谱曲的收藏。",
)
def unfavorite_score(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    取消收藏谱曲。
    - **score_id**: 要取消收藏的谱曲ID。
    - **current_user**: 当前认证用户。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    favorite_to_delete = crud.user_favorite.get_by_user_and_score(
        db, user_id=current_user.id, score_id=score_id
    )
    if not favorite_to_delete:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="您尚未收藏此谱曲"
        )

    crud.user_favorite.remove_by_user_and_score(
        db, user_id=current_user.id, score_id=score_id
    )
    return Msg(message="取消收藏成功")


@router.get(
    "/{score_id}/is_favorited",
    response_model=ScoreIsFavoritedResponse,
    summary="检查谱曲是否已收藏",
    description="检查当前登录用户是否已收藏指定的谱曲。",
)
def check_score_is_favorited(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    检查当前用户是否已收藏某谱曲。
    - **score_id**: 要检查的谱曲ID。
    - **current_user**: 当前认证用户。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    existing_favorite = crud.user_favorite.get_by_user_and_score(
        db, user_id=current_user.id, score_id=score_id
    )
    return ScoreIsFavoritedResponse(
        score_id=score_id, is_favorited=bool(existing_favorite)
    )


# --- 谱曲评分 (UserScoreRating) ---


@router.post(
    "/{score_id}/rate",
    response_model=UserScoreRatingResponse,
    summary="提交或更新谱曲评分",
    description="用户提交或更新对谱曲的评分和可选评论。如果用户已评分，则更新；否则创建新评分。",
)
def rate_score(
    score_id: int,
    rating_in: UserScoreRatingCreate,  # API 设计文档用 UserScoreRatingCreate，它包含 user_id, score_id, rating, comment
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    提交/更新谱曲评分。
    - **score_id**: 要评分的谱曲ID。
    - **rating_in**: 包含评分值和可选评论的数据。
    - **current_user**: 当前认证用户。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    # 确保 rating_in 中的 user_id 和 score_id 与路径参数和当前用户一致
    if rating_in.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="不能代表其他用户评分"
        )
    if rating_in.score_id != score_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请求体中的谱曲ID与路径参数不匹配",
        )

    # CRUD 操作中 create 会处理更新逻辑
    created_or_updated_rating = crud.user_score_rating.create(db, obj_in=rating_in)
    return created_or_updated_rating


@router.get(
    "/{score_id}/ratings_summary",
    response_model=ScoreRatingSummaryResponse,
    summary="获取谱曲的平均评分和评分总数",
    description="获取指定谱曲的平均评分和总评分人数。",
)
def get_score_ratings_summary(
    score_id: int,
    db: Session = Depends(api_deps.get_db),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口或需用户登录
) -> Any:
    """
    获取谱曲的评分摘要。
    - **score_id**: 谱曲ID。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    avg_rating, total_ratings = crud.user_score_rating.get_average_rating_for_score(
        db, score_id=score_id
    )

    return ScoreRatingSummaryResponse(
        score_id=score_id, average_rating=avg_rating, total_ratings=total_ratings
    )


# --- 谱曲评论 (ScoreComment) ---


@router.post(
    "/{score_id}/comments/",  # 注意末尾的斜杠以匹配 API 设计
    response_model=ScoreCommentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="发表谱曲评论",
    description="用户为指定的谱曲发表一条评论。可以回复已有评论 (通过 parent_comment_id)。",
)
def create_score_comment(
    score_id: int,
    comment_in: ScoreCommentCreate,  # API 设计文档用 ScoreCommentCreate
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    发表谱曲评论。
    - **score_id**: 要评论的谱曲ID。
    - **comment_in**: 评论内容，可选的父评论ID。
    - **current_user**: 当前认证用户。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    # 确保 comment_in 中的 user_id 和 score_id 与路径参数和当前用户一致
    if comment_in.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="不能代表其他用户发表评论"
        )
    if comment_in.score_id != score_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请求体中的谱曲ID与路径参数不匹配",
        )

    if comment_in.parent_comment_id:
        parent_comment = crud.score_comment.get(db, id=comment_in.parent_comment_id)
        if not parent_comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="要回复的父评论不存在"
            )
        if parent_comment.score_id != score_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="父评论不属于当前谱曲"
            )

    created_comment = crud.score_comment.create(db, obj_in=comment_in)
    return created_comment


@router.get(
    "/{score_id}/comments/",  # 注意末尾的斜杠
    response_model=List[
        ScoreCommentResponse
    ],  # API 设计文档建议 List[ScoreCommentResponse]
    summary="获取谱曲的评论列表",
    description="获取指定谱曲的评论列表，支持分页和获取特定父评论下的子评论。",
)
def read_score_comments(
    score_id: int,
    skip: int = Query(0, ge=0, description="跳过的评论数"),
    limit: int = Query(20, ge=1, le=100, description="每页返回的评论数上限"),
    parent_id: Optional[int] = Query(None, description="父评论ID (用于获取子评论)"),
    db: Session = Depends(api_deps.get_db),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口或需用户登录
) -> Any:
    """
    获取谱曲的评论列表。
    - **score_id**: 谱曲ID。
    - **skip**: 分页参数。
    - **limit**: 分页参数。
    - **parent_id**: 如果提供，则获取该父评论的直接子回复。否则获取顶级评论。
    """
    score = crud.score.get(db, id=score_id)
    if not score:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="谱曲不存在")

    comments = crud.score_comment.get_multi_by_score(
        db,
        score_id=score_id,
        skip=skip,
        limit=limit,
        parent_id=parent_id,
        include_replies=True,
    )
    return comments


# 注意 API 设计文档中更新和删除评论的路径是 /comments/{comment_id}
# 这意味着这些端点可能更适合放在一个专门的 comments_router.py 文件中
# 或者，如果保持在 scores.py 中，路径可以是 /scores/{score_id}/comments/{comment_id}
# 根据用户任务中 "谱曲评论 (通常路径为 /api/v1/scores/{score_id}/comments/..., /api/v1/comments/{comment_id})"
# 我将采用 /api/v1/scores/{score_id}/comments/{comment_id} 这种更明确的路径


@router.put(
    "/{score_id}/comments/{comment_id}",  # 调整路径以包含 score_id
    response_model=ScoreCommentResponse,
    summary="更新谱曲评论",
    description="用户更新自己发表的评论内容。",
)
def update_score_comment(
    score_id: int,  # 添加 score_id 路径参数
    comment_id: int,
    comment_in: ScoreCommentUpdate,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    更新评论。
    - **score_id**: 谱曲ID (用于验证评论是否属于此谱曲)。
    - **comment_id**: 要更新的评论ID。
    - **comment_in**: 更新的评论内容。
    - **current_user**: 当前认证用户，必须是评论的发布者。
    """
    comment = crud.score_comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论不存在")

    if comment.score_id != score_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="评论不属于指定的谱曲"
        )

    if comment.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权修改此评论"
        )

    # 只允许用户更新 content，status 由管理员通过 PATCH /status 更新
    if comment_in.status is not None:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="用户不能直接修改评论状态"
        )

    # 确保只传递 content 给 update
    update_data = ScoreCommentUpdate(content=comment_in.content)

    updated_comment = crud.score_comment.update(db, db_obj=comment, obj_in=update_data)
    return updated_comment


@router.delete(
    "/{score_id}/comments/{comment_id}",  # 调整路径
    response_model=Msg,  # 或 204 No Content
    status_code=status.HTTP_200_OK,
    summary="删除谱曲评论",
    description="用户删除自己发表的评论，或管理员删除任意评论。",
)
def delete_score_comment(
    score_id: int,  # 添加 score_id 路径参数
    comment_id: int,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(api_deps.get_current_active_user),
) -> Any:
    """
    删除评论。
    - **score_id**: 谱曲ID。
    - **comment_id**: 要删除的评论ID。
    - **current_user**: 当前认证用户。评论发布者或管理员可删除。
    """
    comment = crud.score_comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论不存在")

    if comment.score_id != score_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="评论不属于指定的谱曲"
        )

    is_owner = comment.user_id == current_user.id
    # is_admin = current_user.role == models.UserRole.admin # 管理员权限检查已移除

    if not is_owner:  # 普通用户只能删除自己的评论
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无权删除此评论"
        )

    crud.score_comment.remove(db, id=comment_id)
    return Msg(message="评论删除成功")


# 评论状态更新端点已迁移到 admin 模块
