# app/apis/v1/endpoints/attachments.py
import shutil
import uuid
from pathlib import Path as FilePathLib  # 修改导入
from typing import Optional, Annotated, Any  # Annotated 需要 Python 3.9+
import random  # 用于模拟 db_id
from datetime import datetime, timezone  # 用于模拟 created_at

from fastapi import (
    APIRouter,
    File,
    UploadFile,
    Depends,
    HTTPException,
    Form,
    Query,
    Path,
)  # 添加 Path
from sqlalchemy.orm import Session

from app.schemas.attachment import AttachmentUploadResponse  # 更新导入
from app.apis import deps as api_deps  # 导入依赖
from app import models  # MODIFIED: Added unified models import
# MODIFIED: Removed specific UserModel import
# from app.db.models.attachment import Attachment as AttachmentModel # 假设模型名为 Attachment
# from app.crud.crud_attachment import crud_attachment # 假设有这个 CRUD

router = APIRouter()

# 定义允许的上传文件类型和最大大小 (可以从 config.py 读取)
# 为了简单起见，暂时硬编码，后续可以移到 config.py
ALLOWED_EXTENSIONS = {
    "png",
    "jpg",
    "jpeg",
    "gif",
    "pdf",
    "mid",
    "midi",
    "txt",
    "doc",
    "docx",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
}  # 扩展了允许类型
MAX_FILE_SIZE_MB = 20  # MB, 稍微增大一些
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

# 定义基础上传路径 (可以从 config.py 读取)
# 确保这个路径存在，或者在应用启动时创建它
BASE_UPLOAD_DIR = FilePathLib("static/uploads")  # 相对于项目根目录，使用别名


def get_file_extension(filename: str) -> str:
    """获取文件扩展名(小写)"""
    return filename.split(".")[-1].lower() if "." in filename else ""


@router.post(
    "/upload",
    response_model=AttachmentUploadResponse,  # 更新 response_model
    summary="通用文件上传接口",
    description="上传单个文件，返回文件信息。可指定用途类型以便分类存储。",
)
def upload_attachment(
    file: UploadFile = File(..., description="要上传的文件"),
    usage_type: Annotated[
        Optional[str],
        Form(
            description="文件用途类型 (例如: avatar, ticket_attachment, score_cover, score_midi)"
        ),
    ] = None,
    related_object_type: Annotated[
        Optional[str], Form(description="关联对象类型 (例如: ticket_reply)")
    ] = None,  # 新增
    related_object_id: Annotated[
        Optional[int], Form(description="关联对象ID")
    ] = None,  # 新增
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),  # 假设需要DB操作
) -> Any:
    """
    通用文件上传端点。

    - **file**: 要上传的文件。
    - **usage_type**: 文件用途分类，例如 `avatar`, `ticket_attachment`, `score_cover`, `score_preview_image`。
                      这会影响文件存储的子目录。
    - **权限**: 需要用户登录。
    """
    original_filename = file.filename
    if not original_filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")

    file_extension = get_file_extension(original_filename)
    if file_extension not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型: {file_extension}. 允许的类型: {', '.join(ALLOWED_EXTENSIONS)}",
        )

    # 检查文件大小
    contents = file.file.read()  # 直接读取内容
    file_size = len(contents)
    file.file.seek(0)  # 重置文件指针，以便后续保存

    if file_size > MAX_FILE_SIZE_BYTES:
        raise HTTPException(
            status_code=413,  # Payload Too Large
            detail=f"文件大小超过限制 ({MAX_FILE_SIZE_MB} MB)。上传文件大小: {file_size / (1024*1024):.2f} MB",
        )

    # 根据 usage_type 创建子目录
    # 清理 usage_type，防止路径遍历漏洞，例如只允许字母数字下划线和短横线
    safe_usage_type_str = "general"  # 默认目录
    if usage_type:
        cleaned_usage_type = "".join(
            c if c.isalnum() or c in ["_", "-"] else "" for c in usage_type.strip()
        )
        if cleaned_usage_type:  # 如果清理后不为空
            safe_usage_type_str = cleaned_usage_type

    upload_subdir_path = BASE_UPLOAD_DIR / safe_usage_type_str  # 使用别名

    # 创建上传目录 (如果不存在)
    upload_subdir_path.mkdir(parents=True, exist_ok=True)

    # 生成唯一文件名
    unique_id = uuid.uuid4()
    new_filename = (
        f"{str(unique_id)}.{file_extension}"  # 使用UUID和原始扩展名构成新文件名
    )

    file_path_on_server = upload_subdir_path / new_filename  # 使用别名

    try:
        with open(file_path_on_server, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        # 处理可能的保存文件错误
        # logger.error(f"保存文件失败: {e}", exc_info=True) # 实际项目中应使用日志
        raise HTTPException(status_code=500, detail=f"无法保存文件: {str(e)}")
    finally:
        file.file.close()  # 确保关闭文件

    # 构建文件访问 URL (相对于应用根目录)
    # 假设 static 目录已在 main.py 中挂载为 /static
    # 路径应为 /static/uploads/{safe_usage_type_str}/{new_filename}
    file_url = f"/static/uploads/{safe_usage_type_str}/{new_filename}"

    # (可选) 在数据库中记录附件信息
    # 例如，创建一个 Attachment 模型和 CRUD 操作
    # attachment_record = {
    #     "file_id": str(unique_id),
    #     "original_filename": original_filename,
    #     "stored_filename": new_filename,
    #     "file_path_on_server": str(file_path_on_server.resolve()), # 存储服务器上的绝对路径
    #     "url": file_url,
    #     "content_type": file.content_type,
    #     "size_bytes": file_size,
    #     "uploaded_by_user_id": current_user.id,
    #     "usage_type": safe_usage_type_str,
    #     "created_at": datetime.utcnow()
    # }
    # db_attachment = crud_attachment.create(db=db, obj_in=attachment_record) # 假设有 crud_attachment

    # 模拟数据库记录创建，因为 crud_attachment 未提供
    # 在实际应用中，这里应该调用 crud_attachment.create 来保存附件信息到数据库
    # db_attachment = crud_attachment.create(db, obj_in=attachment_create_schema)
    mock_db_id = random.randint(1, 10000)  # 模拟数据库ID
    mock_created_at = datetime.now(timezone.utc)

    return AttachmentUploadResponse(
        db_id=mock_db_id,
        file_id=str(unique_id),
        filename=original_filename,
        url=file_url,
        file_type=file.content_type or "application/octet-stream",
        file_size_bytes=file_size,
        related_object_type=related_object_type,
        related_object_id=related_object_id,
        uploader_user_id=current_user.id,
        created_at=mock_created_at,
    )


def upload_file_for_category(
    category_id: int = Path(..., description="关联的分类ID"),
    file: UploadFile = File(..., description="上传的文件"),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
    file_type: str = Query(
        "image",
        description="文件类型，如 'image', 'audio', 'video', 'sheet_music', 'other'",
    ),
):
    pass


def upload_file_for_score(
    score_id: int = Path(..., description="关联的乐谱ID"),
    file: UploadFile = File(..., description="上传的文件"),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
    file_type: str = Query(
        "image",
        description="文件类型，如 'image', 'audio', 'video', 'sheet_music', 'other'",
    ),
):
    pass


def upload_file_for_user_profile(
    file: UploadFile = File(..., description="上传的用户头像文件"),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    pass


def get_attachment_info(
    attachment_uuid: str = Path(..., description="附件的UUID"),
    db: Session = Depends(api_deps.get_db),
):
    pass


def serve_attachment(
    attachment_uuid: str = Path(..., description="附件的UUID"),
    db: Session = Depends(api_deps.get_db),
):
    pass


def delete_attachment_by_uuid(
    attachment_uuid: str = Path(..., description="要删除的附件的UUID"),
    current_user: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User # 假设只有管理员可以删除
    db: Session = Depends(api_deps.get_db),
):
    pass
