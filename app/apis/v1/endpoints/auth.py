# TanZouDaShiAPI/app/apis/v1/endpoints/auth.py
from typing import Any, Union, Optional
from datetime import timedelta, datetime, timezone

from app import models  # MODIFIED: Unified model import
from fastapi import APIRouter, Depends, HTTPException, status, Body
from jose import JWTError
from sqlalchemy.orm import Session

# from app import crud # 导入 crud (已移除)
# 修改为直接导入 CRUD 实例
from app.crud.crud_user import user as crud_user
from app.crud.crud_license import license_key as crud_license_key
from app.crud.crud_device import user_bound_device as crud_user_bound_device
from app.crud.crud_session_log import user_session_log as crud_user_session_log

# 直接从具体的 schema 文件导入所需的模型
from app.schemas.auth import (
    LoginPasswordRequest,
    LoginLicenseRequest,
    LoginResponse,
    LoginLicenseActionRequiredData,
    LoginLicenseActionRequiredResponse,
    RegisterRequest,
    RegisterResponse,
    LogoutRequest,
    PasswordResetRequestRequest,
    PasswordResetExecuteRequest,
    VerificationCodeSendRequest,
    HeartbeatRequest,
    ActivateLicenseAccountRequest,
    ActivateLicenseAccountResponse,
    ActivateLicenseAccountUserInfo,
)
from app.schemas.token import RefreshTokenRequest, RefreshTokenResponse
from app.schemas.msg import Msg
from app.schemas.user import UserResponse, UserCreate
from app.schemas.license import LicenseInfoBase, ActivateLicenseAccountLicenseInfo
from app.schemas.device import DeviceCreate
from app.apis import deps as api_deps  # 导入依赖
from app.core import security  # 导入安全模块
from app.core.config import settings  # 导入配置

router = APIRouter()


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="用户登录",
    description="支持用户名/密码登录和卡密登录。登录时会检查设备绑定状态。",
    responses={
        200: {
            "description": "登录成功",
            "content": {
                "application/json": {
                    "examples": {
                        "password_login_success": {
                            "summary": "用户名密码登录成功",
                            "value": {
                                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.example",
                                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.example",
                                "session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.example",
                                "token_type": "Bearer",
                                "expires_in": 1800,
                                "user_info": {
                                    "id": 1,
                                    "username": "testuser",
                                    "nickname": "测试用户",
                                    "email": "<EMAIL>",
                                    "phone_number": "13800138000",
                                    "avatar_url": "https://example.com/avatar.jpg",
                                    "max_concurrent_devices": 3,
                                    "permanent_license_unbind_count_current_month": 0,
                                    "role": "user",
                                    "status": "active",
                                    "email_verified_at": "2024-01-01T10:00:00Z",
                                    "created_at": "2024-01-01T10:00:00Z"
                                },
                                "license_info": {
                                    "id": 1,
                                    "key_string": "ABCD-EFGH-IJKL-MNOP",
                                    "license_type_name": "月卡",
                                    "license_type_code": "monthly",
                                    "expires_at": "2024-02-01T10:00:00Z",
                                    "status": "used"
                                },
                                "device_status": "current_device_active"
                            }
                        },
                        "license_login_success": {
                            "summary": "卡密登录成功",
                            "value": {
                                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.example",
                                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.example",
                                "session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************.example",
                                "token_type": "Bearer",
                                "expires_in": 1800,
                                "user_info": {
                                    "id": 2,
                                    "username": "licenseuser",
                                    "nickname": "卡密用户",
                                    "email": "<EMAIL>",
                                    "phone_number": None,
                                    "avatar_url": None,
                                    "max_concurrent_devices": 5,
                                    "permanent_license_unbind_count_current_month": 1,
                                    "role": "user",
                                    "status": "active",
                                    "email_verified_at": None,
                                    "created_at": "2024-01-02T15:30:00Z"
                                },
                                "license_info": {
                                    "id": 2,
                                    "key_string": "WXYZ-1234-5678-9ABC",
                                    "license_type_name": "年卡",
                                    "license_type_code": "yearly",
                                    "expires_at": "2025-01-02T15:30:00Z",
                                    "status": "used"
                                },
                                "device_status": "new_device_auto_bound"
                            }
                        }
                    }
                }
            }
        },
        401: {
            "description": "认证失败",
            "content": {
                "application/json": {
                    "examples": {
                        "invalid_credentials": {
                            "summary": "用户名或密码错误",
                            "value": {
                                "detail": "用户名或密码不正确"
                            }
                        },
                        "invalid_license": {
                            "summary": "无效的激活码",
                            "value": {
                                "detail": "激活码无效或不存在"
                            }
                        }
                    }
                }
            }
        },
        403: {
            "description": "账户被禁用或设备绑定已满",
            "content": {
                "application/json": {
                    "examples": {
                        "account_disabled": {
                            "summary": "账户被禁用",
                            "value": {
                                "detail": "用户账户已被禁用或未激活"
                            }
                        },
                        "max_devices_reached": {
                            "summary": "设备绑定已满",
                            "value": {
                                "detail": "已达到最大绑定设备数，请先解绑其他设备。"
                            }
                        }
                    }
                }
            }
        }
    }
)
def login(
    db: Session = Depends(api_deps.get_db),
    login_data: Union[LoginPasswordRequest, LoginLicenseRequest] = Body(
        ..., description="登录凭据"
    ),
) -> Any:
    """
    用户登录接口:
    - **login_type: "password"**: 使用用户名和密码登录。
    - **login_type: "license"**: 使用卡密登录。
        - 如果卡密有效但未激活，将引导用户至激活接口。
    - **device_id**: 必填，用于设备绑定检查。
    """
    user: Optional[models.User] = None  # MODIFIED: DBUser to models.User
    license_key_obj: Optional[models.LicenseKey] = None  # 显式指定类型

    if login_data.login_type == "password":
        # 用户名密码登录
        if not isinstance(login_data, LoginPasswordRequest):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="登录类型为 'password' 时，请求体格式不正确。",
            )
        user = crud_user.authenticate_user(
            db, username=login_data.username, password=login_data.password
        )
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码不正确",
                headers={"WWW-Authenticate": "Bearer"},
            )
    elif login_data.login_type == "license":
        # 卡密登录
        if not isinstance(login_data, LoginLicenseRequest):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="登录类型为 'license' 时，请求体格式不正确。",
            )
        license_key_obj = crud_license_key.get_license_key_by_string(
            db, key_string=login_data.key_string
        )
        if not license_key_obj:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="激活码无效或不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if license_key_obj.status == models.LicenseKeyStatus.available or (
            license_key_obj.consignment_reseller_id is not None
            and license_key_obj.status == models.LicenseKeyStatus.consigned_available
        ):  # MODIFIED: LicenseKeyStatus
            if license_key_obj.user_id is None:  # 确保是未关联用户的卡密
                # 卡密有效但未激活，引导至激活接口
                return LoginLicenseActionRequiredResponse(
                    code=40301,  # 自定义错误码
                    message="激活码有效但尚未激活，请先激活账户。",
                    data=LoginLicenseActionRequiredData(
                        action_required="activate_license_account",
                        key_string=login_data.key_string,
                    ),
                )

        if (
            license_key_obj.status != models.LicenseKeyStatus.used
            or not license_key_obj.user_id
        ):  # MODIFIED: LicenseKeyStatus
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="激活码无效、已过期、已禁用或未关联账户",
                headers={"WWW-Authenticate": "Bearer"},
            )
        user = crud_user.get(db, id=license_key_obj.user_id)
        if not user:
            # 理论上卡密关联的用户应该存在，除非数据不一致
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="与激活码关联的用户数据异常",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="无效的登录类型 login_type"
        )

    if not user:  # 再次检查 user 是否已成功获取
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证用户身份",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用或未激活",
        )

    # 设备检查与绑定逻辑
    current_bound_devices = crud_user_bound_device.get_user_bound_devices(
        db, user_id=user.id
    )
    device_id_str = login_data.device_id  # device_id 是字符串

    existing_device_binding = crud_user_bound_device.get_user_bound_device(
        db, user_id=user.id, device_id=device_id_str
    )

    device_status_response: str
    session_token = security.create_access_token(
        subject=f"session_for_user_{user.id}_device_{device_id_str}",
        expires_delta=timedelta(days=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 100),
    )  # Session token 可以更长效

    if existing_device_binding:
        # 设备已绑定
        crud_user_bound_device.update_bound_device_activity(
            db,
            db_obj=existing_device_binding,
            session_token=session_token,
            # ip_address 和 user_agent 可以从请求头获取，这里暂时省略
        )
        device_status_response = "current_device_active"
        # 创建会话日志
        crud_user_session_log.create_user_session_log(
            db, user_id=user.id, device_id=device_id_str
        )

    else:
        # 新设备
        if len(current_bound_devices) < (user.max_concurrent_devices or 1):
            # 自动绑定新设备
            device_create_schema = DeviceCreate(
                device_id=device_id_str, device_alias=f"新设备 {device_id_str[:8]}"
            )
            crud_user_bound_device.create_user_bound_device(
                db,
                obj_in=device_create_schema,
                user_id=user.id,
                session_token=session_token,
                # ip_address 和 user_agent 可以从请求头获取
            )
            device_status_response = "new_device_auto_bound"
            # 创建会话日志
            crud_user_session_log.create_user_session_log(
                db, user_id=user.id, device_id=device_id_str
            )
        else:
            # 绑定名额已满
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,  # 429 也可以
                detail="已达到最大绑定设备数，请先解绑其他设备。",
                # error_code="MAX_BOUND_DEVICES_REACHED" # 如果有统一错误码结构
            )

    # 生成 JWT
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=user.id, expires_delta=access_token_expires
    )
    # 简单起见，refresh_token 也用类似方式生成，但通常有不同生命周期和存储策略
    refresh_token_expires = timedelta(days=7)
    refresh_token = security.create_access_token(
        subject=user.id,
        expires_delta=refresh_token_expires,  # refresh token 通常有不同的 subject 或 claim
    )

    user_info_response = UserResponse.model_validate(user)

    # 获取用户的有效卡密信息 (如果存在)
    active_license_info_response: Optional[LicenseInfoBase] = None
    if (
        license_key_obj and license_key_obj.status == models.LicenseKeyStatus.used
    ):  # MODIFIED: LicenseKeyStatus # 如果是卡密登录且卡密已使用
        active_license_info_response = LicenseInfoBase.model_validate(license_key_obj)
    else:  # 尝试查找用户当前激活的卡密
        user_active_license = crud_license_key.get_user_active_license(
            db, user_id=user.id
        )
        if user_active_license:  # 添加了 None 检查
            active_license_info_response = LicenseInfoBase.model_validate(
                user_active_license
            )
            if user_active_license.license_type:  # 确保 license_type 被加载
                active_license_info_response.license_type_name = (
                    user_active_license.license_type.name
                )
                active_license_info_response.license_type_code = (
                    user_active_license.license_type.code
                )

    return LoginResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        session_token=session_token,
        token_type="Bearer",
        expires_in=int(access_token_expires.total_seconds()),
        user_info=user_info_response,
        license_info=active_license_info_response,
        device_status=device_status_response,
    )


@router.post(
    "/register",
    response_model=RegisterResponse,
    summary="用户注册",
    description="新用户注册接口。",
)
def register(
    *,
    db: Session = Depends(api_deps.get_db),
    user_in: RegisterRequest,
    # device_id: str = Body(..., embed=True, description="设备ID，注册后可能需要自动登录或记录") # 注册时是否需要device_id取决于业务逻辑
) -> Any:
    """
    用户注册:
    - 校验用户名和邮箱是否已存在。
    - 创建新用户。
    - 注册成功后，返回用户信息和JWT令牌 (类似登录)。
    """
    # 检查用户名是否已存在
    existing_user_by_username = crud_user.get_user_by_username(
        db, username=user_in.username
    )
    if existing_user_by_username:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="用户名已被注册，请尝试其他用户名。",
        )
    # 检查邮箱是否已存在 (如果邮箱非空)
    if user_in.email:
        existing_user_by_email = crud_user.get_user_by_email(db, email=user_in.email)
        if existing_user_by_email:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="电子邮箱已被注册，请尝试其他邮箱。",
            )

    # 验证码逻辑 (如果 verification_code 存在)
    # 此处暂时省略验证码校验逻辑，根据项目需求自行实现
    # if user_in.verification_code:
    #     is_valid_code = verify_code(user_in.email or user_in.phone_number, user_in.verification_code, "register")
    #     if not is_valid_code:
    #         raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="验证码无效或已过期。")

    # 创建用户
    # UserCreate schema 可能需要从 RegisterRequest 转换，或 RegisterRequest 直接兼容 UserCreate
    user_create_schema = UserCreate(**user_in.model_dump())
    new_user = crud_user.create_user(db, obj_in=user_create_schema)

    # 注册成功后，类似登录，生成令牌并返回
    # 注意：新注册用户通常没有绑定设备，除非注册后立即登录并绑定
    # 这里简化处理，不立即处理设备绑定，用户需要通过 /login 接口登录以绑定设备和获取 session_token

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=new_user.id, expires_delta=access_token_expires
    )
    refresh_token_expires = timedelta(days=7)
    refresh_token = security.create_access_token(
        subject=new_user.id, expires_delta=refresh_token_expires
    )

    user_info_response = UserResponse.model_validate(new_user)

    # 新注册用户通常没有激活的卡密信息，除非注册流程中包含卡密激活
    # license_info 字段将为 None

    return RegisterResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        session_token=None,  # 新注册不自动生成会话令牌，需通过登录获取
        token_type="Bearer",
        expires_in=int(access_token_expires.total_seconds()),
        user_info=user_info_response,
        license_info=None,
        device_status="not_applicable",  # 注册时不涉及设备状态
    )


@router.post(
    "/refresh-token",
    response_model=RefreshTokenResponse,
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌。",
)
def refresh_token(
    refresh_token_data: RefreshTokenRequest, db: Session = Depends(api_deps.get_db)
) -> Any:
    """
    刷新访问令牌:
    - 验证 `refresh_token` 的有效性。
    - 如果有效，生成新的 `access_token`。
    """
    try:
        payload = security.decode_access_token(refresh_token_data.refresh_token)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌 (解码失败)",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user_id_str = payload.get("sub")
        if user_id_str is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌 (用户标识不存在)",
                headers={"WWW-Authenticate": "Bearer"},
            )

        try:
            user_id = int(user_id_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌 (用户标识格式错误)",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user = crud_user.get(db, id=user_id)
        if not user or user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌 (用户不存在或非活动状态)",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 刷新令牌通常也应有过期时间，security.decode_access_token 会处理 ExpiredSignatureError
        # 此处可以添加额外的逻辑，例如检查刷新令牌是否在黑名单中 (如果实现了黑名单机制)

        new_access_token_expires = timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        new_access_token = security.create_access_token(
            subject=user.id, expires_delta=new_access_token_expires
        )
        return RefreshTokenResponse(
            access_token=new_access_token,
            token_type="Bearer",
            expires_in=int(new_access_token_expires.total_seconds()),
        )

    except JWTError:  # 尽管 decode_access_token 内部处理，但以防万一
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌处理时发生JWT错误",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post(
    "/logout", summary="用户登出", description="用户登出，使其会话和令牌失效。"
)
def logout(
    logout_data: LogoutRequest,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: DBUser to models.User # 确保用户已认证且活动
) -> Msg:
    """
    用户登出:
    - 需要有效的 `access_token` (通过 `current_user` 依赖实现)。
    - 根据请求中的 `session_token` 清除 `user_bound_devices` 中对应设备的 `session_token`。
    - 更新 `user_session_logs` 中对应会话的结束时间。
    - (可选) 将 `access_token` 和 `refresh_token` (如果提供) 加入黑名单。
    """
    # 1. 根据 session_token 从 user_bound_devices 表中找到对应的设备记录
    bound_device = crud_user_bound_device.get_by_session_token(
        db, session_token=logout_data.session_token
    )

    if not bound_device or bound_device.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的会话令牌或会话不属于当前用户。",
        )

    # 2. 清除该记录的 session_token
    crud_user_bound_device.update_bound_device_activity(
        db, db_obj=bound_device, session_token=""
    )  # 设置为空字符串或NULL

    # 3. 查找 user_session_logs 中与当前用户和设备相关的最新一条未结束的会话
    # device_id 应该从 bound_device 对象中获取
    latest_session_log = crud_user_session_log.get_latest_active_session(
        db, user_id=current_user.id, device_id=bound_device.device_id
    )

    # 4. 更新该会话记录的 session_end_at 和 duration_seconds
    if latest_session_log:
        crud_user_session_log.end_user_session_log(
            db, session_log_id=latest_session_log.id
        )

    # 5. 将 access_token 和 (如果提供了) refresh_token 加入黑名单 (这部分需要另外的黑名单机制实现)
    # 例如:
    # token_to_blacklist = get_token_from_request_header() # 假设有方法获取当前请求的 access_token
    # add_token_to_blacklist(token_to_blacklist, expires_at=...)
    # if logout_data.refresh_token:
    #     add_token_to_blacklist(logout_data.refresh_token, expires_at=...)
    # 此处仅为示意，实际黑名单实现可能涉及 Redis 或数据库表

    return Msg(message="成功登出")


@router.post(
    "/password/request-reset",
    response_model=Msg,
    summary="请求密码重置",
    description="用户通过邮箱请求重置密码。",
)
def request_password_reset(
    request_data: PasswordResetRequestRequest, db: Session = Depends(api_deps.get_db)
) -> Any:
    """
    请求密码重置:
    - 验证邮箱是否存在。
    - 生成密码重置令牌/验证码，并通过某种方式发送给用户 (发送逻辑暂时留空或模拟)。
    """
    user = crud_user.get_user_by_email(db, email=request_data.email)
    if not user:
        # 为了安全，即使邮箱不存在，也可能返回成功消息，避免泄露用户信息
        # 但根据文档，这里是 404 Not Found
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="该邮箱地址未注册。",
        )

    # 生成密码重置令牌的逻辑
    # 1. 生成一个有时效性的、唯一的令牌 (例如 JWT 或随机字符串)
    # 2. 将令牌与 user.id 关联存储 (例如在数据库新表 password_reset_tokens，或 Redis)
    #    password_reset_tokens(token_hash, user_id, expires_at, used_at)
    # 3. 发送包含此令牌的邮件给用户 (此步骤暂时模拟)

    # 模拟令牌生成和存储
    # password_reset_token = security.create_access_token(subject=user.email, expires_delta=timedelta(hours=1))
    # print(f"模拟发送密码重置邮件给 {user.email}，令牌: {password_reset_token}")
    # store_password_reset_token(db, user_id=user.id, token=password_reset_token, expires_in_hours=1)

    # 此处仅打印日志模拟发送
    print(f"模拟：为用户 {user.email} (ID: {user.id}) 生成密码重置请求。")
    # TODO: 实现实际的令牌生成、存储和邮件发送逻辑

    return Msg(message="如果您的邮箱已注册，一封包含密码重置说明的邮件将会发送给您。")


@router.post(
    "/password/reset",
    response_model=Msg,
    summary="执行密码重置",
    description="用户使用重置令牌或验证码设置新密码。",
)
def reset_password(
    reset_data: PasswordResetExecuteRequest, db: Session = Depends(api_deps.get_db)
) -> Any:
    """
    执行密码重置:
    - 验证重置令牌/验证码的有效性。
    - 如果有效，更新用户密码。
    """
    # 验证重置令牌的逻辑
    # 1. 从数据库或 Redis 中查找 reset_data.reset_token。
    # 2. 检查令牌是否存在、是否过期、是否已被使用。
    # 3. 如果令牌有效，获取关联的 user_id。

    # 模拟令牌验证和用户获取
    # user_id_from_token = validate_password_reset_token(db, token=reset_data.reset_token)
    # if not user_id_from_token:
    #     raise HTTPException(
    #         status_code=status.HTTP_400_BAD_REQUEST,
    #         detail="密码重置令牌无效或已过期。",
    #     )
    # user = crud.user.get(db, id=user_id_from_token)

    # 此处为模拟逻辑，实际应从令牌获取用户
    # 假设令牌直接包含 email 或 user_id (不推荐直接包含敏感信息，应通过DB间接查找)
    # 为了演示，我们假设 reset_token 是一个可以直接用来查找用户的标识符（例如，一个临时的、与用户关联的验证码）
    # 这是一个非常简化的模拟，真实场景下令牌验证会更复杂。

    # ！！！重要提示：以下为高度简化的模拟逻辑，真实场景需要安全地处理令牌 ！！！
    # 假设 reset_token 是某种形式的 user_identifier 或需要通过一个专门的表来验证
    # 这里我们先假设 reset_token 可以解析出用户，或者可以通过其他方式找到用户
    # 例如，如果 reset_token 是之前发送给特定邮箱的，我们可能需要一个临时表存储 (token, email, expires_at)

    # 模拟：假设令牌验证成功，并能找到用户 (例如，通过一个临时的 reset_tokens 表)
    # user = crud.user.get_user_by_some_reset_token_logic(db, reset_token=reset_data.reset_token) # 伪代码

    # 由于没有实现令牌存储和验证逻辑，这里暂时无法直接获取用户
    # 我们将暂时基于一个假设的用户进行操作，或者提示这部分需要完整实现

    # 模拟：假设我们通过令牌找到了邮箱，再通过邮箱找用户 (这不安全，令牌应直接关联用户ID)
    # decoded_email_from_token = decode_temp_token_for_email(reset_data.reset_token) # 伪代码
    # user = crud.user.get_user_by_email(db, email=decoded_email_from_token) # 伪代码

    # 鉴于当前没有令牌验证机制，此端点暂时无法完整工作。
    # 我们将返回一个提示消息，表明此功能需要完整的令牌验证。
    # 如果要强行模拟，需要一个已知用户ID。

    # 模拟找到用户 (例如，通过ID=1的用户进行测试)
    user_for_reset: Optional[models.User] = crud_user.get(
        db, id=1
    )  # MODIFIED: DBUser to models.User # 这是一个不安全的模拟！
    if not user_for_reset:
        # 真实场景：如果令牌无效或找不到用户
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="密码重置令牌无效或已过期。",
        )

    # 验证新密码强度等 (如果需要)
    # if not is_password_strong_enough(reset_data.new_password):
    #     raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="新密码不符合强度要求。")

    # 更新用户密码
    hashed_password = security.get_password_hash(reset_data.new_password)

    # crud.user.update(db, db_obj=user_for_reset, obj_in=user_update_data) # CRUDBase.update 不直接处理 password_hash
    # 需要一个专门更新密码的 CRUD 方法或直接操作模型
    user_for_reset.password_hash = hashed_password
    db.add(user_for_reset)
    db.commit()
    db.refresh(user_for_reset)

    # 标记重置令牌为已使用 (如果实现了令牌存储)
    # mark_reset_token_as_used(db, token=reset_data.reset_token)

    print(
        f"模拟：用户 {user_for_reset.username} (ID: {user_for_reset.id}) 已通过令牌重置密码。"
    )
    # TODO: 实现安全的令牌验证和用户查找逻辑。

    return Msg(message="密码已成功重置。")


@router.post(
    "/verification-code/send",
    response_model=Msg,
    summary="发送验证码",
    description="发送用于注册、登录、操作验证等的验证码。",
)
def send_verification_code(
    request_data: VerificationCodeSendRequest,
    _: Session = Depends(api_deps.get_db),  # db 依赖可能用于记录发送行为或检查用户状态
) -> Any:
    """
    发送验证码:
    - 根据 `type` (email/sms) 和 `purpose` 生成验证码。
    - 发送逻辑暂时留空或模拟。
    - 考虑实现速率限制。
    """
    # 验证码生成逻辑
    # verification_code = generate_random_numeric_code(length=6) # 伪代码

    # 验证码存储逻辑 (例如 Redis, 包含 recipient, purpose, code, expires_at)
    # store_verification_code(
    #     recipient=request_data.recipient,
    #     purpose=request_data.purpose,
    #     code=verification_code,
    #     expires_in_minutes=10 # 例如10分钟有效期
    # ) # 伪代码

    # 发送逻辑 (邮件或短信)
    # if request_data.type == "email":
    #     send_email_verification_code(to_email=request_data.recipient, code=verification_code, purpose=request_data.purpose) # 伪代码
    # elif request_data.type == "sms":
    #     send_sms_verification_code(to_phone=request_data.recipient, code=verification_code, purpose=request_data.purpose) # 伪代码

    # 此处仅打印日志模拟发送
    print(
        f"模拟：向 {request_data.recipient} ({request_data.type}) 发送用途为 '{request_data.purpose}' 的验证码。"
    )
    # TODO: 实现实际的验证码生成、存储、发送以及速率限制逻辑。

    # 速率限制逻辑 (需要 FastAPI-Limiter 或自定义实现)
    # 例如:
    # limiter = RateLimiter(key_func=get_ipaddr, limits=["5/minute", "20/hour"])
    # await limiter.check(request) # 伪代码，实际使用时 limiter 需要在 app 级别配置

    return Msg(message="验证码已发送（模拟）。")


@router.post(
    "/heartbeat",
    response_model=Msg,
    summary="会话心跳",
    description="客户端定期调用以保持用户会话活跃。",
)
def heartbeat(
    heartbeat_data: HeartbeatRequest,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: DBUser to models.User # 确保用户已认证且活动
) -> Any:
    """
    会话心跳:
    - 需要JWT认证 (`access_token`)。
    - 根据请求中的 `session_token` 和 `access_token` 中的用户信息，更新 `user_bound_devices` 中对应设备会话的 `last_active_at`。
    """
    # 1. 根据 session_token 从 user_bound_devices 表中找到对应的设备记录
    bound_device = crud_user_bound_device.get_by_session_token(
        db, session_token=heartbeat_data.session_token
    )

    if not bound_device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话令牌无效或会话已结束。",
        )

    # 2. 验证设备记录是否属于当前认证用户
    if bound_device.user_id != current_user.id:
        # 这种情况理论上不应该发生，如果 session_token 是唯一的且与用户正确关联
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="会话令牌与当前用户不匹配。",
        )

    # 3. 更新该记录的 last_active_at
    # crud_user_bound_device.update_bound_device_activity 中会自动更新 last_active_at
    # 如果只是单纯的心跳，不需要传递其他参数给 update_bound_device_activity
    updated_device = crud_user_bound_device.update_bound_device_activity(
        db, db_obj=bound_device
    )

    if not updated_device:  # 理论上不会为 None，因为 db_obj 存在
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新设备活跃时间失败。",
        )

    # (可选逻辑) 检查会话是否仍然有效，例如 session_token 是否匹配
    # if updated_device.session_token != heartbeat_data.session_token:
    #    # 这可能意味着会话在其他地方被登出或更新了
    #    pass

    return Msg(message="心跳成功，会话已更新。")


@router.post(
    "/activate-license-account",
    response_model=ActivateLicenseAccountResponse,
    summary="激活卡密并创建/关联账户",
    description="当用户使用未激活的卡密登录时，引导调用此接口完成账户创建、卡密激活和设备绑定。",
)
def activate_license_account(
    request_data: ActivateLicenseAccountRequest, db: Session = Depends(api_deps.get_db)
) -> Any:
    """
    激活激活码并创建/关联账户:
    1.  校验卡密有效性 (存在、状态 `available` 或 `consigned_available`, `user_id` 为 NULL)。
    2.  校验用户名唯一性、密码强度等。
    3.  创建新用户 (`users` 表)。
    4.  处理卡密激活与结算（特别是针对寄售卡密）。
    5.  更新卡密信息 (`license_keys` 表)。
    6.  触发佣金计算的逻辑框架。
    7.  绑定设备 (`user_bound_devices` 表，但不生成 `session_token`)。
    8.  返回成功响应，提示用户使用新凭证登录。
    """
    # 1. 校验 key_string
    license_key = crud_license_key.get_license_key_by_string(
        db, key_string=request_data.key_string
    )
    if not license_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="提供的激活码不存在。"
        )

    if not (
        license_key.status == models.LicenseKeyStatus.available
        or (
            license_key.consignment_reseller_id is not None
            and license_key.status == models.LicenseKeyStatus.consigned_available
        )
    ):  # MODIFIED: LicenseKeyStatus
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="激活码状态无效，可能已被使用、过期或禁用。",
        )

    if license_key.user_id is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="激活码已被其他账户关联。"
        )

    # 2. 校验用户名、密码、邮箱等
    existing_user_by_username = crud_user.get_user_by_username(
        db, username=request_data.username
    )
    if existing_user_by_username:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail="用户名已存在。"
        )

    if request_data.email:
        existing_user_by_email = crud_user.get_user_by_email(
            db, email=request_data.email
        )
        if existing_user_by_email:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="电子邮箱已存在。"
            )

    # TODO: 校验密码强度

    # 3. 创建新用户
    user_create_data = UserCreate(
        username=request_data.username,
        password=request_data.password,
        email=request_data.email,
        phone_number=request_data.phone_number,
        # creation_source 将在 crud_user.create_user 中处理，或在此处显式设置
    )
    # 在 User 模型中增加 creation_source 字段，并在创建时设置
    # new_user = crud_user.create_user(db, obj_in=user_create_data, creation_source="license_activation")
    # 假设 crud_user.create_user 内部可以处理 creation_source，或者我们直接修改 User 模型默认值或 crud
    # 暂时先这样创建，后续再调整 User 模型和 CRUD
    db_user_obj = crud_user.create_user(db, obj_in=user_create_data)
    # 手动更新 creation_source 如果 CRUD 不支持
    # 假设 UserCreationSource 枚举在 models.user 中定义
    # from app.db.models.user import UserCreationSource # 将移至顶部
    if (
        db_user_obj.creation_source != models.UserCreationSource.license_activation
    ):  # MODIFIED: UserCreationSource
        db_user_obj.creation_source = (
            models.UserCreationSource.license_activation
        )  # MODIFIED: UserCreationSource
        db.add(db_user_obj)
        # db.commit() # 稍后统一 commit

    # 4. 处理卡密激活与结算（针对寄售）
    # TODO: 实现详细的寄售卡密结算逻辑
    # - 查询 reseller_discounts 或 license_types.reseller_cost
    # - 扣除代理余额 resellers.balance
    # - 更新 license_keys.settlement_status
    # 假设 LicenseSettlementStatus 枚举在 models.license 中定义
    # from app.db.models.license import LicenseSettlementStatus # 将移至顶部
    if (
        license_key.consignment_reseller_id is not None
        and license_key.status == models.LicenseKeyStatus.consigned_available
    ):  # MODIFIED: LicenseKeyStatus
        print(
            f"模拟：处理寄售卡密 {license_key.key_string} 的结算，代理ID: {license_key.consignment_reseller_id}"
        )
        # 假设结算成功
        license_key.settlement_status = (
            models.LicenseSettlementStatus.settled
        )  # MODIFIED: LicenseSettlementStatus
        db.add(license_key)

    # 5. 更新卡密信息
    license_type = license_key.license_type  # 获取关联的 LicenseType
    if not license_type:
        # 这种情况不应该发生，如果数据库完整性约束正常
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="卡密类型数据异常。",
        )

    expires_at_dt: Optional[datetime] = None
    # 假设 LicenseDurationUnit 枚举在 models.license 中定义
    # from app.db.models.license import LicenseDurationUnit # 将移至顶部
    # from datetime import timezone # 将移至顶部

    if (
        license_type.duration_unit != models.LicenseDurationUnit.permanent
    ):  # MODIFIED: LicenseDurationUnit
        now_utc = datetime.now(timezone.utc)
        if (
            license_type.duration_unit == models.LicenseDurationUnit.days
        ):  # MODIFIED: LicenseDurationUnit
            expires_at_dt = now_utc + timedelta(days=license_type.duration_value)
        elif (
            license_type.duration_unit == models.LicenseDurationUnit.hours
        ):  # MODIFIED: LicenseDurationUnit
            expires_at_dt = now_utc + timedelta(hours=license_type.duration_value)
        elif (
            license_type.duration_unit == models.LicenseDurationUnit.minutes
        ):  # MODIFIED: LicenseDurationUnit
            expires_at_dt = now_utc + timedelta(minutes=license_type.duration_value)
        # 其他单位...

    license_key.user_id = db_user_obj.id
    license_key.status = models.LicenseKeyStatus.used  # MODIFIED: LicenseKeyStatus
    license_key.activated_at = datetime.now(timezone.utc)
    license_key.expires_at = expires_at_dt
    db.add(license_key)

    # 6. 触发佣金计算的逻辑框架
    # TODO: 实现佣金计算逻辑
    if license_key.consignment_reseller_id:
        print(
            f"模拟：为卡密 {license_key.key_string} (销售代理: {license_key.consignment_reseller_id}) 计算佣金。"
        )

    # 7. 绑定设备 (但不生成 session_token)
    # 检查 crud.user_bound_device.create_user_bound_device 是否允许 session_token 为 None
    # 或者直接创建 UserBoundDevice 对象实例
    # from app.db.models.device import UserBoundDevice # 将移至顶部
    bound_device_obj = models.UserBoundDevice(  # MODIFIED: UserBoundDevice
        user_id=db_user_obj.id,
        device_id=request_data.device_id,
        device_alias=f"激活设备 {request_data.device_id[:8]}",
        session_token=None,  # 显式设置为 None
        # bound_at 和 last_active_at 应该有默认值
    )
    db.add(bound_device_obj)

    # 更新用户最大设备数 (如果卡密类型定义了)
    if (
        license_type.max_concurrent_devices is not None
        and license_type.max_concurrent_devices
        > (db_user_obj.max_concurrent_devices or 0)
    ):
        db_user_obj.max_concurrent_devices = license_type.max_concurrent_devices
        db.add(db_user_obj)

    db.commit()  # 统一提交所有更改
    db.refresh(db_user_obj)
    db.refresh(license_key)
    # license_type 已经在会话中，不需要 refresh，除非它是新创建的或者从数据库重新加载
    # if license_type:
    #     db.refresh(license_type)

    # 8. 返回成功响应
    user_info_resp = ActivateLicenseAccountUserInfo(
        id=db_user_obj.id, username=db_user_obj.username
    )
    license_info_resp = ActivateLicenseAccountLicenseInfo(
        key_string=license_key.key_string,
        license_type_code=license_type.code,
        license_type_name=license_type.name,
        expires_at=license_key.expires_at,
    )

    return ActivateLicenseAccountResponse(
        message="账户创建成功并已绑定激活码，请使用新账户信息登录。",
        user_info=user_info_resp,
        license_info=license_info_resp,
    )
