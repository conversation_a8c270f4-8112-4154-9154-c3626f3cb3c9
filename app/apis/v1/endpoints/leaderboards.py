# TanZouDaShiAPI/app/apis/v1/endpoints/leaderboards.py
from typing import List, Optional
from enum import Enum

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from app import models
from app import schemas
from app.apis import deps as api_deps

# MODIFIED: Removed specific model imports, will use app.models
from app.schemas.score import ScoreResponse
from app.schemas.leaderboard import UserLeaderboardEntry

router = APIRouter()


class TimeRange(str, Enum):
    """时间范围枚举"""

    daily = "daily"
    weekly = "weekly"
    monthly = "monthly"
    all_time = "all_time"


@router.get(
    "/scores/popular",
    response_model=List[ScoreResponse],
    summary="获取热门谱曲排行榜",
    description="根据浏览量 (`view_count`) 对谱曲进行排行。",
)
def get_popular_scores_leaderboard(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(10, ge=1, le=100, description="返回数量上限"),
    time_range: Optional[TimeRange] = Query(
        None, description="时间范围 (daily, weekly, monthly, all_time)"
    ),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口
) -> List[models.Score]:  # MODIFIED: ScoreModel to models.Score
    """
    获取热门谱曲排行榜。
    - **limit**: 返回的谱曲数量。
    - **time_range**: 筛选特定时间范围内的热门谱曲（暂未完全实现，需要根据 created_at 或特定时间窗口的浏览数据筛选）。
    """
    # TODO: 实现 time_range 筛选逻辑
    # 目前简单实现，直接按总 view_count 排序
    query = db.query(models.Score).filter(
        models.Score.status == models.ScoreStatus.approved
    )  # MODIFIED: ScoreModel, ScoreStatus

    # if time_range:
    #     # 这里需要根据 time_range 实现更复杂的日期过滤逻辑
    #     # 例如，如果 time_range == "daily", 则需要筛选当天的谱曲或当天的浏览记录
    #     # 这可能需要 Score 模型有更详细的浏览日志或在 Score 上有 creation_date/update_date
    #     pass

    popular_scores = (
        query.order_by(desc(models.Score.view_count)).limit(limit).all()
    )  # MODIFIED: ScoreModel
    return popular_scores


@router.get(
    "/scores/latest",
    response_model=List[ScoreResponse],
    summary="获取最新谱曲排行榜",
    description="根据创建时间 (`created_at`) 对谱曲进行排行。",
)
def get_latest_scores_leaderboard(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(10, ge=1, le=100, description="返回数量上限"),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口
) -> List[models.Score]:  # MODIFIED: ScoreModel to models.Score
    """
    获取最新谱曲排行榜。
    - **limit**: 返回的谱曲数量。
    """
    latest_scores = (
        db.query(models.Score)  # MODIFIED: ScoreModel
        .filter(
            models.Score.status == models.ScoreStatus.approved
        )  # MODIFIED: ScoreModel, ScoreStatus
        .order_by(desc(models.Score.created_at))  # MODIFIED: ScoreModel
        .limit(limit)
        .all()
    )
    return latest_scores


@router.get(
    "/scores/top_rated",
    response_model=List[ScoreResponse],
    summary="获取高分谱曲排行榜",
    description="根据平均评分 (`average_rating`) 对谱曲进行排行。",
)
def get_top_rated_scores_leaderboard(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(10, ge=1, le=100, description="返回数量上限"),
    min_ratings_count: Optional[int] = Query(
        0, ge=0, description="至少有多少评分才参与排行"
    ),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口
) -> List[models.Score]:  # MODIFIED: ScoreModel to models.Score
    """
    获取高分谱曲排行榜。
    - **limit**: 返回的谱曲数量。
    - **min_ratings_count**: 谱曲至少需要有多少个评分才能参与排行。
    """
    query = db.query(models.Score).filter(
        models.Score.status == models.ScoreStatus.approved
    )  # MODIFIED: ScoreModel, ScoreStatus
    if min_ratings_count is not None and min_ratings_count > 0:
        query = query.filter(
            models.Score.rating_count >= min_ratings_count
        )  # MODIFIED: ScoreModel

    top_rated_scores = (
        query.order_by(
            desc(models.Score.average_rating), desc(models.Score.rating_count)
        )
        .limit(limit)
        .all()
    )  # MODIFIED: ScoreModel
    return top_rated_scores


@router.get(
    "/scores/most_downloaded",
    response_model=List[ScoreResponse],
    summary="获取下载最多谱曲排行榜",
    description="根据下载次数 (`download_count`) 对谱曲进行排行。",
)
def get_most_downloaded_scores_leaderboard(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(10, ge=1, le=100, description="返回数量上限"),
    time_range: Optional[TimeRange] = Query(
        None, description="时间范围 (daily, weekly, monthly, all_time)"
    ),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口
) -> List[models.Score]:  # MODIFIED: ScoreModel to models.Score
    """
    获取下载最多谱曲排行榜。
    - **limit**: 返回的谱曲数量。
    - **time_range**: 筛选特定时间范围内的下载数据（暂未完全实现）。
    """
    # TODO: 实现 time_range 筛选逻辑
    query = db.query(models.Score).filter(
        models.Score.status == models.ScoreStatus.approved
    )  # MODIFIED: ScoreModel, ScoreStatus

    # if time_range:
    # pass

    most_downloaded_scores = (
        query.order_by(desc(models.Score.download_count)).limit(limit).all()
    )  # MODIFIED: ScoreModel
    return most_downloaded_scores


@router.get(
    "/users/top_uploaders",
    response_model=List[UserLeaderboardEntry],  # 使用新的 Schema
    summary="获取用户上传排行榜",
    description="根据用户上传并通过审核的谱曲数量进行排行。",
)
def get_top_uploaders_leaderboard(
    db: Session = Depends(api_deps.get_db),
    limit: int = Query(10, ge=1, le=100, description="返回数量上限"),
    # current_user: Optional[models.User] = Depends(deps.get_current_user), # 公开接口
) -> List[dict]:  # SQLAlchemy 返回的是 Row 对象，需要转换为 Pydantic 模型
    """
    获取用户上传排行榜。
    - **limit**: 返回的用户数量。
    返回一个包含用户对象和其上传数量的列表。
    """
    # 查询用户及其已批准的谱曲数量
    # 使用 func.count 来计数，并按计数结果排序
    # 需要 join User 和 Score 表
    top_uploaders_query = (
        db.query(
            models.User,  # MODIFIED: UserModel to models.User
            func.count(models.Score.id).label(
                "upload_count"
            ),  # MODIFIED: ScoreModel to models.Score
        )
        .join(
            models.Score, models.User.id == models.Score.uploader_user_id
        )  # MODIFIED: ScoreModel, UserModel
        .filter(
            models.Score.status == models.ScoreStatus.approved
        )  # MODIFIED: ScoreModel, ScoreStatus # 只计算已批准的谱曲
        .group_by(models.User.id)  # MODIFIED: UserModel
        .order_by(desc("upload_count"))
        .limit(limit)
    )

    results = top_uploaders_query.all()

    # 将查询结果转换为 Pydantic 模型列表
    leaderboard_entries: List[UserLeaderboardEntry] = []
    for user_model, upload_count in results:
        # user_data = schemas.user.UserResponse.from_orm(user_model) # 转换为 UserResponse
        # 使用 MinimalUserResponse
        minimal_user_data = schemas.leaderboard.MinimalUserResponse.model_validate(
            user_model
        )
        leaderboard_entries.append(
            UserLeaderboardEntry(user=minimal_user_data, upload_count=upload_count)
        )

    return leaderboard_entries
