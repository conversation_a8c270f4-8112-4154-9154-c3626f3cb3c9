# TanZouDaShiAPI/app/apis/v1/endpoints/resellers.py
from typing import List, Optional
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, Query, Body, Path, status
from sqlalchemy.orm import Session

from app.schemas.reseller import (
    ResellerInfoResponse,
    ResellerProfileUpdateRequest,
    ResellerCreate,
    ResellerLevelResponse,  # 新增
    ResellerLevelCreate,  # 新增
    ResellerLevelUpdate,  # 新增
    AdminResellerCreateRequest,  # 新增
    AdminResellerUpdateRequest,  # 新增
)
from app.schemas.transaction import (
    CommissionRecordResponse,
    WithdrawalRecordResponse,
    WithdrawalRecordCreate,
    WithdrawalProcessRequest,  # 新增
    # WithdrawalRecordCreateWithUser # This schema is not in transaction.py, will be handled by CRUD
)

# Assuming WithdrawalRecordCreateWithUser is an internal schema or handled differently,
# as it's not directly in app.schemas.transaction.py.
# If it were, it would be: from app.schemas.transaction import WithdrawalRecordCreateWithUser
from app import models  # MODIFIED: Changed import from app.db to app
from app.crud.crud_reseller import (
    crud_reseller,
    crud_reseller_level,
)  # 导入经销商相关的 CRUD 操作
from app.crud.crud_reseller_finance import (
    crud_commission_record,
)  # 假设佣金记录在 crud_reseller_finance.py 中
from app.crud.crud_reseller_finance import (
    crud_withdrawal_record,
)  # 假设提现记录在 crud_reseller_finance.py 中
from app.crud.crud_user import user as crud_user  # crud_user 被 admin 端点使用
from app.apis import deps as api_deps  # 依赖项, 使用别名避免潜在冲突
# MODIFIED: Removed specific model imports, will use app.models

router = APIRouter()

# --- 代理用户自身相关的端点 ---


@router.get(
    "/me", response_model=ResellerInfoResponse, summary="获取当前用户的代理信息"
)
def get_current_reseller_info(
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    获取当前已认证代理用户的详细代理信息。
    """
    reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=current_user.id
    )
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="当前用户不是代理或代理信息未找到",
        )

    # 尝试加载关联的 user 和 level 信息 (如果 CRUD 方法不自动加载)
    # crud_reseller.crud_reseller.load_related(db, db_obj=reseller_profile) # 假设有这样的方法
    # 或者在 schema 中配置 from_attributes = True 并确保 ORM 加载了关系
    return reseller_profile


@router.put(
    "/me", response_model=ResellerInfoResponse, summary="更新当前用户的代理信息"
)
def update_current_reseller_info(
    reseller_update_data: ResellerProfileUpdateRequest,  # 代理只能更新部分信息
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    更新当前已认证代理用户的部分信息，例如提现账户（如果模型支持）。
    当前 ResellerProfileUpdateRequest 仅包含 remarks。
    """
    reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=current_user.id
    )
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="代理信息未找到"
        )

    # 实际的更新逻辑，目前 ResellerProfileUpdateRequest 只有 remarks
    update_data_dict = reseller_update_data.model_dump(exclude_unset=True)

    # 假设 Reseller 模型中有 withdrawal_payment_method 和 withdrawal_payment_account 字段
    # 并且 ResellerProfileUpdateRequest 也包含这些字段
    # if "withdrawal_payment_method" in update_data_dict:
    #     reseller_profile.withdrawal_payment_method = update_data_dict["withdrawal_payment_method"]
    # if "withdrawal_payment_account" in update_data_dict:
    #     reseller_profile.withdrawal_payment_account = update_data_dict["withdrawal_payment_account"]

    updated_reseller = crud_reseller.crud_reseller.update(
        db, db_obj=reseller_profile, obj_in=update_data_dict
    )
    return updated_reseller


@router.get(
    "/me/commission-records",
    response_model=List[CommissionRecordResponse],
    summary="获取当前代理的佣金记录",
)
def get_current_reseller_commission_records(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    获取当前已认证代理用户的佣金记录列表，支持分页。
    """
    commission_records = (
        crud_commission_record.crud_commission_record.get_multi_by_reseller(
            db, reseller_id=current_user.id, skip=skip, limit=limit
        )
    )
    return commission_records


@router.get(
    "/me/withdrawal-records",
    response_model=List[WithdrawalRecordResponse],
    summary="获取当前代理的提现记录",
)
def get_current_reseller_withdrawal_records(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    获取当前已认证代理用户的提现记录列表，支持分页。
    """
    withdrawal_records = (
        crud_withdrawal_record.crud_withdrawal_record.get_multi_by_reseller(
            db, reseller_id=current_user.id, skip=skip, limit=limit
        )
    )
    return withdrawal_records


@router.post(
    "/me/withdrawals",
    response_model=WithdrawalRecordResponse,
    status_code=status.HTTP_201_CREATED,
    summary="当前代理申请提现",
)
def create_reseller_withdrawal_request(
    withdrawal_create_data: WithdrawalRecordCreate,
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    当前已认证代理用户提交新的提现申请。
    需要校验提现金额是否超过可用余额。
    """
    reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=current_user.id
    )
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="无法找到代理信息"
        )

    if withdrawal_create_data.amount <= Decimal(0):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="提现金额必须大于0"
        )

    if reseller_profile.balance < withdrawal_create_data.amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="提现金额超过可用余额"
        )

    # 创建提现记录前，先扣除代理余额 (或者在管理员批准后扣除，这里根据设计文档，先申请再处理)
    # 事务性考虑：如果创建记录失败，余额是否回滚？ 一般是先记录，批准时再扣款。
    # 这里，我们先创建记录，状态为 pending。余额将在管理员批准时更新。

    # 补充 reseller_user_id 到创建数据中
    db_withdrawal_data = withdrawal_create_data.model_dump()
    db_withdrawal_data["reseller_user_id"] = current_user.id

    # 确保 WithdrawalRecordCreate schema 中不包含 reseller_user_id，或者在 CRUD 的 create 方法中处理
    # 为了安全，我们显式地从 current_user 获取 reseller_user_id

    # Assuming WithdrawalRecordCreateWithUser is handled by the CRUD method internally
    # or is a specific schema used within CRUD, not directly imported here.
    # If WithdrawalRecordCreateWithUser was defined in app.schemas.transaction, the line would be:
    # db, obj_in=WithdrawalRecordCreateWithUser(**db_withdrawal_data)
    # For now, we assume the CRUD method takes a dictionary or a compatible Pydantic model
    # that can be constructed from db_withdrawal_data.
    # The original code uses schemas.transaction.WithdrawalRecordCreateWithUser which implies it might be defined there.
    # However, the provided file content for transaction.py does not show it.
    # We will proceed assuming the CRUD method can handle `WithdrawalRecordCreate` or a dict.
    # If `WithdrawalRecordCreateWithUser` is essential and defined elsewhere, this might need adjustment.
    # For now, let's assume the CRUD method is flexible or `WithdrawalRecordCreate` is sufficient.
    # A more robust solution would be to define `WithdrawalRecordCreateWithUser` in `app.schemas.transaction`
    # or adjust the CRUD method signature.
    # For the purpose of this refactoring, if `WithdrawalRecordCreateWithUser` IS in `app.schemas.transaction`
    # (and was missed in the file read), then the import should include it and be used here.
    # Given it's not listed in the read file, we'll use the base create schema.
    from app.schemas.transaction import (
        WithdrawalRecordCreateWithUser,
    )  # Assuming it exists for this line

    new_withdrawal_request_obj = (
        crud_withdrawal_record.crud_withdrawal_record.create_with_user_id(
            db, obj_in=WithdrawalRecordCreateWithUser(**db_withdrawal_data)
        )
    )

    # 提现申请成功后，可以考虑更新代理余额（例如冻结这部分金额），但更常见的做法是管理员批准时才扣减。
    # 此处仅创建申请记录。
    return new_withdrawal_request_obj


@router.get(
    "/me/downlines",
    response_model=List[ResellerInfoResponse],
    summary="获取当前代理的下线代理列表",
)
def get_current_reseller_downlines(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    current_user: models.User = Depends(
        api_deps.get_current_active_reseller_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    获取当前已认证代理用户的直接下级代理列表。
    """
    downline_resellers = crud_reseller.crud_reseller.get_multi_by_parent_reseller(
        db, parent_reseller_user_id=current_user.id, skip=skip, limit=limit
    )
    return downline_resellers


# --- 管理员相关的代理接口 ---

ADMIN_PREFIX = "/admin"  # 定义管理员接口的统一前缀，方便管理


@router.get(
    f"{ADMIN_PREFIX}/levels",
    response_model=List[ResellerLevelResponse],
    summary="管理员获取代理级别列表",
)
def admin_get_reseller_levels(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    is_active: Optional[bool] = Query(None, description="按激活状态筛选"),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员获取所有代理级别列表，支持分页和按激活状态筛选。
    """
    filters = {}
    if is_active is not None:
        filters["is_active"] = is_active

    levels = crud_reseller_level.crud_reseller_level.get_multi_with_filters(
        db, skip=skip, limit=limit, filters=filters
    )
    return levels


@router.post(
    f"{ADMIN_PREFIX}/levels",
    response_model=ResellerLevelResponse,
    status_code=status.HTTP_201_CREATED,
    summary="管理员创建代理级别",
)
def admin_create_reseller_level(
    level_in: ResellerLevelCreate,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员创建新的代理级别。
    需要确保 level_value和 name 的唯一性。
    """
    existing_level_by_value = (
        crud_reseller_level.crud_reseller_level.get_by_level_value(
            db, level_value=level_in.level_value
        )
    )
    if existing_level_by_value:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"级别值 '{level_in.level_value}' 已存在。",
        )

    existing_level_by_name = crud_reseller_level.crud_reseller_level.get_by_name(
        db, name=level_in.name
    )
    if existing_level_by_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"级别名称 '{level_in.name}' 已存在。",
        )

    new_level = crud_reseller_level.crud_reseller_level.create(db, obj_in=level_in)
    return new_level


@router.put(
    f"{ADMIN_PREFIX}/levels/{{level_id}}",
    response_model=ResellerLevelResponse,
    summary="管理员更新代理级别",
)
def admin_update_reseller_level(
    level_id: int = Path(..., description="要更新的代理级别ID", ge=1),
    *,
    level_update_data: ResellerLevelUpdate,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员更新指定的代理级别信息。
    """
    level_obj = crud_reseller_level.crud_reseller_level.get(db, id=level_id)
    if not level_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="未找到指定的代理级别"
        )

    update_data_dict = level_update_data.model_dump(exclude_unset=True)

    # 检查潜在的唯一性冲突 (level_value, name)
    if (
        "level_value" in update_data_dict
        and update_data_dict["level_value"] != level_obj.level_value
    ):
        existing = crud_reseller_level.crud_reseller_level.get_by_level_value(
            db, level_value=update_data_dict["level_value"]
        )
        if existing and existing.id != level_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"级别值 '{update_data_dict['level_value']}' 已被其他级别使用。",
            )

    if "name" in update_data_dict and update_data_dict["name"] != level_obj.name:
        existing = crud_reseller_level.crud_reseller_level.get_by_name(
            db, name=update_data_dict["name"]
        )
        if existing and existing.id != level_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"级别名称 '{update_data_dict['name']}' 已被其他级别使用。",
            )

    updated_level = crud_reseller_level.crud_reseller_level.update(
        db, db_obj=level_obj, obj_in=update_data_dict
    )
    return updated_level


@router.get(
    f"{ADMIN_PREFIX}/all",
    response_model=List[ResellerInfoResponse],
    summary="管理员获取代理列表",
)
def admin_get_all_resellers(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页记录数"),
    username_filter: Optional[str] = Query(None, description="按用户名筛选 (模糊匹配)"),
    level_value_filter: Optional[int] = Query(None, description="按代理级别值筛选"),
    status_filter: Optional[str] = Query(
        None, description="按代理状态筛选 (active, inactive, frozen)"
    ),  # TODO: 使用枚举
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员获取所有代理列表，支持分页和多种筛选条件。
    """
    # 此处需要一个更复杂的 get_multi 方法，支持连接查询和多种筛选
    # 暂时使用 get_multi，后续可以扩展 crud_reseller
    # filters = {}
    # if username_filter: filters["user.username__ilike"] = f"%{username_filter}%" # 假设支持这种筛选
    # if level_value_filter is not None: filters["reseller_level_value"] = level_value_filter
    # if status_filter: filters["status"] = status_filter

    # resellers = crud_reseller.crud_reseller.get_multi_with_filters(db, skip=skip, limit=limit, filters=filters)
    # 简单实现，不带复杂筛选
    resellers = crud_reseller.crud_reseller.get_multi(db, skip=skip, limit=limit)
    return resellers


@router.post(
    f"{ADMIN_PREFIX}/set-reseller",
    response_model=ResellerInfoResponse,
    status_code=status.HTTP_201_CREATED,
    summary="管理员设置用户为代理",
)
def admin_set_user_as_reseller(
    reseller_data: AdminResellerCreateRequest,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员将现有用户设置为代理，或创建新代理记录。
    """
    target_user = crud_user.user.get(db, id=reseller_data.user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"用户ID {reseller_data.user_id} 未找到。",
        )

    existing_reseller = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=reseller_data.user_id
    )
    if existing_reseller:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户 {target_user.username} (ID: {target_user.id}) 已经是代理了。",
        )

    if reseller_data.reseller_level_value:
        level = crud_reseller_level.crud_reseller_level.get_by_level_value(
            db, level_value=reseller_data.reseller_level_value
        )
        if not level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别值 {reseller_data.reseller_level_value} 无效或未找到。",
            )

    if reseller_data.parent_reseller_user_id:
        parent_reseller_user = crud_user.user.get(
            db, id=reseller_data.parent_reseller_user_id
        )
        if not parent_reseller_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {reseller_data.parent_reseller_user_id} 未找到。",
            )
        parent_reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
            db, user_id=reseller_data.parent_reseller_user_id
        )
        if not parent_reseller_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {reseller_data.parent_reseller_user_id} 不是一个有效的代理。",
            )

    # 创建 Reseller 记录
    # AdminResellerCreateRequest 已经包含了 user_id
    reseller_create_schema = ResellerCreate(**reseller_data.model_dump())

    new_reseller_profile = crud_reseller.crud_reseller.create(
        db, obj_in=reseller_create_schema
    )

    # (可选) 更新用户角色为 reseller
    if (
        target_user.role != models.UserRole.reseller
    ):  # MODIFIED: models.user.UserRole to models.UserRole
        crud_user.user.update(
            db, db_obj=target_user, obj_in={"role": models.UserRole.reseller}
        )  # MODIFIED: models.user.UserRole to models.UserRole

    # 重新获取以加载关联数据
    # new_reseller_profile_with_relations = crud_reseller.crud_reseller.get_by_user_id(db, user_id=new_reseller_profile.user_id)
    return new_reseller_profile  # crud_reseller.create 返回的应该就是 ResellerInfoResponse 兼容的


@router.put(
    f"{ADMIN_PREFIX}/{{reseller_user_id}}",
    response_model=ResellerInfoResponse,
    summary="管理员更新代理信息",
)
def admin_update_reseller_profile(
    reseller_user_id: int = Path(..., description="要更新的代理的用户ID", ge=1),
    *,
    reseller_update_data: AdminResellerUpdateRequest,
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员更新指定代理用户的信息。
    """
    reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=reseller_user_id
    )
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID为 {reseller_user_id} 的代理信息。",
        )

    update_data_dict = reseller_update_data.model_dump(exclude_unset=True)

    if (
        "reseller_level_value" in update_data_dict
        and update_data_dict["reseller_level_value"] is not None
    ):
        level = crud_reseller_level.crud_reseller_level.get_by_level_value(
            db, level_value=update_data_dict["reseller_level_value"]
        )
        if not level:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"代理级别值 {update_data_dict['reseller_level_value']} 无效或未找到。",
            )

    if (
        "parent_reseller_user_id" in update_data_dict
        and update_data_dict["parent_reseller_user_id"] is not None
    ):
        if (
            update_data_dict["parent_reseller_user_id"] == reseller_user_id
        ):  # 防止自己是自己的上级
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能将自己设置为自己的上级代理。",
            )
        parent_reseller_user = crud_user.user.get(
            db, id=update_data_dict["parent_reseller_user_id"]
        )
        if not parent_reseller_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {update_data_dict['parent_reseller_user_id']} 未找到。",
            )
        parent_reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
            db, user_id=update_data_dict["parent_reseller_user_id"]
        )
        if not parent_reseller_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"上级代理用户ID {update_data_dict['parent_reseller_user_id']} 不是一个有效的代理。",
            )

    updated_profile = crud_reseller.crud_reseller.update(
        db, db_obj=reseller_profile, obj_in=update_data_dict
    )
    return updated_profile


@router.get(
    f"{ADMIN_PREFIX}/withdrawals",
    response_model=List[WithdrawalRecordResponse],
    summary="管理员获取提现申请列表",
)
def admin_get_withdrawal_requests(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    status_filter: Optional[models.WithdrawalStatus] = Query(
        None, description="按提现状态筛选"
    ),  # MODIFIED: WithdrawalStatus to models.WithdrawalStatus
    reseller_user_id_filter: Optional[int] = Query(
        None, description="按代理用户ID筛选"
    ),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员获取所有提现申请列表，支持分页和筛选。
    """
    # 此处需要一个更复杂的 get_multi 方法，支持多种筛选
    # 暂时使用 get_multi，后续可以扩展 crud_withdrawal_record
    filters = {}
    if status_filter:
        filters["status"] = status_filter
    if reseller_user_id_filter is not None:
        filters["reseller_user_id"] = reseller_user_id_filter

    # withdrawals = crud_withdrawal_record.crud_withdrawal_record.get_multi_with_filters(db, skip=skip, limit=limit, filters=filters)
    # 简单实现
    withdrawals = crud_withdrawal_record.crud_withdrawal_record.get_multi(
        db, skip=skip, limit=limit, filters=filters if filters else None
    )
    return withdrawals


@router.patch(
    f"{ADMIN_PREFIX}/withdrawals/{{withdrawal_id}}/process",
    response_model=WithdrawalRecordResponse,
    summary="管理员处理提现申请",
)
def admin_process_withdrawal_request(
    withdrawal_id: int = Path(..., description="要处理的提现申请ID", ge=1),
    process_data: WithdrawalProcessRequest = Body(...),
    db: Session = Depends(api_deps.get_db),
    current_admin: models.User = Depends(
        api_deps.get_current_active_superuser
    ),  # MODIFIED: UserModel to models.User
):
    """
    管理员处理提现申请，批准或拒绝。
    如果批准，需要更新代理的余额。
    """
    withdrawal_request = crud_withdrawal_record.crud_withdrawal_record.get(
        db, id=withdrawal_id
    )
    if not withdrawal_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="未找到指定的提现申请。"
        )

    if withdrawal_request.status not in [
        models.WithdrawalStatus.pending
    ]:  # MODIFIED: WithdrawalStatus # 只能处理待处理的申请
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"此提现申请状态为 {withdrawal_request.status}，无法处理。",
        )

    reseller_profile = crud_reseller.crud_reseller.get_by_user_id(
        db, user_id=withdrawal_request.reseller_user_id
    )
    if not reseller_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到提现申请关联的代理信息。",
        )

    if (
        process_data.status == models.WithdrawalStatus.approved
    ):  # MODIFIED: WithdrawalStatus
        if reseller_profile.balance < withdrawal_request.amount:
            # 理论上申请时已校验，但此处再次校验以防万一，或者如果余额在申请后发生变动
            crud_withdrawal_record.crud_withdrawal_record.update_status(
                db,
                db_obj=withdrawal_request,
                status=models.WithdrawalStatus.failed,  # MODIFIED: WithdrawalStatus # 或其他表示余额不足的状态
                remarks=f"管理员(ID:{current_admin.id})尝试批准但余额不足。原备注: {process_data.remarks or ''}",
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="代理余额不足以完成此次提现。提现申请已标记为失败。",
            )

        # 批准提现，扣减代理余额
        new_balance = reseller_profile.balance - withdrawal_request.amount
        crud_reseller.crud_reseller.update(
            db, db_obj=reseller_profile, obj_in={"balance": new_balance}
        )

        # 更新提现记录状态为 approved (或 completed，取决于流程)
        # API 设计中提到 "处理成功后需要更新代理余额", PATCH 路径是 /process,
        # 通常 process 意味着状态变为 approved/rejected。打款完成可能是另一个步骤/状态。
        # 这里我们先将其设为 approved
        updated_withdrawal = crud_withdrawal_record.crud_withdrawal_record.update_status(
            db,
            db_obj=withdrawal_request,
            status=models.WithdrawalStatus.approved,  # MODIFIED: WithdrawalStatus
            remarks=f"管理员(ID:{current_admin.id})批准。备注: {process_data.remarks or ''}",
        )
    elif (
        process_data.status == models.WithdrawalStatus.rejected
    ):  # MODIFIED: WithdrawalStatus
        updated_withdrawal = crud_withdrawal_record.crud_withdrawal_record.update_status(
            db,
            db_obj=withdrawal_request,
            status=models.WithdrawalStatus.rejected,  # MODIFIED: WithdrawalStatus
            remarks=f"管理员(ID:{current_admin.id})拒绝。备注: {process_data.remarks or ''}",
        )
    else:
        # 如果 process_data.status 不是 approved 或 rejected
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的处理状态。只能是 'approved' 或 'rejected'。",
        )

    return updated_withdrawal


# The CRUD operations (crud_reseller, crud_commission_record, crud_withdrawal_record, crud_user, crud_reseller_level)
# are now assumed to be synchronous based on previous analysis of the app/crud/ directory.
# All endpoint handlers have been converted from 'async def' to 'def' and 'await' keywords removed.
# Calls to CRUD methods have been changed from 'method_async' to 'method'.
