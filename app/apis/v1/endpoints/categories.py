# TanZouDaShiAPI/app/apis/v1/endpoints/categories.py
from typing import List, Any, Optional

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    Path,
    status,
)  # status import might be unused if all admin endpoints removed
from sqlalchemy.orm import Session

from app.schemas.category import (
    CategoryDetailResponse,
)  # MODIFIED - Removed CategoryCreate, CategoryUpdate
from app import crud
from app.apis import deps as api_deps
# from app.db.models.user import User # MODIFIED - User import removed as it was for admin endpoints

router = APIRouter()

# Admin endpoints (create, update, delete) are moved to app/apis/admin/categories_admin.py


@router.get(
    "/",
    response_model=List[CategoryDetailResponse],  # MODIFIED
    summary="获取谱曲分类列表 (公开)",
    description="获取所有公开的谱曲分类列表，支持按父分类ID筛选和分页。",
)
def read_categories(
    db: Session = Depends(api_deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=200, description="每页返回的记录数"),
    parent_id: Optional[int] = Query(
        None, description="父分类ID，用于获取其直接子分类"
    ),
    only_active: bool = Query(True, description="是否仅返回启用的分类"),
) -> Any:
    """
    获取谱曲分类列表。

    - **skip**: 分页参数，跳过的条目数。
    - **limit**: 分页参数，每页最大条目数。
    - **parent_id**: 可选，如果提供，则返回指定父分类ID下的子分类。
    - **only_active**: 可选，如果为True（默认），仅返回is_active=True的分类。
    """
    if parent_id is not None:
        categories = crud.category.get_children_of_category(
            db, parent_id=parent_id, skip=skip, limit=limit, only_active=only_active
        )
    else:
        # 如果未指定 parent_id，则获取所有顶级分类 (parent_category_id is None)
        # 或者，根据需求，可以返回所有分类，让客户端自行组织层级
        # 这里我们遵循接口设计文档，如果 parent_id 为空，则获取所有顶级分类
        query = db.query(crud.category.model)
        if only_active:
            query = query.filter(crud.category.model.is_active is True)  # type: ignore

        # 仅获取顶级分类
        query = query.filter(crud.category.model.parent_category_id.is_(None))

        categories = (
            query.order_by(crud.category.model.sort_order, crud.category.model.name)
            .offset(skip)
            .limit(limit)
            .all()
        )

    return categories


@router.get(
    "/{category_id_or_slug}",
    response_model=CategoryDetailResponse,  # MODIFIED
    summary="获取特定分类详情 (公开)",
    description="通过分类ID或slug获取单个分类的详细信息。",
)
def read_category_by_id_or_slug(
    category_id_or_slug: str = Path(..., description="分类的ID或Slug"),
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    获取单个分类的详细信息。

    - **category_id_or_slug**: 分类的ID (整数) 或 Slug (字符串)。
    """
    category = None
    if category_id_or_slug.isdigit():
        category = crud.category.get(db, id=int(category_id_or_slug))
    else:
        category = crud.category.get_by_slug(db, slug=category_id_or_slug)

    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"ID或Slug为 '{category_id_or_slug}' 的分类未找到。",
        )
    if not category.is_active:  # 根据接口设计，公开接口通常只返回 active 的
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,  # 或者 403 Forbidden 如果想区分
            detail=f"ID或Slug为 '{category_id_or_slug}' 的分类当前未启用。",
        )
    return category


# Admin endpoints (create, update, delete) were here and are now moved to app/apis/admin/categories_admin.py
