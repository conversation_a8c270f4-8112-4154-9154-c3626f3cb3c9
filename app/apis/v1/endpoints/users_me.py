# TanZouDaShiAPI/app/apis/v1/endpoints/users_me.py
from typing import Any, List, Optional

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    Body,
    UploadFile,
    File,
    Path,
    status,
)
from sqlalchemy.orm import Session

from app import crud  # schemas 将被单独导入

# 修改导入方式：直接从具体模块导入所需的 schema
from app.schemas.user import (
    UserMeResponse,
    UserLicenseInfo,
    UserResponse,
    UserPasswordUpdate,
    UserProfileUpdate,
    UserAvatarUpdateResponse,
)
from app.schemas.msg import Msg
from app.schemas.license import (
    UserLicenseDetailsResponse,
    ActiveLicenseInfo,
    HistoricalLicenseInfo,
    UserLicenseActivateResponse,
    UserLicenseActivateRequest,
    LicenseSettlementStatus,  # 假设在 license.py
    LicenseDurationUnit,  # 假设在 license.py
    LicenseInfoBase,
)
from app.schemas.device import (
    DeviceListResponse,
    DeviceResponse,
    <PERSON>ceUpdate,
    DeviceUnbindResponse,
    DeviceListMeta,
)
from app.schemas.user_interaction import UserFavoriteResponse
from app.schemas.score import ScoreResponse

from app import models  # MODIFIED: Changed import from app.db to app
from app.apis import deps as api_deps
from app.core.security import get_password_hash, verify_password
from app.core import security
from app.core.config import settings

# MODIFIED: Removed specific model imports, will use app.models
from datetime import datetime, timedelta, timezone

router = APIRouter()


@router.get("/me", response_model=UserMeResponse, summary="获取当前用户信息")
def read_user_me(
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
) -> Any:
    """
    获取当前登录用户的详细信息，包括其当前激活的卡密信息。
    """
    active_license_db = crud.license_key.get_user_active_license(
        db, user_id=current_user.id
    )

    user_license_info_schema = None
    if active_license_db and active_license_db.license_type:
        user_license_info_schema = UserLicenseInfo(  # 直接使用 UserLicenseInfo
            key_string=active_license_db.key_string,
            license_type_code=active_license_db.license_type.code,
            license_type_name=active_license_db.license_type.name,
            activated_at=active_license_db.activated_at,
            expires_at=active_license_db.expires_at,
        )

    return UserMeResponse(  # 直接使用 UserMeResponse
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        phone_number=current_user.phone_number,
        avatar_url=current_user.avatar_url,
        nickname=current_user.nickname,
        role=current_user.role.value,  # Enum 需要 .value
        status=current_user.status.value,  # Enum 需要 .value
        email_verified_at=current_user.email_verified_at,
        created_at=current_user.created_at,
        license_info=user_license_info_schema,
    )


@router.put("/me", response_model=UserResponse, summary="更新当前用户信息")
def update_user_me(
    user_update_data: UserProfileUpdate,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    更新当前登录用户的个人资料信息。
    """
    # 检查邮箱是否已被其他用户使用 (如果邮箱允许被修改)
    if user_update_data.email and user_update_data.email != current_user.email:
        existing_user = crud.user.get_by_email(
            db, email=user_update_data.email
        )  # _async removed
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(status_code=400, detail="此邮箱已被其他用户注册")

    # 检查用户名是否已被其他用户使用 (如果用户名允许被修改)
    if user_update_data.username and user_update_data.username != current_user.username:
        existing_user = crud.user.get_by_username(
            db, username=user_update_data.username
        )  # _async removed
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(status_code=400, detail="此用户名已被其他用户占用")

    updated_user = crud.user.update(
        db, db_obj=current_user, obj_in=user_update_data
    )  # _async removed
    return updated_user


@router.post("/me/change-password", response_model=Msg, summary="修改当前用户密码")
def change_current_user_password(
    password_data: UserPasswordUpdate,
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    修改当前登录用户的密码。
    """
    if not verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(status_code=400, detail="当前密码不正确")
    if password_data.new_password == password_data.current_password:
        raise HTTPException(status_code=400, detail="新密码不能与当前密码相同")

    hashed_new_password = get_password_hash(password_data.new_password)
    current_user.password_hash = hashed_new_password
    db.add(current_user)
    db.commit()
    return Msg(message="密码修改成功")


@router.put(
    "/me/avatar", response_model=UserAvatarUpdateResponse, summary="更新用户头像"
)
def update_user_avatar(
    file: UploadFile = File(..., description="新的头像文件 (jpg, png)"),
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    更新当前用户的头像。
    文件将保存到服务器，并在用户记录中更新头像URL。
    """
    if not file.content_type or file.content_type not in ["image/jpeg", "image/png"]:
        raise HTTPException(
            status_code=400, detail="头像文件格式无效，请上传 JPG 或 PNG 图片。"
        )

    # 文件大小限制 (例如5MB)
    MAX_AVATAR_SIZE = 5 * 1024 * 1024  # 5MB
    # For UploadFile, sync read is file.file.read()
    file_content = file.file.read()  # Changed from await file.read()
    file.file.seek(0)  # Changed from await file.seek(0)
    if len(file_content) > MAX_AVATAR_SIZE:
        raise HTTPException(
            status_code=413,
            detail=f"头像文件过大，请上传小于 {MAX_AVATAR_SIZE // (1024*1024)}MB 的图片。",
        )

    # 生成文件名并保存
    # 注意：生产环境中应使用更健壮的文件名生成和存储方案，如UUID + 内容哈希，并存储到对象存储服务
    file_extension = file.filename.split(".")[-1] if file.filename else "png"
    avatar_filename = f"avatar_user_{current_user.id}_{security.generate_random_string(8)}.{file_extension}"

    # 假设 save_upload_file_locally 和 get_file_path 是定义好的工具函数
    # UPLOAD_DIRECTORY 来自 settings.AVATARS_UPLOAD_DIR 或类似配置
    # Ensure security.save_upload_file_locally is synchronous or adapted
    security.save_upload_file_locally(
        settings.AVATARS_UPLOAD_DIR, file, avatar_filename
    )

    # 更新用户数据库中的 avatar_url
    # 假设 avatar_url 存储的是相对于服务器静态文件服务根目录的路径或完整URL
    avatar_url = f"/static/avatars/{avatar_filename}"  # 示例URL，取决于静态文件配置

    crud.user.update(
        db, db_obj=current_user, obj_in={"avatar_url": avatar_url}
    )  # _async removed

    return UserAvatarUpdateResponse(avatar_url=avatar_url)


# @router.get("/me/devices", response_model=List[DeviceResponse], summary="获取当前用户绑定的设备列表")
# def get_user_devices(
#     current_user: models.User = Depends(api_deps.get_current_active_user), # MODIFIED: UserModel to models.User
#     db: Session = Depends(api_deps.get_db),
# ):
#     """
#     获取当前用户已绑定的设备列表。
#     """
#     devices = crud.user_bound_device.get_multi_by_user_id(db, user_id=current_user.id) # _async removed
#     return devices

# @router.post("/me/devices/register", response_model=DeviceInfoResponse, summary="为当前用户注册新设备")
# def register_new_device_for_user(
#     device_data: DeviceRegisterRequest,
#     current_user: models.User = Depends(api_deps.get_current_active_user), # MODIFIED: UserModel to models.User
#     db: Session = Depends(api_deps.get_db),
# ):
#     """
#     为当前用户注册一个新的设备。
#     如果设备已存在但未绑定，则绑定到当前用户。
#     如果设备已存在且已绑定到其他用户，则根据策略处理（例如，报错或允许抢占）。
#     """
#     # 检查设备是否已存在
#     existing_device = crud.device.get_by_unique_id(db, unique_id=device_data.device_unique_id) # _async removed

#     if existing_device:
#         # 设备已存在，检查绑定情况
#         is_bound_to_current_user = crud.user_bound_device.is_device_bound_to_user( # _async removed
#             db, device_id=existing_device.id, user_id=current_user.id
#         )
#         if is_bound_to_current_user:
#             # 已经绑定到当前用户，直接返回设备信息或提示已绑定
#             return existing_device
#         else:
#             # 设备存在但未绑定到当前用户，或绑定到其他用户
#             # 检查是否绑定到其他活跃用户 (假设业务逻辑：一个设备只能被一个用户激活使用)
#             active_binding = crud.user_bound_device.get_active_binding_for_device(db, device_id=existing_device.id) # _async removed
#             if active_binding and active_binding.user_id != current_user.id:
#                 raise HTTPException(status_code=409, detail="此设备已被其他用户绑定并激活")

#             # 如果没有其他活跃绑定，或者允许覆盖，则创建新的绑定
#             # (可选：如果存在非活跃绑定到其他用户，可以先解绑或标记为不活跃)
#             crud.user_bound_device.create_binding(db, user_id=current_user.id, device_id=existing_device.id) # _async removed
#             return existing_device
#     else:
#         # 设备不存在，创建新设备并绑定
#         new_device_obj = crud.device.create_with_owner( # _async removed
#             db, obj_in=device_data, user_id=current_user.id # CRUD方法内部处理绑定
#         )
#         return new_device_obj

# @router.delete("/me/devices/{device_db_id}", response_model=Msg, summary="当前用户解绑设备")
# def unbind_device_for_user(
#     device_db_id: int, # 注意：这里用的是数据库中的 device ID，而不是 unique_id
#     current_user: models.User = Depends(api_deps.get_current_active_user), # MODIFIED: UserModel to models.User
#     db: Session = Depends(api_deps.get_db),
# ):
#     """
#     当前用户解绑指定的设备。
#     """
#     device_to_unbind = crud.device.get(db, id=device_db_id) # _async removed
#     if not device_to_unbind:
#         raise HTTPException(status_code=404, detail="未找到要解绑的设备")

#     unbound = crud.user_bound_device.remove_binding(db, user_id=current_user.id, device_id=device_db_id) # _async removed
#     if not unbound:
#         # 可能设备存在但并非由当前用户绑定，或绑定关系不存在
#         raise HTTPException(status_code=403, detail="无法解绑此设备，可能设备不属于您或绑定关系不存在")

#     return Msg(message="设备解绑成功")


@router.get(
    "/me/favorite-scores",
    response_model=List[ScoreResponse],
    summary="获取我收藏的乐谱",
)
def get_my_favorite_scores(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    获取当前用户收藏的乐谱列表。
    """
    favorite_scores = crud.user_favorite.get_multi_scores_by_user(  # _async removed
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return favorite_scores


@router.post("/me/scores/{score_id}/favorite", response_model=Msg, summary="收藏乐谱")
def add_score_to_favorites(
    score_id: int,
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    将指定乐谱添加到当前用户的收藏夹。
    """
    score = crud.score.get(db, id=score_id)  # _async removed
    if not score:
        raise HTTPException(status_code=404, detail="乐谱未找到")

    is_favorited = crud.user_favorite.is_favorited(
        db, user_id=current_user.id, score_id=score_id
    )  # _async removed
    if is_favorited:
        return Msg(message="乐谱已在您的收藏夹中")  # 或者返回 200 OK，表示操作无变化

    crud.user_favorite.create_favorite(
        db, user_id=current_user.id, score_id=score_id
    )  # _async removed
    return Msg(message="乐谱收藏成功")


@router.delete(
    "/me/scores/{score_id}/favorite", response_model=Msg, summary="取消收藏乐谱"
)
def remove_score_from_favorites(
    score_id: int,
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    db: Session = Depends(api_deps.get_db),
):
    """
    从当前用户的收藏夹中移除指定乐谱。
    """
    removed_count = crud.user_favorite.remove_favorite(
        db, user_id=current_user.id, score_id=score_id
    )  # _async removed
    if removed_count == 0:
        # 乐谱可能未被收藏，或者 score_id 无效
        # 为保持幂等性，即使未找到收藏记录，通常也返回成功，或一个特定消息
        return Msg(message="乐谱未在收藏夹中或已取消收藏")
    return Msg(message="乐谱取消收藏成功")


# @router.get("/me/score-attempts", response_model=List[UserScoreAttemptResponse], summary="获取我的演奏记录")
# def get_my_score_attempts(
#     score_id: Optional[int] = Query(None, description="按乐谱ID筛选"),
#     skip: int = Query(0, ge=0),
#     limit: int = Query(20, ge=1, le=100),
#     current_user: models.User = Depends(api_deps.get_current_active_user), # MODIFIED: UserModel to models.User
#     db: Session = Depends(api_deps.get_db),
# ):
#     """
#     获取当前用户的乐谱演奏尝试记录列表。
#     """
#     attempts = crud.user_score_attempt.get_multi_by_user( # _async removed
#         db, user_id=current_user.id, score_id=score_id, skip=skip, limit=limit
#     )
#     return attempts


@router.get(
    "/me/license",
    response_model=UserLicenseDetailsResponse,
    summary="查看当前用户的卡密信息",
)
def read_user_license_details(
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
) -> Any:
    """
    查看当前用户已激活的卡密详情和历史激活记录。
    """
    active_license_db = crud.license_key.get_user_active_license(
        db, user_id=current_user.id
    )

    active_license_schema: Optional[ActiveLicenseInfo] = None  # 直接使用
    if active_license_db and active_license_db.license_type:
        active_license_schema = ActiveLicenseInfo(  # 直接使用
            key_string=active_license_db.key_string,
            license_type_code=active_license_db.license_type.code,
            license_type_name=active_license_db.license_type.name,
            activated_at=active_license_db.activated_at,
            expires_at=active_license_db.expires_at,
            status=active_license_db.status.value,  # Enum 需要 .value
        )

    # 获取历史卡密 (这里简单地获取所有与该用户关联的卡密，可以根据需求细化)
    # 假设 crud.license_key.get_multi_by_user 获取的是所有关联卡密
    historical_licenses_db = crud.license_key.get_multi_by_user(
        db, user_id=current_user.id, limit=100
    )  # 获取最近100条

    history_schemas: List[HistoricalLicenseInfo] = []  # 直接使用
    for lic_db in historical_licenses_db:
        if lic_db.license_type:  # 确保 license_type 存在
            # 排除当前正在使用的活动卡密，避免重复显示
            if active_license_db and lic_db.id == active_license_db.id:
                continue

            history_schemas.append(
                HistoricalLicenseInfo(  # 直接使用
                    key_string=lic_db.key_string,
                    license_type_code=lic_db.license_type.code,
                    license_type_name=lic_db.license_type.name,
                    activated_at=lic_db.activated_at,
                    expires_at=lic_db.expires_at,
                    status=lic_db.status.value,  # Enum 需要 .value
                )
            )

    return UserLicenseDetailsResponse(  # 直接使用
        active_license=active_license_schema, history=history_schemas
    )


@router.post(
    "/me/license/activate",
    response_model=UserLicenseActivateResponse,
    summary="用户使用新卡密激活/续期",
)
def activate_or_renew_license(
    *,
    db: Session = Depends(api_deps.get_db),
    license_activation_request: UserLicenseActivateRequest = Body(...),  # 直接使用
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
) -> Any:
    """
    用户输入新的卡密字符串进行激活或为当前账户续期。
    续期时，新卡密的有效期将在原卡密到期时间基础上进行叠加。
    """
    key_string = license_activation_request.key_string
    license_to_activate = crud.license_key.get_license_key_by_string(
        db, key_string=key_string
    )

    if not license_to_activate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="提供的卡密不存在。"
        )

    if license_to_activate.status not in [
        models.LicenseKeyStatus.available,
        models.LicenseKeyStatus.consigned_available,
    ]:  # MODIFIED: LicenseKeyStatus
        # 如果卡密已被使用、过期或禁用
        if (
            license_to_activate.status == models.LicenseKeyStatus.used
            and license_to_activate.user_id == current_user.id
        ):  # MODIFIED: LicenseKeyStatus
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="此卡密您已激活。"
            )
        elif (
            license_to_activate.status == models.LicenseKeyStatus.used
        ):  # MODIFIED: LicenseKeyStatus
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="此卡密已被其他用户激活。"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"此卡密状态为 '{license_to_activate.status.value}'，无法激活。",
            )

    if (
        license_to_activate.user_id is not None
        and license_to_activate.user_id != current_user.id
    ):
        # 这种情况理论上应该被上面的 status check 覆盖，但作为额外检查
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail="此卡密已关联其他用户。"
        )

    license_type = license_to_activate.license_type
    if not license_type:
        # 这种情况不太可能发生，因为卡密创建时必须有关联类型
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="卡密类型信息缺失。",
        )

    # 检查卡密类型是否有效
    if not license_type.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该卡密对应的类型已失效，无法激活。",
        )

    # 1. 处理卡密激活与结算（针对寄售）- 此处简化，仅标记，实际结算逻辑应更复杂
    if (
        license_to_activate.consignment_reseller_id is not None
        and license_to_activate.status == models.LicenseKeyStatus.consigned_available
    ):  # MODIFIED: LicenseKeyStatus
        # TODO: 实现详细的寄售卡密结算逻辑，包括：
        # 1. 计算代理拿货成本 (cost_price)
        # 2. 从代理余额中扣除 cost_price (检查余额)
        # 3. 更新 license_keys.settlement_status
        # 暂时只更新状态
        license_to_activate.settlement_status = (
            LicenseSettlementStatus.pending
        )  # 直接使用
        pass  # 标记此处需要实现完整结算逻辑

    # 2. 更新卡密基础信息
    license_to_activate.user_id = current_user.id
    license_to_activate.status = (
        models.LicenseKeyStatus.used
    )  # MODIFIED: LicenseKeyStatus
    license_to_activate.activated_at = datetime.now(timezone.utc)

    # 计算过期时间
    current_active_license = crud.license_key.get_user_active_license(
        db, user_id=current_user.id
    )
    base_expiry_date = datetime.now(timezone.utc)

    if (
        current_active_license
        and current_active_license.expires_at
        and current_active_license.expires_at > base_expiry_date
    ):
        # 如果用户当前有未过期的有效许可，则在此基础上叠加
        base_expiry_date = current_active_license.expires_at
        # (可选) 如果旧卡密在被续期后需要做特殊处理 (例如标记为已续期)，可以在此操作
        # current_active_license.status = LicenseKeyStatus.expired # 或其他自定义状态
        # db.add(current_active_license)

    if license_type.duration_unit == LicenseDurationUnit.permanent:  # 直接使用
        license_to_activate.expires_at = None  # 永久卡密过期时间为 None
    else:
        duration_delta = timedelta()
        if license_type.duration_unit == LicenseDurationUnit.days:  # 直接使用
            duration_delta = timedelta(days=license_type.duration_value)
        elif license_type.duration_unit == LicenseDurationUnit.hours:  # 直接使用
            duration_delta = timedelta(hours=license_type.duration_value)
        elif license_type.duration_unit == LicenseDurationUnit.minutes:  # 直接使用
            duration_delta = timedelta(minutes=license_type.duration_value)

        if duration_delta:  # 确保有有效的时长增量
            license_to_activate.expires_at = base_expiry_date + duration_delta
        else:  # 如果时长单位无法识别或值为0，则按原样处理或报错
            license_to_activate.expires_at = base_expiry_date  # 至少是当前时间

    db.add(license_to_activate)

    # 3. 触发佣金计算 (如果适用) - 此处简化，仅标记
    if license_to_activate.consignment_reseller_id is not None:
        # TODO: 实现详细的佣金计算逻辑，与 /auth/activate-license-account 中的逻辑类似
        # 1. 确定佣金基准
        # 2. 计算并记录直接佣金 (如果平台有给销售代理的奖励)
        # 3. 计算并记录一级间接佣金 (给L2)
        # 4. 计算并记录二级间接佣金 (给L1)
        pass  # 标记此处需要实现佣金计算

    # (可选) 更新用户的 max_concurrent_devices
    # 如果新激活的卡密类型具有更高的设备限制，可以更新用户表的 max_concurrent_devices
    if license_type.max_concurrent_devices > current_user.max_concurrent_devices:
        current_user.max_concurrent_devices = license_type.max_concurrent_devices
        db.add(current_user)

    db.commit()
    db.refresh(license_to_activate)
    db.refresh(current_user)  # 如果更新了用户信息

    response_license_info = LicenseInfoBase(
        key_string=license_to_activate.key_string,
        license_type_code=license_type.code,
        license_type_name=license_type.name,
        expires_at=license_to_activate.expires_at,
        status=license_to_activate.status.value,
    )

    return UserLicenseActivateResponse(
        message="卡密激活/续期成功！", license_info=response_license_info
    )


@router.get(
    "/me/devices",
    response_model=DeviceListResponse,
    summary="获取当前用户绑定的设备列表",
)
def list_user_devices(
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    获取当前用户所有绑定的设备列表，以及绑定数量信息。
    """
    bound_devices_db = crud.user_bound_device.get_user_bound_devices(
        db, user_id=current_user.id, limit=100
    )  # 假设最多绑定100个，或根据需求调整

    device_responses: List[DeviceResponse] = []
    for device_db in bound_devices_db:
        # 判断会话是否活跃：session_token 存在且 last_active_at 在一定时间内 (例如5分钟内)
        # 这个逻辑可以根据实际需求调整，这里简单判断 session_token 是否存在
        is_session_active = bool(device_db.session_token)
        # 更精确的判断：
        # if device_db.session_token and device_db.last_active_at:
        #     is_session_active = (datetime.now(timezone.utc) - device_db.last_active_at) < timedelta(minutes=settings.SESSION_ACTIVE_TIMEOUT_MINUTES) # 假设有这个配置

        device_responses.append(
            DeviceResponse(
                device_id=device_db.device_id,
                device_alias=device_db.device_alias,
                bound_at=device_db.bound_at,
                last_active_at=device_db.last_active_at,
                current_session_active=is_session_active,
            )
        )

    meta_info = DeviceListMeta(
        current_bound_count=len(bound_devices_db),
        max_bound_count=current_user.max_concurrent_devices,
    )

    return DeviceListResponse(data=device_responses, meta=meta_info)


@router.put(
    "/me/devices/{device_id_path}",
    response_model=DeviceResponse,
    summary="修改用户指定绑定设备的别名",
)
def update_user_device_alias(
    *,
    db: Session = Depends(api_deps.get_db),
    device_id_path: str = Path(..., alias="deviceId", description="要修改的设备ID"),
    device_update_in: DeviceUpdate = Body(...),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    修改用户指定绑定设备的别名。
    路径参数名 deviceId 在Python中用 device_id_path 接收。
    """
    bound_device = crud.user_bound_device.get_user_bound_device(
        db, user_id=current_user.id, device_id=device_id_path
    )

    if not bound_device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的设备未绑定到当前用户或不存在。",
        )

    if not device_update_in.device_alias or not device_update_in.device_alias.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="设备别名不能为空。"
        )

    bound_device.device_alias = device_update_in.device_alias
    db.add(bound_device)
    db.commit()
    db.refresh(bound_device)

    return DeviceResponse(
        device_id=bound_device.device_id,
        device_alias=bound_device.device_alias,
        bound_at=bound_device.bound_at,
        last_active_at=bound_device.last_active_at,
        current_session_active=bool(bound_device.session_token),  # 简单判断
    )


@router.delete(
    "/me/devices/{device_id_path}/unbind",
    response_model=DeviceUnbindResponse,
    summary="用户解绑指定的设备",
)
def unbind_user_device(
    *,
    db: Session = Depends(api_deps.get_db),
    device_id_path: str = Path(..., alias="deviceId", description="要解绑的设备ID"),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
):
    """
    用户解绑指定的设备。
    包含解绑惩罚逻辑 (扣时长、计次)。
    """
    bound_device_to_unbind = crud.user_bound_device.get_user_bound_device(
        db, user_id=current_user.id, device_id=device_id_path
    )

    if not bound_device_to_unbind:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的设备未绑定到当前用户或不存在，无法解绑。",
        )

    # 1. 检查是否为最后一个绑定设备且有有效许可
    current_bound_devices_count = len(
        crud.user_bound_device.get_user_bound_devices(db, user_id=current_user.id)
    )
    active_license = crud.license_key.get_user_active_license(
        db, user_id=current_user.id
    )

    if current_bound_devices_count <= 1 and active_license:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="这是您最后一个绑定的设备，且您拥有有效的许可，无法解绑。",
        )

    time_deducted_message: Optional[str] = None

    # 2. 解绑惩罚逻辑
    if active_license and active_license.license_type:
        license_type = active_license.license_type
        if license_type.duration_unit != LicenseDurationUnit.permanent:
            # 2.a 时限卡: 扣除24小时
            if active_license.expires_at:
                new_expires_at = active_license.expires_at - timedelta(hours=24)
                # 确保不会早于当前时间
                if new_expires_at < datetime.now(timezone.utc):
                    # 如果扣除后早于当前，可以考虑直接让卡密过期或只扣到当前时间
                    # 这里简单处理为至少是当前时间，或者让卡密直接失效
                    active_license.expires_at = datetime.now(timezone.utc)
                    active_license.status = (
                        models.LicenseKeyStatus.expired
                    )  # MODIFIED: LicenseKeyStatus # 标记为过期
                else:
                    active_license.expires_at = new_expires_at

                db.add(active_license)
                time_deducted_message = "您的账户时长已成功扣除24小时。"
                # TODO: 记录到 user_device_unbind_logs 表
        else:
            # 2.b 永久卡: 计次
            current_month_str = datetime.now(timezone.utc).strftime("%Y-%m")
            if current_user.permanent_license_last_unbind_month != current_month_str:
                current_user.permanent_license_unbind_count_current_month = 0

            if (
                current_user.permanent_license_unbind_count_current_month is None
            ):  # 防御性编程
                current_user.permanent_license_unbind_count_current_month = 0

            if (
                current_user.permanent_license_unbind_count_current_month >= 1
            ):  # 假设每月只能解绑1次
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="永久许可用户本月解绑次数已达上限。",
                )

            current_user.permanent_license_unbind_count_current_month += 1
            current_user.permanent_license_last_unbind_month = current_month_str
            db.add(current_user)
            time_deducted_message = "永久许可用户本月解绑次数已记录。"
            # TODO: 记录到 user_device_unbind_logs 表

    # 3. 从 user_bound_devices 表删除记录
    db.delete(bound_device_to_unbind)

    # 4. (可选) 记录解绑日志 user_device_unbind_logs
    # crud.unbind_log.create_log(...) # 假设有此 CRUD 操作
    # 任务要求：如果其 CRUD 操作尚未创建，可以暂时跳过记录日志的步骤，或用注释标记。

    db.commit()
    # db.refresh(current_user) # 如果更新了用户表
    # if active_license: db.refresh(active_license) # 如果更新了卡密表

    return DeviceUnbindResponse(
        message="设备解绑成功。", time_deducted_message=time_deducted_message
    )


@router.get(
    "/me/favorites/",  # 对应 API 设计文档中的 /me/favorites/scores，但遵循任务描述的路径
    response_model=List[
        UserFavoriteResponse
    ],  # API 设计文档 2.9.1 (GET /me/favorites/scores)
    summary="获取当前用户收藏的谱曲列表",
    description="获取当前登录用户收藏的所有谱曲列表，支持分页。",
)
def read_user_favorite_scores(
    db: Session = Depends(api_deps.get_db),
    current_user: models.User = Depends(
        api_deps.get_current_active_user
    ),  # MODIFIED: UserModel to models.User
    skip: int = 0,
    limit: int = 20,
) -> Any:
    """
    获取当前用户的收藏列表。
    - **skip**: 分页参数，跳过的条目数。
    - **limit**: 分页参数，每页返回的条目数。
    - **current_user**: 当前认证用户。
    """
    favorites = crud.user_favorite.get_multi_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    # UserFavoriteResponse schema 中定义了 user 和 score 字段，
    # crud.user_favorite.get_multi_by_user 在查询时已通过 joinedload(UserFavorite.score) 预加载了谱曲信息
    # 如果需要 user 信息，也应在 CRUD 层预加载
    return favorites
