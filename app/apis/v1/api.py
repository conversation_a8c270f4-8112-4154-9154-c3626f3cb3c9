# app/apis/v1/api.py
from fastapi import APIRouter

# 从 endpoints 子包中导入各个模块的路由
from .endpoints import auth as auth_router  # 导入 auth.py 中定义的 router
from .endpoints import reseller_auth as reseller_auth_router  # 导入代理认证路由
from .endpoints import users_me as users_me_router  # 导入 users_me.py 中定义的 router
from .endpoints import (
    categories as categories_router,
)  # 导入 categories.py 中定义的 router
from .endpoints import scores as scores_router  # 导入 scores.py 中定义的 router
from .endpoints import (
    leaderboards as leaderboards_router,
)  # 导入 leaderboards.py 中定义的 router
from .endpoints import tickets as tickets_router  # 导入 tickets.py 中定义的 router
from .endpoints import (
    attachments as attachments_router,
)  # 导入 attachments.py 中定义的 router
from .endpoints import (
    resellers as resellers_router,
)  # 导入 resellers.py 中定义的 router

# 创建一个总的 API v1 路由对象
api_router_v1 = (
    APIRouter()
)  # 修改变量名以避免与导入的 auth_router 冲突，或在导入时重命名

# 在这里聚合 v1 版本下的所有模块路由
api_router_v1.include_router(
    auth_router.router, prefix="/auth", tags=["认证模块"]
)  # 使用 auth_router.router
api_router_v1.include_router(
    reseller_auth_router.router, prefix="/reseller-auth", tags=["代理认证模块"]
)  # 注册代理认证路由
api_router_v1.include_router(
    users_me_router.router, prefix="/users", tags=["用户接口 - 当前用户"]
)  # 注册当前用户相关接口
api_router_v1.include_router(
    categories_router.router, prefix="/categories", tags=["谱曲分类模块"]
)  # 注册分类接口
api_router_v1.include_router(
    scores_router.router, prefix="/scores", tags=["谱曲信息模块"]
)  # 注册谱曲接口
api_router_v1.include_router(
    leaderboards_router.router, prefix="/leaderboards", tags=["排行榜模块"]
)  # 注册排行榜接口
api_router_v1.include_router(
    tickets_router.router, prefix="/tickets", tags=["工单模块"]
)  # 注册工单接口
api_router_v1.include_router(
    attachments_router.router, prefix="/attachments", tags=["通用附件模块"]
)  # 注册附件接口
api_router_v1.include_router(
    resellers_router.router, prefix="/resellers", tags=["代理模块"]
)  # 注册代理模块接口
# router.include_router(items_router, prefix="/items", tags=["物品模块"])
# ... 其他模块的路由


# 示例: 添加一个简单的 v1 测试端点 (后续可以移除)
@api_router_v1.get("/test-v1", summary="V1 测试端点", tags=["测试"])
async def test_v1_endpoint():
    """
    一个简单的 V1 测试端点，用于确认 V1 路由是否正常工作。
    """
    return {"message": "欢迎来到 API Version 1!"}


# 注意：
# 1. `tags` 参数用于在 OpenAPI (Swagger) 文档中对端点进行分组。
# 2. `prefix` 参数用于为该组路由统一添加路径前缀。
#    例如，如果 auth_router 中有一个 /login 路由，
#    那么通过 router.include_router(auth_router, prefix="/auth") 注册后，
#    实际访问路径将是 /api/v1/auth/login (假设 /api/v1 是在 app/main.py 中为这个总路由设置的前缀)。

# 确保在 app/main.py 中取消注释并正确引入这个 router:
# from app.apis.v1.api import api_router_v1 # 确保使用更新后的变量名
# app.include_router(api_router_v1, prefix="/api/v1")
