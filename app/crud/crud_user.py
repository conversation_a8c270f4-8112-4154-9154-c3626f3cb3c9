# TanZouDaShiAPI/app/crud/crud_user.py
from typing import Any, Dict, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy import or_, func  # 引入 or_ 和 func

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app import models  # 统一导入 models
from app.schemas.user import (
    UserCreate,
    UserProfileUpdate,
    AdminUserCreate,
    AdminUserUpdate,
)  # 导入 Admin <PERSON>, UserUpdate 替换为 UserProfileUpdate
from datetime import datetime, timezone  # 导入 datetime 和 timezone


class CRUDUser(CRUDBase[models.User, UserCreate, UserProfileUpdate]):
    """
    用户相关的 CRUD 操作类。
    """

    def get_user_by_username(
        self, db: Session, *, username: str
    ) -> Optional[models.User]:
        """
        根据用户名获取用户。

        :param db: SQLAlchemy Session 对象。
        :param username: 用户名。
        :return: 用户模型实例或 None。
        """
        return db.query(models.User).filter(models.User.username == username).first()

    def get_user_by_email(self, db: Session, *, email: str) -> Optional[models.User]:
        """
        根据电子邮箱获取用户。

        :param db: SQLAlchemy Session 对象。
        :param email: 电子邮箱地址。
        :return: 用户模型实例或 None。
        """
        return db.query(models.User).filter(models.User.email == email).first()

    def create_user(self, db: Session, *, obj_in: UserCreate) -> models.User:
        """
        创建新用户。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: 包含用户创建信息的 Pydantic schema。
        :return: 创建的用户模型实例。
        """
        # 从 UserCreate schema 转换为字典，排除 verification_code
        create_data = obj_in.model_dump(exclude={"verification_code"})

        # 哈希密码
        hashed_password = get_password_hash(obj_in.password)
        create_data["password_hash"] = hashed_password
        del create_data["password"]  # 确保原始密码不被存储

        # 如果 UserCreate schema 中没有 role, status, max_concurrent_devices 等，
        # 数据库模型中定义的默认值会自动生效。
        # 如果需要在此处显式设置，可以添加：
        # create_data["role"] = models.UserRole.user # 例如
        # create_data["status"] = models.UserStatus.active # 例如

        db_obj = models.User(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_user(
        self,
        db: Session,
        *,
        db_obj: models.User,
        obj_in: Union[UserProfileUpdate, Dict[str, Any]],
    ) -> models.User:
        """
        更新用户信息。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 数据库中的用户模型实例。
        :param obj_in: 包含用户更新信息的 Pydantic schema 或字典。
        :return: 更新后的用户模型实例。
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # exclude_unset=True 表示只更新传入的字段
            update_data = obj_in.model_dump(exclude_unset=True)

        # 如果需要更新密码，应该通过专门的接口和逻辑处理，这里不直接处理密码更新
        # if "password" in update_data:
        #     hashed_password = get_password_hash(update_data["password"])
        #     update_data["password_hash"] = hashed_password
        #     del update_data["password"]

        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def authenticate_user(
        self, db: Session, *, username: str, password: str
    ) -> Optional[models.User]:
        """
        认证用户。

        :param db: SQLAlchemy Session 对象。
        :param username: 用户名。
        :param password: 用户密码。
        :return: 如果认证成功，返回用户模型实例；否则返回 None。
        """
        user = self.get_user_by_username(db, username=username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user

    def is_superuser(self, user: models.User) -> bool:
        """
        检查用户是否为超级用户 (admin)。

        :param user: 用户模型实例。
        :return: 如果用户角色是 admin，则返回 True；否则返回 False。
        """
        return user.role == models.UserRole.admin

    # get_user(db: Session, user_id: int) -> Optional[models.User]
    # 这个方法可以直接使用 CRUDBase 中的 get(id) 方法
    # def get_user(self, db: Session, user_id: int) -> Optional[models.User]:
    #     return super().get(db, id=user_id)

    def create_user_by_admin(
        self, db: Session, *, obj_in: AdminUserCreate
    ) -> models.User:
        """
        由管理员创建新用户。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: 包含用户创建信息的 Pydantic schema (AdminUserCreate)。
        :return: 创建的用户模型实例。
        """
        create_data = obj_in.model_dump(
            exclude_none=True
        )  # 使用 exclude_none=True 避免覆盖默认值

        hashed_password = get_password_hash(obj_in.password)
        create_data["password_hash"] = hashed_password
        del create_data["password"]

        create_data["creation_source"] = models.UserCreationSource.admin_created

        # role 和 status 已在 AdminUserCreate 中有默认值，如果传入则使用传入值
        # max_concurrent_devices 也是可选的

        db_obj = models.User(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_user_by_admin(
        self,
        db: Session,
        *,
        db_obj: models.User,
        obj_in: Union[AdminUserUpdate, Dict[str, Any]],
    ) -> models.User:
        """
        由管理员更新用户信息。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 数据库中的用户模型实例。
        :param obj_in: 包含用户更新信息的 Pydantic schema (AdminUserUpdate) 或字典。
        :return: 更新后的用户模型实例。
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)  # 只更新传入的字段

        # 管理员更新时，不应直接更新密码，密码重置应有专门接口
        if "password" in update_data:
            del update_data["password"]

        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def get_multi_by_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        role: Optional[models.UserRole] = None,
        status: Optional[models.UserStatus] = None,
    ) -> list[models.User]:
        """
        根据筛选条件获取用户列表（分页）。
        排除已软删除的用户 (deleted_at IS NULL)。

        :param db: SQLAlchemy Session 对象。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :param search: 用户名或邮箱的搜索关键词。
        :param role: 用户角色筛选。
        :param status: 用户状态筛选。
        :return: 用户模型实例列表。
        """
        query = db.query(self.model).filter(
            self.model.deleted_at.is_(None)
        )  # 排除软删除

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    models.User.username.ilike(search_term),
                    models.User.email.ilike(search_term),
                )
            )
        if role:
            query = query.filter(models.User.role == role)
        if status:
            query = query.filter(models.User.status == status)

        return query.order_by(models.User.id.desc()).offset(skip).limit(limit).all()

    def count_by_filters(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        role: Optional[models.UserRole] = None,
        status: Optional[models.UserStatus] = None,
    ) -> int:
        """
        根据筛选条件统计用户数量。
        排除已软删除的用户 (deleted_at IS NULL)。

        :param db: SQLAlchemy Session 对象。
        :param search: 用户名或邮箱的搜索关键词。
        :param role: 用户角色筛选。
        :param status: 用户状态筛选。
        :return: 符合条件的用户数量。
        """
        query = db.query(func.count(self.model.id)).filter(
            self.model.deleted_at.is_(None)
        )  # 排除软删除

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    models.User.username.ilike(search_term),
                    models.User.email.ilike(search_term),
                )
            )
        if role:
            query = query.filter(models.User.role == role)
        if status:
            query = query.filter(models.User.status == status)

        count = query.scalar()
        return count if count is not None else 0

    def deactivate_user(self, db: Session, *, db_obj: models.User) -> models.User:
        """
        逻辑删除用户 (标记为不活跃并记录删除时间)。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要逻辑删除的用户模型实例。
        :return: 更新后的用户模型实例。
        """
        db_obj.status = models.UserStatus.inactive  # 标记为不活跃
        db_obj.deleted_at = datetime.now(timezone.utc)  # 记录软删除时间
        # db_obj.is_active = False # 如果有 is_active 字段，也应更新
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


user = CRUDUser(models.User)
