# TanZouDaShiAPI/app/crud/crud_category.py
from typing import List, Optional

from sqlalchemy.orm import Session, joinedload

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.category import Category # 由 models 替代
from app.schemas.category import CategoryCreate, CategoryUpdate


class CRUDCategory(CRUDBase[models.Category, CategoryCreate, CategoryUpdate]):
    def get_by_name(
        self, db: Session, *, name: str, parent_category_id: Optional[int] = None
    ) -> Optional[models.Category]:
        """
        按名称和可选的父分类ID查找分类。
        因为分类名称在同一父分类下应该是唯一的。
        """
        query = db.query(self.model).filter(models.Category.name == name)
        if parent_category_id is None:
            query = query.filter(models.Category.parent_category_id.is_(None))
        else:
            query = query.filter(
                models.Category.parent_category_id == parent_category_id
            )
        return query.first()

    def get_by_slug(self, db: Session, *, slug: str) -> Optional[models.Category]:
        """
        按 slug 查找分类。Slug 是全局唯一的。
        """
        return db.query(self.model).filter(models.Category.slug == slug).first()

    def get_multi_with_parent_info(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[models.Category]:
        """
        获取分类列表，并预加载父分类信息以避免 N+1 查询问题。
        """
        return (
            db.query(self.model)
            .options(joinedload(self.model.parent_category))  # 预加载父分类
            .order_by(self.model.sort_order, self.model.name)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_active(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[models.Category]:
        """
        获取所有启用的顶级分类列表。
        """
        return (
            db.query(self.model)
            .filter(
                models.Category.is_active is True,
                models.Category.parent_category_id.is_(None),
            )
            .order_by(self.model.sort_order, self.model.name)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_children_of_category(
        self,
        db: Session,
        *,
        parent_id: int,
        skip: int = 0,
        limit: int = 100,
        only_active: bool = False,
    ) -> List[models.Category]:
        """
        获取指定父分类下的所有子分类。
        :param parent_id: 父分类的ID。
        :param only_active: 是否只返回启用的子分类。
        """
        query = db.query(self.model).filter(
            models.Category.parent_category_id == parent_id
        )
        if only_active:
            query = query.filter(models.Category.is_active is True)
        return (
            query.order_by(self.model.sort_order, self.model.name)
            .offset(skip)
            .limit(limit)
            .all()
        )


category = CRUDCategory(models.Category)
