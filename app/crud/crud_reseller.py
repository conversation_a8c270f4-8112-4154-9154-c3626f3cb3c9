# TanZouDaShiAPI/app/crud/crud_reseller.py
from typing import Optional, List
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import func

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.user import User, Reseller, ResellerLevel # 由 models 替代
from app.schemas.reseller import (
    ResellerLevelCreate,
    ResellerLevelUpdate,
    ResellerCreate,
    ResellerUpdate,
)
# from app.db.models.license import LicenseKey # 为 get_downline_count 导入 # 任务4：移除未使用的导入


class CRUDResellerLevel(
    CRUDBase[models.ResellerLevel, ResellerLevelCreate, ResellerLevelUpdate]
):
    def get_by_name(self, db: Session, *, name: str) -> Optional[models.ResellerLevel]:
        """
        按名称查找代理级别。

        :param db: SQLAlchemy Session 对象。
        :param name: 代理级别名称。
        :return: ResellerLevel 模型实例或 None。
        """
        return (
            db.query(models.ResellerLevel)
            .filter(models.ResellerLevel.name == name)
            .first()
        )

    def get_by_level_value(
        self, db: Session, *, level_value: int
    ) -> Optional[models.ResellerLevel]:
        """
        按级别数值查找代理级别。

        :param db: SQLAlchemy Session 对象。
        :param level_value: 代理级别数值。
        :return: ResellerLevel 模型实例或 None。
        """
        return (
            db.query(models.ResellerLevel)
            .filter(models.ResellerLevel.level_value == level_value)
            .first()
        )


class CRUDReseller(CRUDBase[models.Reseller, ResellerCreate, ResellerUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[models.Reseller]:
        """
        通过用户ID获取代理信息。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户ID。
        :return: Reseller 模型实例或 None。
        """
        return (
            db.query(models.Reseller).filter(models.Reseller.user_id == user_id).first()
        )

    # def get_by_invitation_code(self, db: Session, *, invitation_code: str) -> Optional[models.Reseller]:
    #     """
    #     通过邀请码获取代理信息。
    #     注意：当前 Reseller 模型没有直接的 invitation_code 字段。
    #     此方法假设邀请码存储在关联的 User 模型的某个字段，或者通过其他逻辑关联。
    #     这里我们假设 User 模型有一个 'invitation_code' 字段。

    #     :param db: SQLAlchemy Session 对象。
    #     :param invitation_code: 邀请码。
    #     :return: Reseller 模型实例或 None。
    #     """
    #     # 假设 User 模型有 invitation_code 字段
    #     user_with_code = db.query(models.User).filter(models.User.username == invitation_code).first() # 假设邀请码是用户名或特定字段
    #     if user_with_code:
    #         return db.query(models.Reseller).filter(models.Reseller.user_id == user_with_code.id).first()
    #     return None
    #     # 如果邀请码是 Reseller 表的某个字段，可以这样查询：
    #     # return db.query(models.Reseller).filter(models.Reseller.invitation_code_column == invitation_code).first()

    def update_balance(
        self,
        db: Session,
        *,
        db_obj: models.Reseller,
        amount_change: Decimal,
        is_commission: bool,
    ) -> models.Reseller:
        """
        更新代理余额。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新的 Reseller 模型实例。
        :param amount_change: 余额变动金额 (正数为增加，负数为减少)。
        :param is_commission: 标记此次变动是否为佣金增加。
        :return: 更新后的 Reseller 模型实例。
        """
        # 注意：并发安全问题。在高并发情况下，直接读取-修改-写入可能导致数据不一致。
        # 实际生产环境中，可能需要使用数据库级别的锁 (例如 SELECT ... FOR UPDATE)
        # 或者使用 F()表达式 (如果ORM支持) 来进行原子更新。
        # setattr(db_obj, 'balance', models.Reseller.balance + amount_change) # 这种方式不安全

        db_obj.balance += amount_change
        if is_commission and amount_change > Decimal(0):
            db_obj.total_commission_earned += amount_change

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_downline_count(self, db: Session, *, reseller_id: int) -> int:
        """
        获取下线用户数量 (直接下级代理数量)。

        :param db: SQLAlchemy Session 对象。
        :param reseller_id: 代理的用户ID。
        :return: 下线用户数量。
        """
        return (
            db.query(func.count(models.Reseller.user_id))
            .filter(models.Reseller.parent_reseller_user_id == reseller_id)
            .scalar()
            or 0
        )

    def get_multi_by_parent_reseller(
        self,
        db: Session,
        *,
        parent_reseller_user_id: int,
        skip: int = 0,
        limit: int = 100,
    ) -> List[models.Reseller]:
        """
        获取指定上级代理的所有直接下级代理。

        :param db: SQLAlchemy Session 对象。
        :param parent_reseller_user_id: 上级代理的用户 ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: Reseller 模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.Reseller.parent_reseller_user_id == parent_reseller_user_id)
            .offset(skip)
            .limit(limit)
            .all()
        )


# 实例化 CRUD 类
crud_reseller_level = CRUDResellerLevel(models.ResellerLevel)
crud_reseller = CRUDReseller(models.Reseller)
