# TanZouDaShiAPI/app/crud/crud_reseller_finance.py
from typing import Optional, List
from datetime import datetime

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase

# from app.db import models # Removed this line
from app.schemas.transaction import (
    CommissionRecordCreate,
    CommissionRecordUpdate,
    WithdrawalRecordUpdate,
    WithdrawalRecordCreateWithUser,
)
from app import models  # Use the app.models aggregator


class CRUDCommissionRecord(
    CRUDBase[models.CommissionRecord, CommissionRecordCreate, CommissionRecordUpdate]
):
    def get_multi_by_reseller(
        self, db: Session, *, reseller_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.CommissionRecord]:
        """
        获取指定代理的佣金记录。

        :param db: SQLAlchemy Session 对象。
        :param reseller_id: 代理的用户ID (受益人ID)。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: CommissionRecord 模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.CommissionRecord.beneficiary_reseller_user_id == reseller_id)
            .order_by(models.CommissionRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_license_key(
        self, db: Session, *, license_key_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.CommissionRecord]:
        """
        获取特定订单 (卡密) 产生的佣金记录。

        :param db: SQLAlchemy Session 对象。
        :param license_key_id: 卡密的ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: CommissionRecord 模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.CommissionRecord.source_license_id == license_key_id)
            .order_by(models.CommissionRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_source_reseller(
        self,
        db: Session,
        *,
        source_reseller_user_id: int,
        skip: int = 0,
        limit: int = 100,
    ) -> List[models.CommissionRecord]:
        """
        获取由特定源头代理销售产生的佣金记录。

        :param db: SQLAlchemy Session 对象。
        :param source_reseller_user_id: 源头销售代理的用户ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: CommissionRecord 模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(
                models.CommissionRecord.source_reseller_user_id
                == source_reseller_user_id
            )
            .order_by(models.CommissionRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        beneficiary_reseller_user_id: Optional[int] = None,
        source_license_id: Optional[int] = None,
        status: Optional[models.CommissionStatus] = None,  # 管理员可能需要按状态筛选
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> List[models.CommissionRecord]:
        """
        根据筛选条件获取佣金记录列表。
        """
        query = db.query(self.model)
        if beneficiary_reseller_user_id is not None:
            query = query.filter(
                models.CommissionRecord.beneficiary_reseller_user_id
                == beneficiary_reseller_user_id
            )
        if source_license_id is not None:
            query = query.filter(
                models.CommissionRecord.source_license_id == source_license_id
            )
        if status is not None:
            query = query.filter(models.CommissionRecord.status == status)
        if start_time:
            query = query.filter(models.CommissionRecord.created_at >= start_time)
        if end_time:
            query = query.filter(models.CommissionRecord.created_at <= end_time)

        return (
            query.order_by(models.CommissionRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDWithdrawalRecord(
    CRUDBase[
        models.WithdrawalRecord, WithdrawalRecordCreateWithUser, WithdrawalRecordUpdate
    ]
):
    def get_multi_by_reseller(
        self, db: Session, *, reseller_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.WithdrawalRecord]:
        """
        获取指定代理的提现记录。

        :param db: SQLAlchemy Session 对象。
        :param reseller_id: 代理的用户ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: WithdrawalRecord 模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.WithdrawalRecord.reseller_user_id == reseller_id)
            .order_by(models.WithdrawalRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        reseller_user_id: Optional[int] = None,
        status: Optional[models.WithdrawalStatus] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> List[models.WithdrawalRecord]:
        """
        根据筛选条件获取提现记录列表。
        """
        query = db.query(self.model)
        if reseller_user_id is not None:
            query = query.filter(
                models.WithdrawalRecord.reseller_user_id == reseller_user_id
            )
        if status is not None:
            query = query.filter(models.WithdrawalRecord.status == status)
        if start_time:
            query = query.filter(models.WithdrawalRecord.created_at >= start_time)
        if end_time:
            query = query.filter(models.WithdrawalRecord.created_at <= end_time)

        return (
            query.order_by(models.WithdrawalRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status(
        self,
        db: Session,
        *,
        db_obj: models.WithdrawalRecord,
        status: models.WithdrawalStatus,  # 直接使用导入的枚举类型
        processed_by_user_id: Optional[int] = None,  # 假设处理人是 User ID
        remarks: Optional[str] = None,
    ) -> models.WithdrawalRecord:
        """
        更新提现记录状态。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新的 WithdrawalRecord 模型实例。
        :param status: 新的提现状态。
        :param processed_by_user_id: 处理该请求的管理员用户ID (如果适用)。
        :param remarks: 管理员备注。
        :return: 更新后的 WithdrawalRecord 模型实例。
        """
        db_obj.status = status
        db_obj.processed_at = datetime.utcnow()  # 记录处理时间
        if remarks is not None:
            db_obj.admin_notes = remarks  # 更新管理员备注

        # 如果模型中有 'processed_by_user_id' 字段，则可以更新
        # if hasattr(db_obj, 'processed_by_user_id') and processed_by_user_id is not None:
        #     db_obj.processed_by_user_id = processed_by_user_id

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


# 实例化 CRUD 类
crud_commission_record = CRUDCommissionRecord(models.CommissionRecord)
crud_withdrawal_record = CRUDWithdrawalRecord(models.WithdrawalRecord)
