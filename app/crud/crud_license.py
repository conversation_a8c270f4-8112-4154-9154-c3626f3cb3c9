# TanZouDaShiAPI/app/crud/crud_license.py
from typing import Any, List, Optional, Sequence
from datetime import datetime, timezone
import uuid  # 用于生成批次ID和卡密密钥

from sqlalchemy.orm import Session, selectinload
from sqlalchemy import func  # 导入 and_ 用于组合查询条件

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.license import LicenseKey, LicenseType, LicenseKeyStatus, LicenseDurationUnit, LicenseSettlementStatus # 由 models 替代
# from app.db.models.user import User # 由 models 替代
from app.schemas.license import (
    LicenseKeyCreate,  # 已在 schemas/license.py 中重新定义
    LicenseKeyUpdate,  # 已在 schemas/license.py 中重新定义
    LicenseTypeCreateAdmin,  # 使用管理员版本的 Schema
    LicenseTypeUpdateAdmin,  # 使用管理员版本的 Schema
    AdminLicenseKeyBatchCreate,  # 新增的管理员批量创建 Schema
    # 新增的管理员更新 Schema
)
import random
import string


# 工具函数：生成随机卡密字符串
def generate_random_key_string(length: int = 16) -> str:
    characters = string.ascii_uppercase + string.digits
    return "".join(random.choice(characters) for i in range(length))


class CRUDLicenseType(
    CRUDBase[models.LicenseType, LicenseTypeCreateAdmin, LicenseTypeUpdateAdmin]
):
    """
    卡密类型相关的 CRUD 操作类。
    """

    def get_license_type_by_code(
        self, db: Session, *, code: str
    ) -> Optional[models.LicenseType]:
        """
        根据类型代码获取卡密类型。

        :param db: SQLAlchemy Session 对象。
        :param code: 卡密类型代码。
        :return: 卡密类型模型实例或 None。
        """
        return (
            db.query(models.LicenseType).filter(models.LicenseType.code == code).first()
        )

    # get_license_type(db: Session, license_type_id: int) -> Optional[models.LicenseType]
    # 这个方法可以直接使用 CRUDBase 中的 get(id) 方法

    # 根据需要添加创建、更新等方法，如果它们与基类不同的话
    # def create_license_type(self, db: Session, *, obj_in: LicenseTypeCreate) -> LicenseType:
    #     return super().create(db, obj_in=obj_in)

    # def update_license_type(
    #     self, db: Session, *, db_obj: LicenseType, obj_in: Union[LicenseTypeUpdate, Dict[str, Any]]
    # ) -> LicenseType:
    #     return super().update(db, db_obj=db_obj, obj_in=obj_in)


class CRUDLicenseKey(CRUDBase[models.LicenseKey, LicenseKeyCreate, LicenseKeyUpdate]):
    """
    卡密相关的 CRUD 操作类。
    """

    def get_license_key_by_string(
        self, db: Session, *, key_string: str
    ) -> Optional[models.LicenseKey]:
        """
        根据卡密字符串获取卡密。

        :param db: SQLAlchemy Session 对象。
        :param key_string: 卡密字符串。
        :return: 卡密模型实例或 None。
        """
        return (
            db.query(models.LicenseKey)
            .filter(models.LicenseKey.key_string == key_string)
            .first()
        )

    def create_license_key(
        self,
        db: Session,
        *,
        obj_in: LicenseKeyCreate,
        user_id: Optional[int] = None,  # 卡密激活用户
        issued_by_user_id: Optional[int] = None,  # 卡密发放者
        consignment_reseller_id: Optional[int] = None,  # 寄售代理
    ) -> models.LicenseKey:
        """
        创建新卡密。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: 包含卡密创建信息的 Pydantic schema。
        :param user_id: (可选) 激活该卡密的用户ID。
        :param issued_by_user_id: (可选) 发放该卡密的管理员/代理ID。
        :param consignment_reseller_id: (可选) 持有此寄售卡密的代理ID。
        :return: 创建的卡密模型实例。
        """
        create_data = obj_in.model_dump()
        if user_id is not None:
            create_data["user_id"] = user_id
        if issued_by_user_id is not None:
            create_data["issued_by_user_id"] = issued_by_user_id
        if consignment_reseller_id is not None:
            create_data["consignment_reseller_id"] = consignment_reseller_id

        # 卡密字符串 (key_string) 和类型 (license_type_id) 应该由 obj_in 提供
        # 状态 (status) 默认为 available，也可以在 obj_in 中指定

        db_obj = self.model(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def activate_license_key(
        self,
        db: Session,
        *,
        db_obj: models.LicenseKey,
        user_id: int,
        expires_at: datetime,
    ) -> models.LicenseKey:
        """
        激活卡密。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要激活的卡密模型实例。
        :param user_id: 激活用户的 ID。
        :param expires_at: 卡密激活后的过期时间。
        :return: 更新后的卡密模型实例。
        """
        update_data = {
            "user_id": user_id,
            "status": models.LicenseKeyStatus.used,
            "activated_at": datetime.now(
                db_obj.activated_at.tzinfo
                if db_obj.activated_at and db_obj.activated_at.tzinfo
                else None
            ),  # 保持时区一致性
            "expires_at": expires_at,
        }
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def update_license_key_status(
        self, db: Session, *, db_obj: models.LicenseKey, status: models.LicenseKeyStatus
    ) -> models.LicenseKey:
        """
        更新卡密状态。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新状态的卡密模型实例。
        :param status: 新的卡密状态。
        :return: 更新后的卡密模型实例。
        """
        return super().update(db, db_obj=db_obj, obj_in={"status": status})

    def get_user_active_license(
        self, db: Session, *, user_id: int
    ) -> Optional[models.LicenseKey]:
        """
        获取用户当前有效的激活卡密。
        一个用户理论上只应该有一个状态为 'used' 且未过期的卡密。
        如果存在多个，优先返回过期时间最晚的那个。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :return: 有效的卡密模型实例或 None。
        """
        now = datetime.now(
            timezone.utc if models.LicenseKey.expires_at.type.timezone else None
        )
        return (
            db.query(models.LicenseKey)
            .filter(models.LicenseKey.user_id == user_id)
            .filter(models.LicenseKey.status == models.LicenseKeyStatus.used)
            .filter(models.LicenseKey.expires_at > now)
            .order_by(models.LicenseKey.expires_at.desc())
            .first()
        )

    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.LicenseKey]:
        """
        获取指定用户的所有卡密（通常是已激活的）。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 卡密模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.LicenseKey.user_id == user_id)
            .order_by(models.LicenseKey.activated_at.desc())  # 按激活时间降序
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_batch_license_keys(
        self,
        db: Session,
        *,
        batch_create_input: AdminLicenseKeyBatchCreate,
        issued_by_user_id: Optional[int] = None,
    ) -> List[models.LicenseKey]:
        """
        批量生成卡密。

        :param db: SQLAlchemy Session 对象。
        :param batch_create_input: 包含批量创建信息的 Pydantic schema。
        :param issued_by_user_id: (可选) 发放这些卡密的管理员ID。
        :return: 创建的卡密模型实例列表。
        """
        license_type_obj = (
            db.query(models.LicenseType)
            .filter(models.LicenseType.id == batch_create_input.license_type_id)
            .first()
        )
        if not license_type_obj:
            raise ValueError(
                f"ID为 {batch_create_input.license_type_id} 的卡密类型不存在。"
            )
        if not license_type_obj.is_active:
            raise ValueError(
                f"卡密类型 '{license_type_obj.name}' 已被禁用，无法生成新的卡密。"
            )

        created_keys = []
        batch_id = str(uuid.uuid4())  # 为这一批次生成一个唯一的批次ID

        for _ in range(batch_create_input.quantity):
            key_string = generate_random_key_string()
            # 确保生成的 key_string 是唯一的
            while (
                db.query(models.LicenseKey)
                .filter(models.LicenseKey.key_string == key_string)
                .first()
            ):
                key_string = generate_random_key_string()

            key_data = LicenseKeyCreate(
                key_string=key_string,
                license_type_id=batch_create_input.license_type_id,
                status=models.LicenseKeyStatus.available
                if not batch_create_input.consignment_reseller_id
                else models.LicenseKeyStatus.consigned_available,
                issued_by_user_id=issued_by_user_id,
                consignment_reseller_id=batch_create_input.consignment_reseller_id,
                batch_id=batch_id,
                notes=batch_create_input.notes,
                settlement_status=models.LicenseSettlementStatus.not_applicable
                if not batch_create_input.consignment_reseller_id
                else models.LicenseSettlementStatus.pending,
            )
            # 注意：这里的 create_license_key 预期是 self.create 方法，或者一个更底层的创建方法
            # 如果使用 CRUDBase 的 create, 它期望 obj_in 是 Schema 类型
            db_obj = self.model(**key_data.model_dump())
            db.add(db_obj)
            created_keys.append(db_obj)

        db.commit()
        for key in created_keys:
            db.refresh(key)
        return created_keys

    def get_multi_with_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        key_string: Optional[str] = None,
        status: Optional[models.LicenseKeyStatus] = None,
        license_type_id: Optional[int] = None,
        # expires_at_from: Optional[datetime] = None, # 暂时不实现日期范围，简化
        # expires_at_to: Optional[datetime] = None,
        is_assigned_to_reseller: Optional[
            bool
        ] = None,  # consignment_reseller_id 是否非空
        is_used_by_user: Optional[bool] = None,  # user_id 是否非空
        batch_id: Optional[str] = None,
        issued_by_user_id: Optional[int] = None,
        consignment_reseller_id: Optional[int] = None,
        user_id: Optional[int] = None,  # 直接按使用者ID筛选
        sort_by: Optional[str] = "created_at",  # 默认排序字段
        sort_order: Optional[str] = "desc",  # 默认排序顺序
    ) -> Sequence[models.LicenseKey]:
        """
        根据多种筛选条件获取卡密列表 (管理员视角)。
        """
        query = db.query(self.model).options(
            selectinload(self.model.license_type),
            selectinload(self.model.user),
            selectinload(self.model.issuer),
            selectinload(self.model.consignment_reseller),
        )

        if key_string:
            query = query.filter(models.LicenseKey.key_string.ilike(f"%{key_string}%"))
        if status:
            query = query.filter(models.LicenseKey.status == status)
        if license_type_id is not None:
            query = query.filter(models.LicenseKey.license_type_id == license_type_id)
        if batch_id:
            query = query.filter(models.LicenseKey.batch_id == batch_id)
        if issued_by_user_id is not None:
            query = query.filter(
                models.LicenseKey.issued_by_user_id == issued_by_user_id
            )
        if consignment_reseller_id is not None:
            query = query.filter(
                models.LicenseKey.consignment_reseller_id == consignment_reseller_id
            )
        if user_id is not None:  # 筛选特定用户已使用的卡密
            query = query.filter(models.LicenseKey.user_id == user_id)

        if is_assigned_to_reseller is not None:
            if is_assigned_to_reseller:
                query = query.filter(
                    models.LicenseKey.consignment_reseller_id.isnot(None)
                )
            else:
                query = query.filter(
                    models.LicenseKey.consignment_reseller_id.is_(None)
                )

        if is_used_by_user is not None:
            if is_used_by_user:
                query = query.filter(models.LicenseKey.user_id.isnot(None))
            else:
                query = query.filter(models.LicenseKey.user_id.is_(None))

        # 排序逻辑
        if sort_by and hasattr(self.model, sort_by):
            column_to_sort = getattr(self.model, sort_by)
            if sort_order == "asc":
                query = query.order_by(column_to_sort.asc())
            else:
                query = query.order_by(column_to_sort.desc())
        else:  # 默认排序
            query = query.order_by(models.LicenseKey.created_at.desc())

        return query.offset(skip).limit(limit).all()

    def count_with_filters(
        self,
        db: Session,
        *,
        key_string: Optional[str] = None,
        status: Optional[models.LicenseKeyStatus] = None,
        license_type_id: Optional[int] = None,
        is_assigned_to_reseller: Optional[bool] = None,
        is_used_by_user: Optional[bool] = None,
        batch_id: Optional[str] = None,
        issued_by_user_id: Optional[int] = None,
        consignment_reseller_id: Optional[int] = None,
        user_id: Optional[int] = None,
    ) -> int:
        """
        根据多种筛选条件统计卡密数量。
        """
        query = db.query(func.count(self.model.id))

        if key_string:
            query = query.filter(models.LicenseKey.key_string.ilike(f"%{key_string}%"))
        if status:
            query = query.filter(models.LicenseKey.status == status)
        if license_type_id is not None:
            query = query.filter(models.LicenseKey.license_type_id == license_type_id)
        if batch_id:
            query = query.filter(models.LicenseKey.batch_id == batch_id)
        if issued_by_user_id is not None:
            query = query.filter(
                models.LicenseKey.issued_by_user_id == issued_by_user_id
            )
        if consignment_reseller_id is not None:
            query = query.filter(
                models.LicenseKey.consignment_reseller_id == consignment_reseller_id
            )
        if user_id is not None:
            query = query.filter(models.LicenseKey.user_id == user_id)

        if is_assigned_to_reseller is not None:
            if is_assigned_to_reseller:
                query = query.filter(
                    models.LicenseKey.consignment_reseller_id.isnot(None)
                )
            else:
                query = query.filter(
                    models.LicenseKey.consignment_reseller_id.is_(None)
                )

        if is_used_by_user is not None:
            if is_used_by_user:
                query = query.filter(models.LicenseKey.user_id.isnot(None))
            else:
                query = query.filter(models.LicenseKey.user_id.is_(None))

        count = query.scalar_one_or_none()
        return count if count is not None else 0

    # get_license_key(db: Session, license_key_id: int) -> Optional[models.LicenseKey]
    # 这个方法可以直接使用 CRUDBase 中的 get(id) 方法, 但为了加载关联数据，可以覆盖
    def get_with_details(self, db: Session, id: Any) -> Optional[models.LicenseKey]:
        """
        获取单个卡密详情，并预加载关联数据。
        """
        return (
            db.query(self.model)
            .options(
                selectinload(self.model.license_type),
                selectinload(self.model.user),
                selectinload(self.model.issuer),
                selectinload(self.model.consignment_reseller),
            )
            .filter(self.model.id == id)
            .first()
        )


license_type = CRUDLicenseType(models.LicenseType)
license_key = CRUDLicenseKey(models.LicenseKey)
