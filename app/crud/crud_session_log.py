# TanZouDaShiAPI/app/crud/crud_session_log.py
from typing import List, Optional
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import desc  # 导入 desc 用于排序

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.log import UserSessionLog # 由 models 替代
from app.schemas.log import SessionLogCreate, SessionLogUpdate  # 假设有这些 schemas


class CRUDSessionLog(
    CRUDBase[models.UserSessionLog, SessionLogCreate, SessionLogUpdate]
):
    """
    用户会话日志相关的 CRUD 操作类。
    """

    def create_user_session_log(
        self,
        db: Session,
        *,
        user_id: int,
        device_id: Optional[str] = None,  # device_id 可以是可选的
        ip_address: Optional[str] = None,
    ) -> models.UserSessionLog:
        """
        创建新的用户会话日志记录 (会话开始)。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param device_id: (可选) 设备唯一标识符。
        :param ip_address: (可选) 会话开始时的 IP 地址。
        :return: 创建的会话日志模型实例。
        """
        # session_start_at 会自动使用默认值 (当前时间)
        # duration_seconds 在会话开始时为 None
        log_data = {
            "user_id": user_id,
            "device_id": device_id,
            "ip_address": ip_address,
            "session_start_at": datetime.now(timezone.utc),  # 确保使用带时区的时间
        }
        # 使用 self.model 而不是直接 UserSessionLog，以保持与 CRUDBase 的一致性
        db_obj = self.model(**log_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def end_user_session_log(
        self, db: Session, *, session_log_id: int
    ) -> Optional[models.UserSessionLog]:
        """
        结束用户会话日志记录 (会话结束)。
        计算并填充会话时长。

        :param db: SQLAlchemy Session 对象。
        :param session_log_id: 要结束的会话日志记录的 ID。
        :return: 更新后的会话日志模型实例或 None (如果记录不存在)。
        """
        db_obj = self.get(db, id=session_log_id)
        if db_obj and db_obj.session_start_at and db_obj.session_end_at is None:
            session_end_at = datetime.now(
                db_obj.session_start_at.tzinfo or timezone.utc
            )  # 保持时区一致性
            duration = session_end_at - db_obj.session_start_at

            update_data = {
                "session_end_at": session_end_at,
                "duration_seconds": int(duration.total_seconds()),
            }
            return super().update(db, db_obj=db_obj, obj_in=update_data)
        return db_obj  # 如果会话已结束或记录不存在，则返回原始对象或None

    def get_latest_active_session(
        self, db: Session, *, user_id: int, device_id: Optional[str] = None
    ) -> Optional[models.UserSessionLog]:
        """
        获取用户在特定设备上最近一次未结束的活动会话。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param device_id: (可选) 设备唯一标识符。如果为None，则不按device_id筛选。
        :return: 最近的活动会话日志模型实例或 None。
        """
        query = (
            db.query(models.UserSessionLog)
            .filter(models.UserSessionLog.user_id == user_id)
            .filter(models.UserSessionLog.session_end_at.is_(None))  # type: ignore
            .order_by(desc(models.UserSessionLog.session_start_at))
        )
        if device_id:
            query = query.filter(models.UserSessionLog.device_id == device_id)

        return query.first()

    def get_session_logs_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserSessionLog]:
        """
        获取指定用户的所有会话日志，按开始时间降序排列。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 会话日志模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(models.UserSessionLog.user_id == user_id)
            .order_by(desc(models.UserSessionLog.session_start_at))
            .offset(skip)
            .limit(limit)
            .all()
        )


user_session_log = CRUDSessionLog(models.UserSessionLog)
