# TanZouDaShiAPI/app/crud/crud_score.py
from typing import List, Optional, Any, Dict, Union

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, or_, asc  # 确保导入 asc
from sqlalchemy.sql.expression import cast
from sqlalchemy import String as SQLString  # 用于 cast

from app.crud.base import CRUDBase
from app import models  # 导入所需 Enum
from app.schemas.score import (
    ScoreCreate,
    ScoreUpdate,
    AdminScoreUpdate,
    ScoreStatusUpdate,
)  # 导入 AdminScoreUpdate 和 ScoreStatusUpdate
from fastapi.encoders import jsonable_encoder
from datetime import datetime, timezone  # 导入 datetime 和 timezone


class CRUDScore(CRUDBase[models.Score, ScoreCreate, ScoreUpdate]):
    def get_by_title(self, db: Session, *, title: str) -> Optional[models.Score]:
        """
        按标题查找谱曲。
        """
        return db.query(self.model).filter(models.Score.title == title).first()

    def get_multi_with_filtering_and_sorting(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 20,
        sort_by: Optional[str] = None,
        order: Optional[str] = "desc",  # asc or desc
        category_id: Optional[int] = None,
        uploader_user_id: Optional[int] = None,
        search_keyword: Optional[str] = None,
        score_type: Optional[models.ScoreType] = None,
        difficulty: Optional[models.ScoreDifficulty] = None,
        status: Optional[models.ScoreStatus] = None,  # 管理员接口默认获取所有状态
        is_premium_only: Optional[bool] = None,
        tags_list: Optional[List[str]] = None,
    ) -> tuple[List[models.Score], int]:
        """
        获取谱曲列表，支持筛选、排序和分页。
        返回谱曲列表和符合条件的总数。
        """
        query = db.query(self.model).options(
            joinedload(self.model.uploader),  # 预加载上传者信息
            joinedload(self.model.category),  # 预加载分类信息
        )

        # 应用筛选条件
        if status:
            query = query.filter(self.model.status == status)
        if category_id is not None:
            query = query.filter(self.model.category_id == category_id)
        if uploader_user_id is not None:
            query = query.filter(self.model.uploader_user_id == uploader_user_id)
        if score_type:
            query = query.filter(self.model.type == score_type)
        if difficulty:
            query = query.filter(self.model.difficulty == difficulty)
        if is_premium_only is not None:
            query = query.filter(self.model.is_premium_only == is_premium_only)

        if search_keyword:
            keyword_like = f"%{search_keyword}%"
            query = query.filter(
                or_(
                    self.model.title.ilike(keyword_like),
                    self.model.description.ilike(keyword_like),
                    # 对于 JSON 类型的 tags，搜索方式比较复杂
                    # MySQL: JSON_CONTAINS(Score.tags, JSON_QUOTE(keyword_like_for_json))
                    # 或者将 tags 转换为字符串搜索，但不精确
                    # 此处简化为字符串转换搜索，实际可能需要更精确的 JSON 查询
                    cast(self.model.tags, SQLString).ilike(keyword_like),
                )
            )

        if tags_list:
            # 搜索包含所有指定标签的谱曲
            # 这需要更复杂的 JSON 查询，例如对每个 tag 使用 JSON_CONTAINS
            # 简化处理：如果 tags_list 只有一个元素，可以像上面那样搜索
            # 如果有多个，可能需要动态构建 filter 条件
            # 此处暂时忽略多标签精确匹配的复杂性，或假定 tags_list 只有一个元素
            if len(tags_list) == 1:
                tag_like = f"%{tags_list[0]}%"
                query = query.filter(cast(self.model.tags, SQLString).ilike(tag_like))
            # 若要精确匹配所有标签，可能需要类似：
            # for tag_item in tags_list:
            #     query = query.filter(func.json_contains(self.model.tags, func.json_array(tag_item)))

        # 获取总数 (在应用分页之前)
        total_count = query.count()

        # 应用排序
        if sort_by:
            sort_column = getattr(self.model, sort_by, None)
            if sort_column:
                if order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
            else:  # 默认按创建时间降序
                query = query.order_by(desc(self.model.created_at))
        else:
            query = query.order_by(desc(self.model.created_at))

        # 应用分页
        items = query.offset(skip).limit(limit).all()
        return items, total_count

    def get_multi_by_uploader(
        self, db: Session, *, uploader_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.Score]:
        """
        获取特定用户上传的谱曲列表。
        """
        return (
            db.query(self.model)
            .filter(models.Score.uploader_user_id == uploader_id)
            .order_by(desc(models.Score.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_category(
        self,
        db: Session,
        *,
        category_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[
            models.ScoreStatus
        ] = models.ScoreStatus.approved,  # 默认只获取已批准的
    ) -> List[models.Score]:
        """
        获取特定分类下的谱曲列表。
        可以指定谱曲状态。
        """
        query = db.query(self.model).filter(models.Score.category_id == category_id)
        if status:
            query = query.filter(models.Score.status == status)
        return (
            query.order_by(desc(models.Score.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status(
        self,
        db: Session,
        *,
        db_obj: models.Score,
        status_in: ScoreStatusUpdate,  # 使用 ScoreStatusUpdate Schema
        approver_id: int,  # 审核者ID，由依赖传入
    ) -> models.Score:
        """
        更新谱曲的状态和审核信息。
        """
        db_obj.status = status_in.status
        db_obj.approval_remarks = status_in.approval_remarks
        db_obj.approved_by_user_id = approver_id
        db_obj.approved_at = datetime.now(timezone.utc)  # 记录审核时间

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def create(self, db: Session, *, obj_in: ScoreCreate) -> models.Score:
        """
        创建新的谱曲记录。
        特别处理 tags 字段，Pydantic schema 接收 List[str]，SQLAlchemy 模型存储为 JSON。
        jsonable_encoder 应该能正确处理 List[str] 到 JSON 的转换。
        """
        obj_in_data = jsonable_encoder(obj_in)
        # tags 字段在 ScoreCreate schema 中已经是 List[str]，
        # SQLAlchemy 的 JSON 类型可以直接接受 Python list/dict
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: models.Score,
        obj_in: Union[
            ScoreUpdate, AdminScoreUpdate, Dict[str, Any]
        ],  # 允许 AdminScoreUpdate
    ) -> models.Score:
        """
        更新现有谱曲记录。
        特别处理 tags 字段。
        管理员更新可能包含 status 等字段。
        """
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # exclude_unset=True 确保只更新传入的字段
            update_data = obj_in.model_dump(exclude_unset=True)

        # 如果 tags 在 update_data 中，确保它是 list (Pydantic schema 会处理)
        # SQLAlchemy 的 JSON 类型可以直接接受 Python list/dict
        for field in obj_data:  # 遍历模型本身的字段
            if field in update_data:  # 如果传入数据中有这个字段
                setattr(db_obj, field, update_data[field])

        # 如果是 AdminScoreUpdate 并且传入了 status，确保 status 被更新
        # ScoreUpdate schema 默认也有 status，但 AdminScoreUpdate 可能有更宽松的校验或不同逻辑
        # 这里的 for 循环已经可以处理 status 字段的更新，因为 AdminScoreUpdate 继承自 ScoreUpdate，
        # 并且 ScoreUpdate 包含了 status 字段。
        # 如果 AdminScoreUpdate 中有 ScoreUpdate 没有的字段，且需要在 CRUD层面特殊处理，则在这里添加逻辑。
        # 例如，如果 AdminScoreUpdate 允许直接修改 approved_by_user_id (但不推荐，应走审核流程)
        # if 'approved_by_user_id' in update_data and isinstance(obj_in, AdminScoreUpdate):
        #     db_obj.approved_by_user_id = update_data['approved_by_user_id']
        # if 'approval_remarks' in update_data and isinstance(obj_in, AdminScoreUpdate):
        #      db_obj.approval_remarks = update_data['approval_remarks']

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def increment_view_count(
        self, db: Session, *, score_id: int
    ) -> Optional[models.Score]:
        """
        增加谱曲的查看次数。
        """
        db_obj = self.get(db, id=score_id)
        if db_obj:
            db_obj.view_count = (db_obj.view_count or 0) + 1
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def search_scores(
        self,
        db: Session,
        *,
        keyword: str,
        skip: int = 0,
        limit: int = 100,
        status: models.ScoreStatus = models.ScoreStatus.approved,
    ) -> List[models.Score]:
        """
        根据关键词搜索谱曲 (标题、描述、标签)。
        默认只搜索已批准的谱曲。
        """
        keyword_like = f"%{keyword}%"
        query = (
            db.query(self.model)
            .filter(models.Score.status == status)
            .filter(
                or_(
                    models.Score.title.ilike(keyword_like),
                    models.Score.description.ilike(keyword_like),
                    # 对于 JSON 类型的 tags，搜索方式可能需要数据库特定函数
                    # 例如 PostgreSQL: Score.tags.op('?')(keyword)
                    # MySQL: JSON_CONTAINS(Score.tags, '\"' + keyword + '\"')
                    # 这里使用一个通用的 ilike，如果 tags 是简单字符串列表可能部分有效
                    # 更健壮的方式是遍历 tags 列表进行比较，或者使用数据库的 JSON 查询功能
                    # 此处简化处理，实际项目中可能需要更复杂的查询
                    cast(models.Score.tags, SQLString).ilike(keyword_like),
                )
            )
        )
        return (
            query.order_by(desc(models.Score.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )


score = CRUDScore(models.Score)
