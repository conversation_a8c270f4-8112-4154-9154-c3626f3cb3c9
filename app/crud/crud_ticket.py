from pydantic import BaseModel

# TanZouDaShiAPI/app/crud/crud_ticket.py
from datetime import datetime, timezone
from typing import List, Optional, Union, Dict, Any
import uuid  # 用于生成唯一的工单号部分

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app import (
    models as app_models,
)  # 使用 app_models 避免与参数名冲突，或者确保参数名不叫 models
from app.schemas.ticket import (
    TicketCreate,
    TicketUpdate,
    TicketUpdateByAdmin,
    TicketMessageCreate,
)
# from app.db.models.ticket import Ticket, TicketMessage, TicketStatus, TicketPriority # 由 app_models 替代


class CRUDTicket(CRUDBase[app_models.Ticket, TicketCreate, TicketUpdate]):
    """工单对象的 CRUD 操作"""

    def _generate_ticket_number(self) -> str:
        """生成唯一的工单号 (例如 TK-YYYYMMDD-XXXXXX)"""
        now = datetime.now(timezone.utc)
        date_str = now.strftime("%Y%m%d")
        unique_part = uuid.uuid4().hex[:6].upper()  # 使用UUID生成6位唯一部分
        return f"TK-{date_str}-{unique_part}"

    def create(
        self, db: Session, *, obj_in: TicketCreate, user_id: int
    ) -> app_models.Ticket:
        """
        创建新工单。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: Pydantic schema 实例，包含创建所需数据。
        :param user_id: 创建工单的用户ID。
        :return: 创建的工单模型实例。
        """
        ticket_number = self._generate_ticket_number()
        # 确保生成的 ticket_number 是唯一的
        while (
            db.query(self.model)
            .filter(self.model.ticket_number == ticket_number)
            .first()
        ):
            ticket_number = self._generate_ticket_number()

        create_data = obj_in.model_dump()
        db_obj = self.model(
            **create_data,
            user_id=user_id,
            ticket_number=ticket_number,
            status=app_models.TicketStatus.open,  # 新工单默认为开启状态
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_by_user(
        self,
        db: Session,
        *,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        is_assignee: bool = False,
    ) -> List[app_models.Ticket]:
        """
        获取特定用户创建的或分配给特定用户的工单列表。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :param is_assignee: 如果为True，则查询分配给该用户的工单，否则查询该用户创建的工单。
        :return: 工单模型实例列表。
        """
        query = db.query(self.model)
        if is_assignee:
            query = query.filter(self.model.assignee_user_id == user_id)
        else:
            query = query.filter(self.model.user_id == user_id)
        return (
            query.order_by(self.model.updated_at.desc()).offset(skip).limit(limit).all()
        )

    def get_multi_by_status(
        self,
        db: Session,
        *,
        status: app_models.TicketStatus,
        skip: int = 0,
        limit: int = 100,
    ) -> List[app_models.Ticket]:
        """
        按状态获取工单列表。

        :param db: SQLAlchemy Session 对象。
        :param status: 工单状态。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 工单模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(self.model.status == status)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_status(
        self,
        db: Session,
        *,
        db_obj: app_models.Ticket,
        status: app_models.TicketStatus,
        updated_by_user_id: Optional[int] = None,  # pylint: disable=unused-argument
    ) -> app_models.Ticket:
        """
        更新工单状态，并记录更新者 (当前版本中 updated_by_user_id 未直接存储，但保留参数用于未来审计)。
        如果状态变为 resolved 或 closed，则更新 resolved_at 或 closed_at 时间。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新的工单模型实例。
        :param status: 新的工单状态。
        :param updated_by_user_id: 执行更新操作的用户ID (可选)。
        :return: 更新后的工单模型实例。
        """
        now = datetime.now(timezone.utc)
        update_data = {"status": status, "updated_at": now}
        if status == app_models.TicketStatus.resolved and not db_obj.resolved_at:
            update_data["resolved_at"] = now
            # 如果之前是 closed，重新 resolved，则清空 closed_at
            if db_obj.closed_at:
                update_data["closed_at"] = None
        elif status == app_models.TicketStatus.closed and not db_obj.closed_at:
            update_data["closed_at"] = now
            # 如果解决后关闭，确保 resolved_at 也被设置 (如果尚未设置)
            if not db_obj.resolved_at:
                update_data["resolved_at"] = now
        elif (
            status == app_models.TicketStatus.open
            or status == app_models.TicketStatus.reopened
            or status == app_models.TicketStatus.in_progress
            or status == app_models.TicketStatus.awaiting_reply
        ):
            # 如果从 resolved/closed 状态重新打开或处理，清空 resolved_at 和 closed_at
            if db_obj.resolved_at:
                update_data["resolved_at"] = None
            if db_obj.closed_at:
                update_data["closed_at"] = None

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def assign_ticket(
        self,
        db: Session,
        *,
        db_obj: app_models.Ticket,
        assignee_user_id: int,
        updated_by_user_id: Optional[int] = None,  # pylint: disable=unused-argument
    ) -> app_models.Ticket:
        """
        指派工单给处理人。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要指派的工单模型实例。
        :param assignee_user_id: 新的负责人用户ID。
        :param updated_by_user_id: 执行更新操作的用户ID (可选)。
        :return: 更新后的工单模型实例。
        """
        update_data = {
            "assignee_user_id": assignee_user_id,
            "updated_at": datetime.now(timezone.utc),
        }
        # 如果工单当前状态是 open，指派后可以考虑自动更新为 in_progress
        if db_obj.status == app_models.TicketStatus.open:
            update_data["status"] = app_models.TicketStatus.in_progress

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: app_models.Ticket,
        obj_in: Union[TicketUpdate, TicketUpdateByAdmin, Dict[str, Any]],
    ) -> app_models.Ticket:
        """
        更新工单。
        特别处理 status, assignee_user_id 的更新，因为它们有特定的方法。
        其他字段如 priority, category 可以通过此通用方法更新。
        """
        update_data: Dict[str, Any]
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # 如果更新数据中包含 status，则调用 update_status
        if "status" in update_data and update_data["status"] is not None:
            # updated_by_user_id 可以从认证信息中获取，这里暂时不传递
            self.update_status(db=db, db_obj=db_obj, status=update_data["status"])
            del update_data["status"]  # 从 update_data 中移除，避免重复更新

        # 如果更新数据中包含 assignee_user_id，则调用 assign_ticket
        if (
            "assignee_user_id" in update_data
            and update_data["assignee_user_id"] is not None
        ):
            self.assign_ticket(
                db=db, db_obj=db_obj, assignee_user_id=update_data["assignee_user_id"]
            )
            del update_data["assignee_user_id"]  # 从 update_data 中移除

        # 更新其他允许的字段
        # 父类的 update 方法会处理通用字段，但这里我们已经细化了 status 和 assignee_user_id
        # 所以我们只处理剩余的字段，例如 priority, category
        # 注意：TicketUpdate schema 中目前只定义了 status, priority, assignee_user_id
        # 如果要更新 title, description 等，需要扩展 TicketUpdate/TicketUpdateByAdmin schema
        # TicketUpdateByAdmin 增加了 category 字段
        # 父类的 update 方法会处理通用字段，但这里我们已经细化了 status 和 assignee_user_id
        # 所以我们只处理剩余的字段，例如 priority, category

        # 获取当前对象所有可写字段的 Pydantic 模型，以便比较
        # obj_data = jsonable_encoder(db_obj) # Pydantic v1 style, or db_obj.model_dump() for v2
        # Pydantic v2: db_obj is a SQLAlchemy model, not a Pydantic model.
        # We need to iterate through fields in update_data that are also attributes of db_obj.

        changed_fields = False
        allowed_fields_to_update = ["priority", "category"]  # 明确可更新的字段

        for field in allowed_fields_to_update:
            if field in update_data and update_data[field] is not None:
                if (
                    hasattr(db_obj, field)
                    and getattr(db_obj, field) != update_data[field]
                ):
                    setattr(db_obj, field, update_data[field])
                    changed_fields = True

        # 如果有任何字段（包括 status, assignee_user_id 或其他）被更改，则提交
        # 之前对 status 和 assignee_user_id 的更新已经调用了 db.commit()
        # 这里我们需要确保如果只有 category 或 priority 更新了，也会提交
        # 并且统一更新 updated_at

        # 检查是否有任何实际的更新操作发生（包括通过特定方法更新的 status 和 assignee_user_id）
        # is_status_updated = "status" in update_data and update_data["status"] is not None
        # is_assignee_updated = "assignee_user_id" in update_data and update_data["assignee_user_id"] is not None

        # 如果有任何更改（包括 status, assignee_user_id 或其他字段），则更新 updated_at 并提交
        # 之前的 update_status 和 assign_ticket 内部已经 commit 了，这里需要判断是否还有其他字段更新
        if changed_fields:  # 如果 priority 或 category 更新了
            db_obj.updated_at = datetime.now(timezone.utc)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
        # 如果只有 status 或 assignee_user_id 更新，它们各自的方法已处理 commit 和 refresh
        # 但仍需确保 db_obj 是最新的
        elif "status" in update_data or "assignee_user_id" in update_data:
            db.refresh(db_obj)  # 确保返回的对象是最新的

        return db_obj

    def get_with_messages(self, db: Session, id: int) -> Optional[app_models.Ticket]:
        """获取单个工单及其所有消息 (使用 eager loading)"""
        from sqlalchemy.orm import selectinload

        return (
            db.query(self.model)
            .options(selectinload(self.model.messages))
            .filter(self.model.id == id)
            .first()
        )

    def get_multi_with_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        user_id: Optional[int] = None,
        status: Optional[app_models.TicketStatus] = None,
        priority: Optional[app_models.TicketPriority] = None,
        ticket_type: Optional[str] = None,  # 对应 model 中的 category 字段
        title_search: Optional[str] = None,
        created_at_start: Optional[datetime] = None,
        created_at_end: Optional[datetime] = None,
    ) -> List[app_models.Ticket]:
        """
        根据多个筛选条件获取工单列表 (分页)。
        """
        query = db.query(self.model)

        if user_id is not None:
            query = query.filter(self.model.user_id == user_id)
        if status is not None:
            query = query.filter(self.model.status == status)
        if priority is not None:
            query = query.filter(self.model.priority == priority)
        if ticket_type is not None:  # 'ticket_type' 对应 'category'
            query = query.filter(self.model.category == ticket_type)
        if title_search:
            query = query.filter(self.model.title.ilike(f"%{title_search}%"))
        if created_at_start:
            query = query.filter(self.model.created_at >= created_at_start)
        if created_at_end:
            query = query.filter(self.model.created_at <= created_at_end)

        return (
            query.order_by(self.model.updated_at.desc()).offset(skip).limit(limit).all()
        )

    def count_with_filters(
        self,
        db: Session,
        *,
        user_id: Optional[int] = None,
        status: Optional[app_models.TicketStatus] = None,
        priority: Optional[app_models.TicketPriority] = None,
        ticket_type: Optional[str] = None,
        title_search: Optional[str] = None,
        created_at_start: Optional[datetime] = None,
        created_at_end: Optional[datetime] = None,
    ) -> int:
        """
        根据多个筛选条件统计工单数量。
        """
        query = db.query(self.model.id)  # 只查询id以提高效率

        if user_id is not None:
            query = query.filter(self.model.user_id == user_id)
        if status is not None:
            query = query.filter(self.model.status == status)
        if priority is not None:
            query = query.filter(self.model.priority == priority)
        if ticket_type is not None:
            query = query.filter(self.model.category == ticket_type)
        if title_search:
            query = query.filter(self.model.title.ilike(f"%{title_search}%"))
        if created_at_start:
            query = query.filter(self.model.created_at >= created_at_start)
        if created_at_end:
            query = query.filter(self.model.created_at <= created_at_end)

        return query.count()


class CRUDTicketMessage(
    CRUDBase[app_models.TicketMessage, TicketMessageCreate, BaseModel]
):  # UpdateSchema 未定义，用BaseModel占位
    """工单消息对象的 CRUD 操作"""

    def create_with_ticket_update(
        self,
        db: Session,
        *,
        obj_in: TicketMessageCreate,
        ticket: app_models.Ticket,
        sender_id: int,
        sender_is_admin_or_agent: bool,  # 假设有一个参数判断发送者是否为客服/管理员
    ) -> app_models.TicketMessage:
        """
        创建消息并可能更新工单的 updated_at 和 last_reply_at 时间戳，
        以及根据消息发送者更新工单状态。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: Pydantic schema 实例，包含消息创建所需数据。
        :param ticket: 关联的工单模型实例。
        :param sender_id: 发送消息的用户ID。
        :param sender_is_admin_or_agent: 布尔值，指示发送者是否为管理员或客服。
        :return: 创建的工单消息模型实例。
        """
        create_data = obj_in.model_dump()
        db_message = self.model(**create_data, ticket_id=ticket.id, sender_id=sender_id)
        db.add(db_message)

        # 更新工单的时间戳和状态
        now = datetime.now(timezone.utc)
        ticket.updated_at = now
        ticket.last_reply_at = now

        # 根据发送者和当前工单状态更新工单状态
        if sender_is_admin_or_agent:  # 客服/管理员回复
            if not obj_in.is_internal:  # 只有非内部消息才改变状态为等待用户回复
                # 管理员回复（非内部），将工单状态更新为等待用户回复
                # 即使工单是 open, in_progress, reopened, awaiting_reply (用户刚回复完，管理员又回复)
                # 只要不是 closed 或 resolved，都应变为 awaiting_reply
                if ticket.status not in [
                    app_models.TicketStatus.closed,
                    app_models.TicketStatus.resolved,
                ]:
                    ticket.status = (
                        app_models.TicketStatus.awaiting_reply
                    )  # 等待用户回复
        else:  # 用户回复
            # 用户回复
            # 如果工单是 open, awaiting_reply (管理员刚回复完，用户回复)，则变为 in_progress
            # 如果工单是 in_progress (客服标记处理中，用户补充信息)，保持 in_progress 或根据业务逻辑调整
            # 如果工单是 reopened (之前已解决/关闭，用户重新打开后再次回复)，保持 reopened 或变为 in_progress
            if (
                ticket.status == app_models.TicketStatus.open
                or ticket.status == app_models.TicketStatus.awaiting_reply
            ):
                ticket.status = (
                    app_models.TicketStatus.in_progress
                )  # 用户回复后，状态变为处理中
            elif (
                ticket.status == app_models.TicketStatus.resolved
                or ticket.status == app_models.TicketStatus.closed
            ):
                # 用户在已解决/已关闭工单后回复，视为重新打开
                ticket.status = app_models.TicketStatus.reopened
                ticket.resolved_at = None  # 清空解决时间
                ticket.closed_at = None  # 清空关闭时间
            # 如果是 in_progress 或 reopened 状态下用户再次回复，状态通常不变，仍是 in_progress/reopened
            # 或者根据具体业务需求，可以细化为 in_progress (表示用户仍在积极参与)
            elif (
                ticket.status == app_models.TicketStatus.in_progress
                or ticket.status == app_models.TicketStatus.reopened
            ):
                ticket.status = app_models.TicketStatus.in_progress  # 统一为处理中

        db.add(ticket)
        db.commit()
        db.refresh(db_message)
        db.refresh(ticket)  # 确保工单的更新也刷新了
        return db_message

    def get_multi_by_ticket(
        self, db: Session, *, ticket_id: int, skip: int = 0, limit: int = 100
    ) -> List[app_models.TicketMessage]:
        """
        获取特定工单的所有消息 (对用户可见的)。
        如果需要获取包括内部消息在内的所有消息，可以增加一个参数控制。

        :param db: SQLAlchemy Session 对象。
        :param ticket_id: 工单ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 工单消息模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(self.model.ticket_id == ticket_id)
            # .filter(self.model.is_internal == False) # 默认只获取对用户可见的消息
            .order_by(self.model.created_at.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_ticket_with_internal(
        self, db: Session, *, ticket_id: int, skip: int = 0, limit: int = 100
    ) -> List[app_models.TicketMessage]:
        """
        获取特定工单的所有消息，包括内部消息 (通常供管理员/客服使用)。

        :param db: SQLAlchemy Session 对象。
        :param ticket_id: 工单ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 工单消息模型实例列表。
        """
        return (
            db.query(self.model)
            .filter(self.model.ticket_id == ticket_id)
            .order_by(self.model.created_at.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )


# 实例化 CRUD 对象
ticket = CRUDTicket(app_models.Ticket)
ticket_message = CRUDTicketMessage(app_models.TicketMessage)
