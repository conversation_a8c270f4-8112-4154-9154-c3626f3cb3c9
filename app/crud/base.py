# TanZouDaShiAPI/app/crud/base.py
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.db.base_class import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    通用 CRUD 操作基类。

    参数:
    - `model`: SQLAlchemy 模型类。
    - `schema`: Pydantic schema 用于创建。
    - `update_schema`: Pydantic schema 用于更新。
    """

    def __init__(self, model: Type[ModelType]):
        """
        CRUD 对象构造函数，具体模型将在子类中定义。

        :param model: 一个 SQLAlchemy 模型类
        """
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """
        根据 ID 获取单个记录。

        :param db: SQLAlchemy Session 对象。
        :param id: 记录的 ID。
        :return: SQLAlchemy 模型实例或 None。
        """
        return db.query(self.model).filter(self.model.id == id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """
        获取多个记录 (支持分页)。

        :param db: SQLAlchemy Session 对象。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: SQLAlchemy 模型实例列表。
        """
        return db.query(self.model).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """
        创建新记录。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: Pydantic schema 实例，包含创建所需数据。
        :return: 创建的 SQLAlchemy 模型实例。
        """
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """
        更新现有记录。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新的 SQLAlchemy 模型实例。
        :param obj_in: Pydantic schema 实例或包含更新数据的字典。
        :return: 更新后的 SQLAlchemy 模型实例。
        """
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> Optional[ModelType]:
        """
        根据 ID 删除记录。

        :param db: SQLAlchemy Session 对象。
        :param id: 要删除记录的 ID。
        :return: 被删除的 SQLAlchemy 模型实例或 None (如果记录不存在)。
        """
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
