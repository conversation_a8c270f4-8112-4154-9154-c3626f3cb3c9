# TanZouDaShiAPI/app/crud/crud_device.py
from typing import List, Optional
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.device import UserBoundDevice # 由 models 替代
from app.schemas.device import (
    DeviceCreate,
    DeviceUpdate,
)  # 假设有 DeviceCreate, DeviceUpdate schemas


class CRUDDevice(CRUDBase[models.UserBoundDevice, DeviceCreate, DeviceUpdate]):
    """
    用户设备绑定相关的 CRUD 操作类。
    """

    def get_user_bound_device(
        self, db: Session, *, user_id: int, device_id: str
    ) -> Optional[models.UserBoundDevice]:
        """
        根据用户ID和设备ID获取特定绑定设备记录。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param device_id: 设备唯一标识符。
        :return: 设备绑定模型实例或 None。
        """
        return (
            db.query(models.UserBoundDevice)
            .filter(
                models.UserBoundDevice.user_id == user_id,
                models.UserBoundDevice.device_id == device_id,
            )
            .first()
        )

    def get_user_bound_devices(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserBoundDevice]:
        """
        获取指定用户的所有绑定设备。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param skip: 跳过的记录数。
        :param limit: 返回的最大记录数。
        :return: 设备绑定模型实例列表。
        """
        return (
            db.query(models.UserBoundDevice)
            .filter(models.UserBoundDevice.user_id == user_id)
            .order_by(
                models.UserBoundDevice.last_active_at.desc()
            )  # 按最后活跃时间降序
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_user_bound_device(
        self,
        db: Session,
        *,
        obj_in: DeviceCreate,
        user_id: int,
        session_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> models.UserBoundDevice:
        """
        为用户创建新的设备绑定记录。

        :param db: SQLAlchemy Session 对象。
        :param obj_in: 包含设备创建信息的 Pydantic schema (应包含 device_id, device_alias 等)。
        :param user_id: 要绑定设备的用户 ID。
        :param session_token: 新会话的令牌。
        :param ip_address: (可选) 绑定时的 IP 地址。
        :param user_agent: (可选) 绑定时的 User Agent。
        :return: 创建的设备绑定模型实例。
        """
        create_data = obj_in.model_dump()
        create_data["user_id"] = user_id
        create_data["session_token"] = session_token  # session_token 是必须的
        if ip_address:
            create_data["ip_address"] = ip_address
        if user_agent:
            create_data["user_agent"] = user_agent

        # bound_at 和 last_active_at 会自动使用默认值 (当前时间)

        db_obj = self.model(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_bound_device_activity(
        self,
        db: Session,
        *,
        db_obj: models.UserBoundDevice,
        session_token: Optional[str] = None,  # 通常在重新登录或会话刷新时更新
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> models.UserBoundDevice:
        """
        更新绑定设备的活跃信息 (例如心跳、重新登录)。
        last_active_at 会通过 onupdate自动更新。

        :param db: SQLAlchemy Session 对象。
        :param db_obj: 要更新的设备绑定模型实例。
        :param session_token: (可选) 新的会话令牌。
        :param ip_address: (可选) 最新的 IP 地址。
        :param user_agent: (可选) 最新的 User Agent。
        :return: 更新后的设备绑定模型实例。
        """
        update_data = {}
        if session_token is not None:  # 允许传入空字符串来清除 session_token (例如登出)
            update_data["session_token"] = session_token
        if ip_address is not None:
            update_data["ip_address"] = ip_address
        if user_agent is not None:
            update_data["user_agent"] = user_agent

        # 强制更新 last_active_at，即使其他字段没有变化
        # SQLAlchemy 的 onupdate 仅在实际数据更改时触发，
        # 如果只是想刷新时间戳，可以手动设置
        update_data["last_active_at"] = datetime.now(
            db_obj.last_active_at.tzinfo
            if db_obj.last_active_at and db_obj.last_active_at.tzinfo
            else timezone.utc
        )

        if not update_data:  # 如果没有其他字段更新，仅为了刷新时间戳
            db.refresh(
                db_obj
            )  # 重新从数据库加载以获取可能由 onupdate 触发的 last_active_at
            return db_obj

        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def remove_user_bound_device(
        self, db: Session, *, user_id: int, device_id: str
    ) -> Optional[models.UserBoundDevice]:
        """
        移除用户绑定的设备。

        :param db: SQLAlchemy Session 对象。
        :param user_id: 用户 ID。
        :param device_id: 要解绑的设备 ID。
        :return: 被删除的设备绑定模型实例或 None。
        """
        obj = self.get_user_bound_device(db, user_id=user_id, device_id=device_id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    def get_by_session_token(
        self, db: Session, *, session_token: str
    ) -> Optional[models.UserBoundDevice]:
        """
        根据 session_token 获取设备绑定记录。

        :param db: SQLAlchemy Session 对象。
        :param session_token: 会话令牌。
        :return: 设备绑定模型实例或 None。
        """
        return (
            db.query(models.UserBoundDevice)
            .filter(models.UserBoundDevice.session_token == session_token)
            .first()
        )


user_bound_device = CRUDDevice(models.UserBoundDevice)
