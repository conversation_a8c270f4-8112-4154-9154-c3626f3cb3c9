from pydantic import BaseModel

# TanZouDaShiAPI/app/crud/crud_user_interaction.py
from typing import List, Optional, Tuple, Any  # Any 已添加

# from sqlalchemy.orm import Session, joinedload # selectinload 也需要
from sqlalchemy.orm import Session, joinedload, selectinload  # 引入 selectinload

# from sqlalchemy import func, desc, and_ # or_ 也需要
from sqlalchemy import func, desc  # 引入 or_

from app.crud.base import CRUDBase
from app import models  # 统一导入 models

# from app.db.models.user_interaction import UserFavorite, UserScoreRating, ScoreComment # 由 models 替代
# from app.db.models.score import Score # 由 models 替代
# from app.db.models.user import User # 由 models 替代
# from app.db.models.user_interaction import ScoreCommentStatus # 由 models 替代
from app.schemas.user_interaction import (
    UserFavoriteCreate,
    # UserFavoriteUpdate, # UserFavorite 通常没有更新操作，只有创建和删除
    UserScoreRatingCreate,
    UserScoreRatingUpdate,
    ScoreCommentCreate,
    ScoreCommentUpdate,
)
from datetime import datetime


# --- CRUDUserFavorite ---
class CRUDUserFavorite(
    CRUDBase[models.UserFavorite, UserFavoriteCreate, BaseModel]
):  # UpdateSchema 通常不用于Favorite
    def get_by_user_and_score(
        self, db: Session, *, user_id: int, score_id: int
    ) -> Optional[models.UserFavorite]:
        """
        查找特定用户对特定谱曲的收藏记录。
        """
        return (
            db.query(self.model)
            .filter(
                models.UserFavorite.user_id == user_id,
                models.UserFavorite.score_id == score_id,
            )
            .first()
        )

    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserFavorite]:
        """
        获取用户的所有收藏记录，并预加载谱曲信息。
        """
        return (
            db.query(self.model)
            .filter(models.UserFavorite.user_id == user_id)
            .options(joinedload(models.UserFavorite.score))  # 预加载关联的谱曲信息
            .order_by(desc(models.UserFavorite.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_score(
        self, db: Session, *, score_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserFavorite]:
        """
        获取特定谱曲的所有收藏记录，并预加载用户信息。
        """
        return (
            db.query(self.model)
            .filter(models.UserFavorite.score_id == score_id)
            .options(joinedload(models.UserFavorite.user))  # 预加载关联的用户信息
            .order_by(desc(models.UserFavorite.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create(self, db: Session, *, obj_in: UserFavoriteCreate) -> models.UserFavorite:
        """
        创建收藏记录，并更新 Score 表中的 favorite_count。
        """
        db_obj = super().create(db, obj_in=obj_in)
        if db_obj:
            score = (
                db.query(models.Score)
                .filter(models.Score.id == obj_in.score_id)
                .first()
            )
            if score:
                score.favorite_count = (score.favorite_count or 0) + 1
                db.add(score)
                db.commit()
        return db_obj

    def remove_by_user_and_score(
        self, db: Session, *, user_id: int, score_id: int
    ) -> Optional[models.UserFavorite]:
        """
        删除特定用户对特定谱曲的收藏记录，并更新 Score 表中的 favorite_count。
        注意：CRUDBase 中的 remove 是按 id 删除，这里需要复合主键。
        """
        db_obj = self.get_by_user_and_score(db, user_id=user_id, score_id=score_id)
        if db_obj:
            score = db.query(models.Score).filter(models.Score.id == score_id).first()
            if score:
                score.favorite_count = max(0, (score.favorite_count or 0) - 1)
                db.add(score)
            db.delete(db_obj)
            db.commit()
        return db_obj


# --- CRUDUserScoreRating ---
class CRUDUserScoreRating(
    CRUDBase[models.UserScoreRating, UserScoreRatingCreate, UserScoreRatingUpdate]
):
    def get_by_user_and_score(
        self, db: Session, *, user_id: int, score_id: int
    ) -> Optional[models.UserScoreRating]:
        """
        查找特定用户对特定谱曲的评分记录。
        """
        return (
            db.query(self.model)
            .filter(
                models.UserScoreRating.user_id == user_id,
                models.UserScoreRating.score_id == score_id,
            )
            .first()
        )

    def get_average_rating_for_score(
        self, db: Session, *, score_id: int
    ) -> Tuple[Optional[float], int]:
        """
        计算并获取谱曲的平均评分和评分总数。
        返回 (平均分, 评分人数)。
        """
        result = (
            db.query(
                func.avg(models.UserScoreRating.rating).label("average_rating"),
                func.count(models.UserScoreRating.id).label("rating_count"),
            )
            .filter(models.UserScoreRating.score_id == score_id)
            .first()
        )
        # result 是一个 Row 对象，包含 average_rating 和 rating_count
        # e.g. Row(average_rating=Decimal('3.6667'), rating_count=3)
        # 如果没有评分，average_rating 可能是 None
        avg_rating = (
            float(result.average_rating)
            if result and result.average_rating is not None
            else None
        )
        rating_count = result.rating_count if result else 0
        return avg_rating, rating_count

    def _update_score_average_rating(self, db: Session, score_id: int):
        """
        内部辅助方法：在评分创建、更新或删除后，更新 Score 表的 average_rating 和 rating_count。
        """
        avg_rating, rating_count = self.get_average_rating_for_score(
            db, score_id=score_id
        )
        score = db.query(models.Score).filter(models.Score.id == score_id).first()
        if score:
            score.average_rating = avg_rating if avg_rating is not None else 0.00
            score.rating_count = rating_count
            db.add(score)
            db.commit()  # 提交对 Score 的更改

    def create(
        self, db: Session, *, obj_in: UserScoreRatingCreate
    ) -> models.UserScoreRating:
        """
        创建评分记录，并更新 Score 表的平均评分。
        """
        # 检查是否已存在评分
        existing_rating = self.get_by_user_and_score(
            db, user_id=obj_in.user_id, score_id=obj_in.score_id
        )
        if existing_rating:
            # 如果已存在，则调用更新逻辑
            return self.update(
                db,
                db_obj=existing_rating,
                obj_in=UserScoreRatingUpdate(
                    rating=obj_in.rating, comment=obj_in.comment
                ),
            )

        db_obj = super().create(db, obj_in=obj_in)
        if db_obj:
            self._update_score_average_rating(db, score_id=obj_in.score_id)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: models.UserScoreRating,
        obj_in: UserScoreRatingUpdate,
    ) -> models.UserScoreRating:
        """
        更新评分记录，并更新 Score 表的平均评分。
        """
        updated_obj = super().update(db, db_obj=db_obj, obj_in=obj_in)
        if updated_obj:
            self._update_score_average_rating(db, score_id=updated_obj.score_id)
        return updated_obj

    def remove_by_user_and_score(
        self, db: Session, *, user_id: int, score_id: int
    ) -> Optional[models.UserScoreRating]:
        """
        删除特定用户对特定谱曲的评分记录。
        """
        db_obj = self.get_by_user_and_score(db, user_id=user_id, score_id=score_id)
        if db_obj:
            db.delete(db_obj)
            db.commit()
            self._update_score_average_rating(db, score_id=score_id)
        return db_obj

    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserScoreRating]:
        """
        获取用户的所有评分记录。
        """
        return (
            db.query(self.model)
            .filter(models.UserScoreRating.user_id == user_id)
            .options(joinedload(models.UserScoreRating.score))
            .order_by(desc(models.UserScoreRating.updated_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_score(
        self, db: Session, *, score_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.UserScoreRating]:
        """
        获取特定谱曲的所有评分记录。
        """
        return (
            db.query(self.model)
            .filter(models.UserScoreRating.score_id == score_id)
            .options(joinedload(models.UserScoreRating.user))
            .order_by(desc(models.UserScoreRating.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )


# --- CRUDScoreComment ---
class CRUDScoreComment(
    CRUDBase[models.ScoreComment, ScoreCommentCreate, ScoreCommentUpdate]
):
    def get(self, db: Session, id: Any) -> Optional[models.ScoreComment]:
        """
        获取单个评论，并预加载用户信息、谱曲信息（包括上传者）、父评论（包括用户）和回复（包括用户）。
        """
        return (
            db.query(self.model)
            .options(
                selectinload(self.model.user),
                selectinload(self.model.score).selectinload(
                    models.Score.uploader
                ),  # 谱曲及其上传者
                selectinload(self.model.parent_comment).selectinload(
                    models.ScoreComment.user
                ),  # 父评论及其用户
                selectinload(self.model.replies).selectinload(
                    models.ScoreComment.user
                ),  # 回复及其用户
            )
            .filter(self.model.id == id)
            .first()
        )

    def get_multi_by_score(
        self,
        db: Session,
        *,
        score_id: int,
        skip: int = 0,
        limit: int = 100,
        parent_id: Optional[int] = None,  # None 表示获取顶级评论
        include_replies: bool = True,  # 是否也加载回复
    ) -> List[models.ScoreComment]:
        """
        获取特定谱曲的评论。
        - parent_id: 如果提供，则获取该父评论下的回复。如果为 None，获取顶级评论。
        - include_replies: 如果为 True，对每个获取的评论，预加载其直接子回复。
        """
        query = db.query(self.model).filter(models.ScoreComment.score_id == score_id)
        if parent_id is None:
            query = query.filter(
                models.ScoreComment.parent_comment_id.is_(None)
            )  # 顶级评论
        else:
            query = query.filter(
                models.ScoreComment.parent_comment_id == parent_id
            )  # 特定父评论的回复

        if include_replies:
            # 预加载用户和回复。对于回复，也预加载其用户信息。
            # 这种方式可能导致较深的嵌套加载，对于非常多层级的回复可能需要优化。
            # options(joinedload(models.ScoreComment.user), joinedload(models.ScoreComment.replies).joinedload(models.ScoreComment.user))
            # 简化为加载直接回复及其用户
            query = query.options(
                joinedload(models.ScoreComment.user),
                joinedload(models.ScoreComment.replies).joinedload(
                    models.ScoreComment.user
                ),
            )
        else:
            query = query.options(joinedload(models.ScoreComment.user))

        return (
            query.order_by(models.ScoreComment.created_at)  # 通常按创建时间升序排列评论
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_multi_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[models.ScoreComment]:
        """
        获取用户的所有评论。
        """
        return (
            db.query(self.model)
            .filter(models.ScoreComment.user_id == user_id)
            .options(joinedload(models.ScoreComment.score))  # 预加载评论所属的谱曲信息
            .order_by(desc(models.ScoreComment.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create(self, db: Session, *, obj_in: ScoreCommentCreate) -> models.ScoreComment:
        """
        创建评论，并更新 Score 表中的 comment_count。
        """
        db_obj = super().create(db, obj_in=obj_in)
        if db_obj:
            score = (
                db.query(models.Score)
                .filter(models.Score.id == obj_in.score_id)
                .first()
            )
            if score:
                score.comment_count = (score.comment_count or 0) + 1
                db.add(score)
                db.commit()
        return db_obj

    def remove(self, db: Session, *, id: int) -> Optional[models.ScoreComment]:
        """
        删除评论 (及其所有子回复，因为模型中设置了 cascade)，并更新 Score 表中的 comment_count。
        """
        comment_to_delete = self.get(db, id=id)
        if not comment_to_delete:
            return None

        score_id = comment_to_delete.score_id

        # 计算要删除的评论及其所有子回复的数量
        # 注意：级联删除由数据库或 SQLAlchemy 的 cascade 完成，这里只需计算数量
        # 如果评论有回复，也需要将回复的计数减掉。
        # 简单起见，这里只减 1，假设业务上删除父评论时，子评论也会被隐藏或删除，其计数也应处理。
        # 一个更准确的方法是查询该评论及其所有子回复的数量。
        # count_to_decrement = 1 + db.query(func.count(models.ScoreComment.id)).filter(models.ScoreComment.parent_comment_id == id).scalar()
        # 考虑到 cascade="all, delete-orphan" 在 replies 关系上，直接删除父评论会删除所有子评论
        # 因此，需要统计这个评论以及它的所有回复

        # 查找所有子孙评论ID (递归查找或多层查找，这里简化为只考虑直接子评论的计数，
        # 实际应用中如果需要精确计数，可能需要更复杂的逻辑或数据库触发器)
        # 假设：Score.comment_count 只计算顶级评论或所有可见评论。
        # 如果 Score.comment_count 是所有评论的总和（包括回复），那么删除一个评论及其回复时，需要正确递减。
        # 当前模型 ScoreComment.replies 有 cascade="all, delete-orphan"，所以删除父评论会删除所有子评论。
        # 我们需要计算这个评论和它所有子评论的总数。

        _ = 1  # 至少删除当前评论

        # 简单处理：只减少当前删除的这一个评论的计数。
        # 如果 comment_count 期望是扁平化的总评论数，则需要更复杂的逻辑来处理回复的删除。
        # 假设这里的 comment_count 是一个扁平的计数。

        # 重新获取 Score 对象以减少 comment_count
        score = db.query(models.Score).filter(models.Score.id == score_id).first()

        # 执行删除
        deleted_comment = super().remove(db, id=id)  # CRUDBase.remove 会 commit

        if deleted_comment and score:
            # 重新统计该谱曲的评论总数可能更安全，或者基于删除的评论及其子评论数量来调整
            # 简单递减1，如果 comment_count 代表所有评论（包括回复），这里需要调整
            # score.comment_count = max(0, (score.comment_count or 0) - num_deleted_comments) # 确保不为负

            # 更稳妥的方式是重新计算评论总数
            new_comment_count = (
                db.query(func.count(models.ScoreComment.id))
                .filter(models.ScoreComment.score_id == score_id)
                .scalar()
            )
            score.comment_count = new_comment_count
            db.add(score)
            db.commit()  # 再次提交以更新 Score 的 comment_count
            db.refresh(score)

        return deleted_comment

    def get_multi_for_admin(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        score_id: Optional[int] = None,
        user_id: Optional[int] = None,
        content_keyword: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        status: Optional[models.ScoreCommentStatus] = None,
        username_keyword: Optional[str] = None,  # 新增: 按用户名搜索
        score_title_keyword: Optional[str] = None,  # 新增: 按谱曲标题搜索
    ) -> Tuple[List[models.ScoreComment], int]:
        """
        管理员获取评论列表，支持多种筛选条件，并预加载用户和谱曲信息。
        返回: (评论列表, 总记录数)
        """
        query = db.query(self.model).options(
            selectinload(self.model.user),
            selectinload(self.model.score).selectinload(models.Score.uploader),
            selectinload(self.model.parent_comment).selectinload(
                models.ScoreComment.user
            ),
            selectinload(self.model.replies).selectinload(models.ScoreComment.user),
        )

        if score_id is not None:
            query = query.filter(self.model.score_id == score_id)
        if user_id is not None:
            query = query.filter(self.model.user_id == user_id)
        if content_keyword:
            query = query.filter(self.model.content.ilike(f"%{content_keyword}%"))
        if start_time:
            query = query.filter(self.model.created_at >= start_time)
        if end_time:
            query = query.filter(self.model.created_at <= end_time)
        if status:
            query = query.filter(self.model.status == status)

        # 新增筛选条件
        if username_keyword:
            # 需要 join User 表
            query = query.join(models.User, self.model.user_id == models.User.id)
            query = query.filter(models.User.username.ilike(f"%{username_keyword}%"))

        if score_title_keyword:
            # 需要 join Score 表
            query = query.join(models.Score, self.model.score_id == models.Score.id)
            query = query.filter(models.Score.title.ilike(f"%{score_title_keyword}%"))

        total_query = query  # 保存未应用分页和排序的查询以计算总数

        # 注意: .count() 应该在应用 join 之后，但在应用 offset/limit/order_by 之前
        # 如果 join 导致结果集重复 (例如，一个评论匹配多个用户名关键字，虽然这里 User.id 是唯一的，但 join 语法可能影响 count)
        # 通常对于主表 (self.model) 的 count，直接在 filter 后 count() 是准确的。
        # 如果 join 引入了多对多导致主表行重复，则需要 distinct count(self.model.id)
        # 在这个场景下，因为我们是基于 self.model (ScoreComment) 进行查询，并且 join User 和 Score 是为了 filter，
        # count() 应该仍然是 ScoreComment 的数量。

        # 先计算总数
        total = total_query.distinct(
            self.model.id
        ).count()  # 使用 distinct 确保 join 不会影响总评论数

        # 然后应用排序和分页
        comments = (
            query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
        )
        return comments, total


user_favorite = CRUDUserFavorite(models.UserFavorite)
user_score_rating = CRUDUserScoreRating(models.UserScoreRating)
score_comment = CRUDScoreComment(models.ScoreComment)
