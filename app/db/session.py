# app/db/session.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# 获取数据库连接 URL
database_url = settings.database_url

# 根据数据库连接 URL 创建数据库引擎
# connect_args 是 SQLite 特有的配置，用于允许多线程访问（FastAPI 是异步的）
# 如果使用 PostgreSQL 或 MySQL，connect_args 可能不需要或需要其他配置
connect_args = {}
if database_url.startswith("sqlite"):
    connect_args = {"check_same_thread": False}

engine = create_engine(
    database_url,
    pool_pre_ping=True,  # 推荐，用于检查连接是否仍然有效
    connect_args=connect_args,
)

# 创建数据库会话 SessionLocal
# autocommit=False 和 autoflush=False 是 SQLAlchemy 的推荐默认值
# 在 FastAPI 的依赖注入中使用时，我们会显式地提交或回滚会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 数据库初始化函数 (可选)
# from app.db.base_class import Base
# def init_db():
#     # 在这里创建所有表
#     # 通常在项目启动时或使用 Alembic 进行迁移管理时调用
#     Base.metadata.create_all(bind=engine)

# def get_db():
#     """
#     FastAPI 依赖注入函数，用于获取数据库会话。
#     确保会话在使用后被关闭。
#     """
#     db = SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
#
# 上述 get_db 函数通常放在 app.apis.deps.py 中
