# app/db/init_db.py
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text

from app.db.base_class import Base
from app.db.session import engine
from app.core.config import settings

# 导入所有模型，确保它们在创建表时被注册


def create_database_if_not_exists() -> None:
    """
    创建数据库（如果不存在）
    """
    try:
        # 解析数据库 URL
        db_url = settings.database_url
        # 如果是 SQLite，不需要创建数据库
        if db_url.startswith("sqlite:"):
            return

        # 提取数据库名称
        db_name = db_url.split("/")[-1].split("?")[0]

        # 创建一个不带数据库名称的引擎，用于连接MySQL服务器
        db_url_without_db = db_url[: db_url.rindex("/")] + "/"
        temp_engine = create_engine(db_url_without_db)

        # 尝试创建数据库
        with temp_engine.connect() as conn:
            # 检查数据库是否存在
            result = conn.execute(text(f"SHOW DATABASES LIKE '{db_name}'"))
            exists = result.scalar() is not None

            if not exists:
                print(f"创建数据库: {db_name}")
                conn.execute(
                    text(
                        f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
                    )
                )
                conn.commit()
                print(f"数据库 {db_name} 已创建")
            else:
                print(f"数据库 {db_name} 已存在")
    except Exception as e:
        print(f"尝试创建数据库时出错: {e}")
        # 如果无法创建数据库，继续执行（可能使用已存在的数据库）


# 创建所有表
def init_db() -> None:
    """
    初始化数据库，创建所有表
    """
    # 首先尝试创建数据库
    create_database_if_not_exists()

    # 然后在数据库中创建所有表
    try:
        Base.metadata.create_all(bind=engine)
        print("所有数据库表已创建")
    except Exception as e:
        print(f"创建数据库表时出错: {e}")
        raise


# 创建默认管理员账户
def create_default_admin(db: Session) -> None:
    """
    创建默认管理员账户（如果不存在）
    """
    from app.crud.crud_user import user as crud_user
    from app.schemas.user import AdminUserCreate
    from app.core.config import settings
    from app import models

    # 检查是否已存在管理员账户
    existing_admin = crud_user.get_user_by_username(db, username=settings.DEFAULT_ADMIN_USERNAME)
    if existing_admin:
        print(f"管理员账户 '{settings.DEFAULT_ADMIN_USERNAME}' 已存在，跳过创建")
        return

    # 检查邮箱是否已存在
    if settings.DEFAULT_ADMIN_EMAIL:
        existing_email = crud_user.get_user_by_email(db, email=settings.DEFAULT_ADMIN_EMAIL)
        if existing_email:
            print(f"邮箱 '{settings.DEFAULT_ADMIN_EMAIL}' 已被使用，跳过创建默认管理员")
            return

    # 创建默认管理员账户
    try:
        admin_user = AdminUserCreate(
            username=settings.DEFAULT_ADMIN_USERNAME,
            email=settings.DEFAULT_ADMIN_EMAIL,
            password=settings.DEFAULT_ADMIN_PASSWORD,
            role=models.UserRole.admin,
            status=models.UserStatus.active,
            nickname="系统管理员"
        )

        db_admin = crud_user.create_user_by_admin(db=db, obj_in=admin_user)
        print(f"默认管理员账户已创建:")
        print(f"  用户名: {db_admin.username}")
        print(f"  邮箱: {db_admin.email}")
        print(f"  角色: {db_admin.role}")
        print(f"  状态: {db_admin.status}")
        print(f"  请及时修改默认密码！")

    except Exception as e:
        print(f"创建默认管理员账户失败: {e}")


# 可以添加一个创建初始数据的函数
def create_initial_data(db: Session) -> None:
    """
    创建初始数据，如默认管理员用户、默认分类等
    """
    from app.core.config import settings

    # 创建默认管理员账户
    if settings.CREATE_DEFAULT_ADMIN:
        create_default_admin(db)
