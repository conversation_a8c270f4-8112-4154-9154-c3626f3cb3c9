# TanZouDaShiAPI/app/db/models/user.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Enum as SQLAlchemyEnum,
    ForeignKey,
    Boolean,
    Text,
    DECIMAL,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import (
    TIMESTAMP,
)  # 确保 TIMESTAMP 类型与 MySQL 兼容并支持 onupdate
from typing import TYPE_CHECKING, List

if TYPE_CHECKING:
    from .score import Score  # noqa
    from .user_interaction import UserFavorite, UserScoreRating, ScoreComment  # noqa
    from .ticket import Ticket, TicketMessage  # noqa
    from .license import LicenseKey  # noqa
    from .device import UserBoundDevice  # noqa
    from .log import UserSessionLog  # noqa
    from .reseller_finance import CommissionRecord, WithdrawalRecord  # noqa

from app.db.base_class import Base


class UserRole(str, enum.Enum):
    admin = "admin"
    reseller = "reseller"
    user = "user"


class UserStatus(str, enum.Enum):
    active = "active"
    inactive = "inactive"
    banned = "banned"
    pending_verification = "pending_verification"


class UserCreationSource(str, enum.Enum):
    registration = "registration"
    license_activation = "license_activation"
    admin_created = "admin_created"


class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="用户唯一标识符",
    )
    username: Mapped[str] = mapped_column(
        String(50), unique=True, nullable=False, index=True, comment="用户名 (用于登录)"
    )
    password_hash: Mapped[str] = mapped_column(
        String(255), nullable=False, comment="加密后的密码"
    )
    email: Mapped[str | None] = mapped_column(
        String(100), unique=True, nullable=True, index=True, comment="电子邮箱"
    )
    phone_number: Mapped[str | None] = mapped_column(
        String(20), unique=True, nullable=True, index=True, comment="手机号码"
    )
    avatar_url: Mapped[str | None] = mapped_column(
        String(255), nullable=True, comment="用户头像URL"
    )
    nickname: Mapped[str | None] = mapped_column(
        String(50), nullable=True, comment="用户昵称"
    )
    role: Mapped[UserRole] = mapped_column(
        SQLAlchemyEnum(UserRole, name="user_role_enum"),
        nullable=False,
        default=UserRole.user,
        index=True,
        comment="用户角色: admin-管理员, reseller-代理, user-普通用户",
    )
    status: Mapped[UserStatus] = mapped_column(
        SQLAlchemyEnum(UserStatus, name="user_status_enum"),
        nullable=False,
        default=UserStatus.active,
        index=True,
        comment="账户状态: active-活跃, inactive-未激活, banned-封禁, pending_verification-待验证",
    )
    max_concurrent_devices: Mapped[int] = mapped_column(
        Integer, nullable=False, default=1, comment="最大同时在线设备数"
    )
    creation_source: Mapped[UserCreationSource] = mapped_column(
        SQLAlchemyEnum(UserCreationSource, name="user_creation_source_enum"),
        nullable=False,
        default=UserCreationSource.registration,
        comment="用户创建来源: registration-常规注册, license_activation-激活码激活创建, admin_created-管理员创建",
    )
    registration_ip: Mapped[str | None] = mapped_column(
        String(45), nullable=True, comment="注册时IP地址"
    )
    last_login_ip: Mapped[str | None] = mapped_column(
        String(45), nullable=True, comment="最后登录IP地址"
    )
    last_login_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="最后登录时间"
    )
    email_verified_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="邮箱验证时间"
    )
    phone_verified_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="手机验证时间"
    )
    permanent_license_unbind_count_current_month: Mapped[int | None] = mapped_column(
        Integer, default=0, nullable=True, comment="永久卡用户当月已解绑设备次数"
    )
    permanent_license_last_unbind_month: Mapped[str | None] = mapped_column(
        String(7), nullable=True, comment="永久卡用户上次解绑的月份 (YYYY-MM)"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="账户创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="账户信息更新时间",
    )
    deleted_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="软删除标记时间"
    )

    # --- Relationships ---
    # 一个用户可能是一个代理
    reseller_info: Mapped["Reseller | None"] = relationship(
        "Reseller",
        foreign_keys="[Reseller.user_id]",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
    )
    # 一个用户（管理员/代理）可能发放了多个卡密
    issued_license_keys: Mapped[list["LicenseKey"]] = relationship(
        "LicenseKey",
        foreign_keys="[LicenseKey.issued_by_user_id]",
        back_populates="issuer",
    )
    # 一个用户（代理）可能持有多个寄售卡密
    consigned_license_keys: Mapped[list["LicenseKey"]] = relationship(
        "LicenseKey",
        foreign_keys="[LicenseKey.consignment_reseller_id]",
        back_populates="consignment_reseller",
    )
    # 一个用户可能激活了多个卡密 (注意：在 LicenseKey 中定义 user_id 指向 User)
    activated_license_keys: Mapped[list["LicenseKey"]] = relationship(
        "LicenseKey", foreign_keys="[LicenseKey.user_id]", back_populates="user"
    )
    # 用户绑定的设备
    bound_devices: Mapped[list["UserBoundDevice"]] = relationship(
        "UserBoundDevice", back_populates="user", cascade="all, delete-orphan"
    )
    # 用户会话日志
    session_logs: Mapped[list["UserSessionLog"]] = relationship(
        "UserSessionLog", back_populates="user", cascade="all, delete-orphan"
    )
    # 用户作为上级代理时，其下级代理列表
    sub_resellers: Mapped[list["Reseller"]] = relationship(
        "Reseller",
        foreign_keys="[Reseller.parent_reseller_user_id]",
        back_populates="parent_reseller",
    )

    # --- Relationships with Reseller Finance models ---
    earned_commissions: Mapped[list["CommissionRecord"]] = relationship(
        "CommissionRecord",
        foreign_keys="[CommissionRecord.beneficiary_reseller_user_id]",
        back_populates="beneficiary_reseller",
        cascade="all, delete-orphan",
    )
    sold_for_commissions: Mapped[list["CommissionRecord"]] = relationship(
        "CommissionRecord",
        foreign_keys="[CommissionRecord.source_reseller_user_id]",
        back_populates="source_reseller",
        cascade="all, delete-orphan",  # 如果源头代理删除，其作为销售源的佣金记录也删除
    )
    activated_key_commissions: Mapped[list["CommissionRecord"]] = relationship(
        "CommissionRecord",
        foreign_keys="[CommissionRecord.related_user_id]",
        back_populates="related_user",  # 如果最终用户删除，其关联的佣金记录中的 related_user_id 会变更为 NULL (如果数据库支持SET NULL)或保持原样
    )
    withdrawal_requests: Mapped[list["WithdrawalRecord"]] = relationship(
        "WithdrawalRecord", back_populates="reseller", cascade="all, delete-orphan"
    )

    # --- Relationships with Score and Interaction models ---
    # 用户上传的谱曲
    uploaded_scores: Mapped[list["Score"]] = relationship(
        "Score",
        foreign_keys="[Score.uploader_user_id]",
        back_populates="uploader",
        cascade="all, delete-orphan",  # 如果用户被删除，其上传的谱曲也删除 (根据业务逻辑调整)
    )
    # 用户审核的谱曲 (如果用户是管理员)
    approved_scores: Mapped[list["Score"]] = relationship(
        "Score", foreign_keys="[Score.approved_by_user_id]", back_populates="approver"
    )
    # 用户收藏的谱曲 (通过关联表 UserFavorite)
    favorites_assoc: Mapped[list["UserFavorite"]] = relationship(
        "UserFavorite", back_populates="user", cascade="all, delete-orphan"
    )
    # 用户对谱曲的评分 (通过关联表 UserScoreRating)
    ratings_assoc: Mapped[list["UserScoreRating"]] = relationship(
        "UserScoreRating", back_populates="user", cascade="all, delete-orphan"
    )
    # 用户发表的评论
    comments: Mapped[list["ScoreComment"]] = relationship(
        "ScoreComment", back_populates="user", cascade="all, delete-orphan"
    )

    # --- Relationships with Ticket and TicketMessage models ---
    # 用户创建的工单
    created_tickets: Mapped[List["Ticket"]] = relationship(
        "Ticket",
        foreign_keys="[Ticket.user_id]",
        back_populates="user",
        cascade="all, delete-orphan",
    )
    # 用户被指派处理的工单
    assigned_tickets: Mapped[List["Ticket"]] = relationship(
        "Ticket", foreign_keys="[Ticket.assignee_user_id]", back_populates="assignee"
    )
    # 用户发送的工单消息
    ticket_messages_sent: Mapped[List["TicketMessage"]] = relationship(
        "TicketMessage",
        foreign_keys="[TicketMessage.sender_id]",
        back_populates="sender",
        cascade="all, delete-orphan",
    )

    # 辅助属性，用于方便地获取用户收藏的谱曲列表 (如果需要)
    # @property
    # def favorite_scores(self) -> list["Score"]:
    #     return [assoc.score for assoc in self.favorites_assoc]


class ResellerLevel(Base):
    __tablename__ = "reseller_levels"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="级别唯一标识符"
    )
    level_value: Mapped[int] = mapped_column(
        Integer,
        unique=True,
        nullable=False,
        index=True,
        comment="级别数值 (例如 1, 2, 3)",
    )
    name: Mapped[str] = mapped_column(
        String(100), unique=True, nullable=False, comment="级别名称 (供后台管理和显示)"
    )
    description: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="级别描述"
    )
    upgrade_threshold_sales: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(15, 2), nullable=True, comment="升级到此级别的累计销售额门槛"
    )
    upgrade_threshold_sub_resellers: Mapped[int | None] = mapped_column(
        Integer, nullable=True, comment="升级到此级别的直接下级代理数量门槛"
    )
    default_commission_rate: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(5, 4), nullable=True, comment="此级别的默认直接销售佣金比例"
    )
    default_sub_commission_rate: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(5, 4), nullable=True, comment="此级别的默认间接（下级）佣金比例"
    )
    default_second_tier_sub_commission_rate: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(5, 4), nullable=True, default=None, comment="默认二级间接佣金率"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, index=True, comment="是否启用此级别"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="更新时间",
    )

    # --- Relationships ---
    resellers_at_this_level: Mapped[list["Reseller"]] = relationship(
        "Reseller", back_populates="level"
    )


class ResellerStatus(str, enum.Enum):
    active = "active"
    inactive = "inactive"
    frozen = "frozen"


class Reseller(Base):
    __tablename__ = "resellers"

    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
        comment="关联的用户ID (主键, 外键)",
    )
    parent_reseller_user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="上级代理的用户ID",
    )
    commission_rate: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(5, 4),
        nullable=True,
        comment="特定用户的直接销售佣金比例 (覆盖级别默认值)",
    )
    balance: Mapped[DECIMAL] = mapped_column(
        DECIMAL(12, 2), nullable=False, default=0.00, comment="代理账户余额/可提现额度"
    )
    # 确保 reseller_level_value 引用 reseller_levels.level_value
    reseller_level_value: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("reseller_levels.level_value", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="代理级别数值",
    )
    consignment_limit_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="允许持有的最大寄售卡密数量 (0表示不允许)",
    )
    consignment_limit_value: Mapped[DECIMAL] = mapped_column(
        DECIMAL(12, 2),
        nullable=False,
        default=0.00,
        comment="允许持有的最大寄售卡密总价值 (0表示不允许)",
    )
    status: Mapped[ResellerStatus] = mapped_column(
        SQLAlchemyEnum(ResellerStatus, name="reseller_status_enum"),
        nullable=False,
        default=ResellerStatus.active,
        index=True,
        comment="代理资格状态: active-活跃, inactive-未激活, frozen-冻结",
    )
    total_sales_amount: Mapped[DECIMAL] = mapped_column(
        DECIMAL(15, 2),
        nullable=False,
        default=0.00,
        comment="累计销售总额（已结算部分）",
    )
    total_commission_earned: Mapped[DECIMAL] = mapped_column(
        DECIMAL(15, 2), nullable=False, default=0.00, comment="累计获得佣金总额"
    )
    remarks: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="代理备注信息"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="成为代理的时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="代理信息更新时间",
    )

    # --- Relationships ---
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys="[Reseller.user_id]",
        back_populates="reseller_info",
        uselist=False,
    )
    level: Mapped["ResellerLevel | None"] = relationship(
        "ResellerLevel",
        back_populates="resellers_at_this_level",
        foreign_keys=[reseller_level_value],
    )
    parent_reseller: Mapped["User | None"] = relationship(
        "User",
        foreign_keys=[parent_reseller_user_id],
        back_populates="sub_resellers",
        uselist=False,
    )
    # 子代理关系通过 User.sub_resellers 定义
