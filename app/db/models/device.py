# TanZouDaShiAPI/app/db/models/device.py
from datetime import datetime

from sqlalchemy import Integer, String, ForeignKey, Text, BigInteger
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIM<PERSON><PERSON>MP

from app.db.base_class import Base
# from .user import User # 避免循环导入


class UserBoundDevice(Base):
    __tablename__ = "user_bound_devices"

    id: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True, comment="绑定记录唯一标识符"
    )
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="用户ID",
    )
    device_id: Mapped[str] = mapped_column(
        String(128), nullable=False, comment="设备唯一标识符 (用户维度内应唯一)"
    )  # UNIQUE (`user_id`, `device_id`) 通过 __table_args__ 实现
    device_alias: Mapped[str | None] = mapped_column(
        String(100), nullable=True, comment="用户为设备设置的别名"
    )
    session_token: Mapped[str | None] = mapped_column(
        String(255),
        unique=True,
        nullable=True,
        index=True,
        comment="当前活动会话的令牌",
    )
    ip_address: Mapped[str | None] = mapped_column(
        String(45), nullable=True, comment="最近一次使用该设备登录或活跃的IP地址"
    )
    user_agent: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="最近一次使用该设备登录或活跃的客户端User Agent"
    )

    bound_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="设备绑定时间",
    )
    last_active_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        index=True,
        server_default=func.now(),  # 绑定时默认为当前时间
        server_onupdate=func.now(),  # 后续通过心跳更新
        comment="设备最后活跃时间",
    )
    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="记录创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="记录更新时间",
    )

    # --- Relationships ---
    user: Mapped["User"] = relationship("User", back_populates="bound_devices")

    # --- Table Args ---
    from sqlalchemy import UniqueConstraint

    __table_args__ = (
        UniqueConstraint("user_id", "device_id", name="uq_user_device_id"),
    )
