# TanZouDaShiAPI/app/db/models/user_interaction.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    ForeignKey,
    Text,
    Enum as SQLAlchemyEnum,
    UniqueConstraint,
    SmallInteger,  # 用于 rating 1-5
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP

from app.db.base_class import Base
# from .user import User # 通过字符串引用或在关系中指定
# from .score import Score


class ScoreCommentStatus(str, enum.Enum):
    visible = "visible"
    hidden = "hidden"
    pending_review = "pending_review"


class UserFavorite(Base):
    __tablename__ = "user_favorites"

    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
        comment="用户ID",
    )
    score_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("scores.id", ondelete="CASCADE"),
        primary_key=True,
        comment="谱曲ID",
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="收藏时间",
    )

    # --- Relationships ---
    user: Mapped["User"] = relationship(
        "User", back_populates="favorites_assoc"
    )  # 在 User 中需添加 favorites_assoc
    score: Mapped["Score"] = relationship(
        "Score", back_populates="user_favorites_assoc"
    )  # 在 Score 中已添加 user_favorites_assoc

    __table_args__ = (
        UniqueConstraint("user_id", "score_id", name="uq_user_score_favorite"),
    )


class UserScoreRating(Base):
    __tablename__ = "user_score_ratings"

    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
        comment="用户ID",
    )
    score_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("scores.id", ondelete="CASCADE"),
        primary_key=True,
        comment="谱曲ID",
    )
    rating: Mapped[int] = mapped_column(
        SmallInteger, nullable=False, comment="评分 (1-5)"
    )  # 使用 SmallInteger 替代 TINYINT
    comment: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="评论内容 (可选)"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="评分时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="更新时间",
    )

    # --- Relationships ---
    user: Mapped["User"] = relationship(
        "User", back_populates="ratings_assoc"
    )  # 在 User 中需添加 ratings_assoc
    score: Mapped["Score"] = relationship(
        "Score", back_populates="ratings_assoc"
    )  # 在 Score 中已添加 ratings_assoc

    __table_args__ = (
        UniqueConstraint("user_id", "score_id", name="uq_user_score_rating"),
    )


class ScoreComment(Base):
    __tablename__ = "score_comments"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, index=True, autoincrement=True, comment="评论ID"
    )
    score_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("scores.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="谱曲ID",
    )
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="评论用户ID",
    )  # 假设评论用户被删除，其评论也级联删除
    parent_comment_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("score_comments.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        comment="父评论ID (用于回复嵌套评论)",
    )
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="评论内容")
    status: Mapped[ScoreCommentStatus] = mapped_column(
        SQLAlchemyEnum(ScoreCommentStatus, name="score_comment_status_enum"),
        nullable=False,
        default=ScoreCommentStatus.visible,
        index=True,
        comment="评论状态",
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="评论创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="评论更新时间",
    )

    # --- Relationships ---
    score: Mapped["Score"] = relationship("Score", back_populates="comments")
    user: Mapped["User"] = relationship(
        "User", back_populates="comments"
    )  # 在 User 中需添加 comments 关系

    # 自引用关系: 回复 (子评论)
    replies: Mapped[list["ScoreComment"]] = relationship(
        "ScoreComment",
        back_populates="parent_comment",
        cascade="all, delete-orphan",
        # foreign_keys=[parent_comment_id] # SQLAlchemy 通常能自动推断，但显式指定更清晰
    )
    # 自引用关系: 父评论
    parent_comment: Mapped["ScoreComment | None"] = relationship(
        "ScoreComment",
        back_populates="replies",
        remote_side=[id],  # 指向父评论的 id
        # foreign_keys=[parent_comment_id]
    )
