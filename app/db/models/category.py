# TanZouDaShiAPI/app/db/models/category.py
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Text,
    Boolean,
    ForeignKey,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP

from app.db.base_class import Base


class Category(Base):
    __tablename__ = "categories"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="分类唯一标识符",
    )
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="分类名称 (同级目录下唯一，但SQLAlchemy层面不直接强制，应由应用逻辑保证)",
    )
    slug: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="URL友好型名称 (slug), 全局唯一",
    )
    parent_category_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("categories.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="父分类ID (NULL表示顶级分类)",
    )
    description: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="分类描述"
    )
    icon_url: Mapped[str | None] = mapped_column(
        String(255), nullable=True, comment="分类图标URL"
    )
    sort_order: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, comment="排序字段 (数字越小越靠前)"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, index=True, comment="是否启用"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="分类创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="分类信息更新时间",
    )

    # --- Relationships ---
    # 子分类
    children: Mapped[list["Category"]] = relationship(
        "Category",
        back_populates="parent_category",
        cascade="all, delete-orphan",
        foreign_keys=[parent_category_id],  # 指定外键以避免SQLAlchemy混淆
    )
    # 父分类
    parent_category: Mapped["Category | None"] = relationship(
        "Category",
        back_populates="children",
        remote_side=[id],  # 对于自引用的一对多关系，remote_side 指向 '一' 的那方的主键
        foreign_keys=[parent_category_id],
    )

    # 该分类下的谱曲
    scores: Mapped[list["Score"]] = relationship("Score", back_populates="category")

    # 为了在联合唯一约束 (name, parent_category_id) 上提供更好的提示，可以在 __table_args__ 中定义
    # 但这通常在数据库迁移层面处理，SQLAlchemy 模型层面主要用于 ORM
    # __table_args__ = (
    #     UniqueConstraint('name', 'parent_category_id', name='uq_category_name_parent'),
    # )
