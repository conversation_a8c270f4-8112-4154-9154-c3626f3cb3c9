# TanZouDaShiAPI/app/db/models/license.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Enum as SQLAlchemyEnum,
    ForeignKey,
    Boolean,
    Text,
    DECIMAL,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP
from typing import TYPE_CHECKING

from app.db.base_class import Base

# 需要从 user.py 导入 User 以建立关系，但为了避免循环导入，这里使用字符串形式 "User"
# from .user import User # 实际应用中可能需要更复杂的导入管理或在关系中使用字符串
if TYPE_CHECKING:
    from .user import User  # noqa
    from .reseller_finance import CommissionRecord  # noqa


class LicenseKeyStatus(str, enum.Enum):
    available = "available"
    used = "used"
    expired = "expired"
    disabled = "disabled"
    consigned_available = "consigned_available"


class LicenseSettlementStatus(str, enum.Enum):
    pending = "pending"
    settled = "settled"
    not_applicable = "not_applicable"


class LicenseDurationUnit(str, enum.Enum):
    days = "days"
    hours = "hours"
    minutes = "minutes"
    permanent = "permanent"


class LicenseType(Base):
    __tablename__ = "license_types"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="类型唯一标识符"
    )
    name: Mapped[str] = mapped_column(
        String(100), unique=True, nullable=False, comment="类型名称 (供后台管理识别)"
    )
    code: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="类型代码 (程序内部和API使用)",
    )
    description: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="类型描述"
    )
    duration_value: Mapped[int] = mapped_column(
        Integer, nullable=False, comment="时长数值"
    )
    duration_unit: Mapped[LicenseDurationUnit] = mapped_column(
        SQLAlchemyEnum(LicenseDurationUnit, name="license_duration_unit_enum"),
        nullable=False,
        comment="时长单位: permanent 表示永久有效",
    )
    max_concurrent_devices: Mapped[int] = mapped_column(
        Integer, nullable=False, default=1, comment="该类型允许的最大同时在线设备数"
    )
    price: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(10, 2), nullable=True, comment="建议售价 (供参考)"
    )
    reseller_cost: Mapped[DECIMAL | None] = mapped_column(
        DECIMAL(10, 2), nullable=True, comment="代理拿货成本 (供参考)"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        comment="是否启用该类型 (禁用的类型无法生成新的卡密)",
    )
    is_publicly_available: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否允许代理/用户获取 (控制特殊类型只能由管理员发放)",
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="更新时间",
    )

    # --- Relationships ---
    license_keys: Mapped[list["LicenseKey"]] = relationship(
        "LicenseKey", back_populates="license_type"
    )


class LicenseKey(Base):
    __tablename__ = "license_keys"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="卡密唯一标识符"
    )
    key_string: Mapped[str] = mapped_column(
        String(64), unique=True, nullable=False, index=True, comment="卡密字符串"
    )
    license_type_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("license_types.id"),
        nullable=False,
        index=True,
        comment="卡密类型ID",
    )
    status: Mapped[LicenseKeyStatus] = mapped_column(
        SQLAlchemyEnum(LicenseKeyStatus, name="license_key_status_enum"),
        nullable=False,
        default=LicenseKeyStatus.available,
        index=True,
        comment="卡密状态: disabled-管理员禁用; consigned_available-已分配给代理待售",
    )
    user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="使用该卡密的用户ID (激活后必须关联)",
    )
    activated_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="激活时间"
    )
    expires_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=True,
        index=True,
        comment="过期时间 (根据激活时间和类型时长计算)",
    )
    issued_by_user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="发放该卡密的管理员/代理ID",
    )
    consignment_reseller_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="持有此寄售卡密的代理ID",
    )
    settlement_status: Mapped[LicenseSettlementStatus] = mapped_column(
        SQLAlchemyEnum(LicenseSettlementStatus, name="license_settlement_status_enum"),
        nullable=False,
        default=LicenseSettlementStatus.not_applicable,
        comment="寄售卡密成本结算状态: pending-激活待结算, settled-已结算, not_applicable-非寄售卡",
    )
    batch_id: Mapped[str | None] = mapped_column(
        String(36),
        nullable=True,
        index=True,
        comment="批次号 (用于追踪同一批生成的卡密, 可使用UUID)",
    )
    renewal_source_key_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("license_keys.id", ondelete="SET NULL"),
        nullable=True,
        comment="续期来源卡密ID (指向被续期的旧卡密ID)",
    )
    notes: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="备注信息 (例如，特殊活动卡密)"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="卡密创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="卡密信息更新时间",
    )

    # --- Relationships ---
    license_type: Mapped["LicenseType"] = relationship(
        "LicenseType", back_populates="license_keys"
    )
    user: Mapped["User | None"] = relationship(
        "User", foreign_keys=[user_id], back_populates="activated_license_keys"
    )  # 用户激活的卡密
    issuer: Mapped["User | None"] = relationship(
        "User", foreign_keys=[issued_by_user_id], back_populates="issued_license_keys"
    )  # 卡密发放者
    consignment_reseller: Mapped["User | None"] = relationship(
        "User",
        foreign_keys=[consignment_reseller_id],
        back_populates="consigned_license_keys",
    )  # 寄售代理

    # 自我引用，用于续期
    renewal_source_key: Mapped["LicenseKey | None"] = relationship(
        "LicenseKey",
        remote_side=[id],
        foreign_keys=[renewal_source_key_id],
        backref="renewed_by_keys",
        uselist=False,
    )
    # renewed_by_keys: Mapped[list["LicenseKey"]] will be created by backref

    # 此卡密产生的佣金记录
    generated_commissions: Mapped[list["CommissionRecord"]] = relationship(
        "CommissionRecord",
        back_populates="source_license",
        cascade="all, delete-orphan",  # 如果卡密删除，相关的佣金记录也删除
    )
