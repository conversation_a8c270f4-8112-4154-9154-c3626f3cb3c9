# TanZouDaShiAPI/app/db/models/ticket.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Enum as SQLAlchemyEnum,
    ForeignKey,
    Text,
    Boolean,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP

from app.db.base_class import Base
from app.db.models.user import User  # 引入User模型用于外键关联

# pylint: disable=unused-import # noqa
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    pass  # noqa F401 pylint: disable=unnecessary-pass


class TicketStatus(str, enum.Enum):
    """工单状态枚举"""

    open = "open"  # 开启
    in_progress = "in_progress"  # 处理中
    awaiting_reply = "awaiting_reply"  # 等待回复
    resolved = "resolved"  # 已解决
    closed = "closed"  # 已关闭
    reopened = "reopened"  # 重新开启


class TicketPriority(str, enum.Enum):
    """工单优先级枚举"""

    low = "low"  # 低
    medium = "medium"  # 中
    high = "high"  # 高
    urgent = "urgent"  # 紧急


class Ticket(Base):
    """工单表模型"""

    __tablename__ = "support_tickets"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="工单唯一标识符",
    )
    ticket_number: Mapped[str] = mapped_column(
        String(20),
        unique=True,
        nullable=False,
        index=True,
        comment="工单号 (例如 TK-YYYYMMDD-XXXX)",
    )
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="提交用户ID",
    )
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="工单标题")
    description: Mapped[str] = mapped_column(
        Text, nullable=False, comment="工单详细内容"
    )
    status: Mapped[TicketStatus] = mapped_column(
        SQLAlchemyEnum(TicketStatus, name="ticket_status_enum"),
        nullable=False,
        default=TicketStatus.open,
        index=True,
        comment="工单状态",
    )
    priority: Mapped[TicketPriority] = mapped_column(
        SQLAlchemyEnum(TicketPriority, name="ticket_priority_enum"),
        nullable=False,
        default=TicketPriority.medium,
        index=True,
        comment="工单优先级",
    )
    category: Mapped[str | None] = mapped_column(
        String(50), nullable=True, comment="工单分类 (例如 技术问题, 账号问题)"
    )
    assignee_user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="分配给处理的客服/管理员ID",
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="工单创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="工单信息更新时间",
    )
    resolved_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="解决时间"
    )
    closed_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="关闭时间"
    )
    last_reply_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="最后回复时间"
    )

    # --- Relationships ---
    # 工单创建者
    user: Mapped["User"] = relationship(
        "User", foreign_keys=[user_id], back_populates="created_tickets"
    )
    # 工单处理者/负责人
    assignee: Mapped["User | None"] = relationship(
        "User", foreign_keys=[assignee_user_id], back_populates="assigned_tickets"
    )
    # 工单的消息列表 (一个工单可以有多条消息)
    messages: Mapped[list["TicketMessage"]] = relationship(
        "TicketMessage", back_populates="ticket", cascade="all, delete-orphan"
    )


class TicketMessageType(
    str, enum.Enum
):  # 假设我们需要区分回复类型，尽管数据库设计中没有直接的 is_internal
    """工单消息类型枚举 (扩展，原设计中通过 is_internal 布尔值)"""

    customer_reply = "customer_reply"  # 客户回复
    agent_reply = "agent_reply"  # 客服回复
    internal_note = "internal_note"  # 内部备注


class TicketMessage(Base):  # 对应数据库设计中的 ticket_replies
    """工单消息/回复表模型"""

    __tablename__ = "ticket_replies"  # 保持与数据库设计文档一致

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="回复唯一标识符",
    )
    ticket_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("support_tickets.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="关联工单ID",
    )
    sender_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="回复用户ID (客户、管理员或客服)",
    )  # user_id in doc
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="回复内容")
    is_internal: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否内部回复 (对提交工单的用户不可见)",
    )
    # 如果需要更细致的类型，可以使用上面的 TicketMessageType，并可能移除 is_internal
    # message_type: Mapped[TicketMessageType] = mapped_column(SQLAlchemyEnum(TicketMessageType), nullable=False, default=TicketMessageType.customer_reply)

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="回复创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="回复更新时间",
    )

    # --- Relationships ---
    # 消息所属的工单
    ticket: Mapped["Ticket"] = relationship("Ticket", back_populates="messages")
    # 消息的发送者
    sender: Mapped["User"] = relationship("User", back_populates="ticket_messages_sent")


# 更新User模型以包含与Ticket和TicketMessage的反向关系
# 这部分通常在User模型文件中完成，但为了逻辑完整性在此提及
# 在 User 模型中添加:
# created_tickets: Mapped[list["Ticket"]] = relationship("Ticket", foreign_keys="[Ticket.user_id]", back_populates="user", cascade="all, delete-orphan")
# assigned_tickets: Mapped[list["Ticket"]] = relationship("Ticket", foreign_keys="[Ticket.assignee_user_id]", back_populates="assignee")
# ticket_messages_sent: Mapped[list["TicketMessage"]] = relationship("TicketMessage", foreign_keys="[TicketMessage.sender_id]", back_populates="sender", cascade="all, delete-orphan")
