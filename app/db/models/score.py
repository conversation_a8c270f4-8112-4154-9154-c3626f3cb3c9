# TanZouDaShiAPI/app/db/models/score.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Text,  # 确保 Text 已导入
    <PERSON><PERSON>,
    ForeignKey,
    Enum as SQLAlchemyEnum,
    JSON,
    DECIMAL,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP, LONGTEXT

from app.db.base_class import Base
# 确保 User 和 Category 模型可以被引用
# from .user import User # 如果直接引用，需要处理循环导入问题，通常通过字符串引用或在关系中指定
# from .category import Category


class ScoreType(str, enum.Enum):
    TXT = "TXT"
    MIDI_REF = "MIDI_REF"
    EXTERNAL_LINK = "EXTERNAL_LINK"


class MidiStorageType(str, enum.Enum):
    local = "local"
    s3 = "s3"
    oss = "oss"


class ScoreDifficulty(str, enum.Enum):
    beginner = "beginner"
    easy = "easy"
    medium = "medium"
    hard = "hard"
    expert = "expert"


class ScoreStatus(str, enum.Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"
    private = "private"
    draft = "draft"


class Score(Base):
    __tablename__ = "scores"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="谱曲唯一标识符",
    )
    title: Mapped[str] = mapped_column(
        String(255), nullable=False, index=True, comment="谱曲标题"
    )
    description: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="谱曲描述 (例如作者、风格简介)"
    )
    uploader_user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
        comment="上传者用户ID",
    )
    category_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("categories.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
        comment="所属分类ID",
    )

    type: Mapped[ScoreType] = mapped_column(
        SQLAlchemyEnum(ScoreType, name="score_type_enum"),
        nullable=False,
        index=True,
        comment="谱曲类型: TXT, MIDI_REF-关联服务器MIDI文件, EXTERNAL_LINK-外部链接谱",
    )
    txt_content: Mapped[str | None] = mapped_column(
        LONGTEXT, nullable=True, comment="TXT谱曲内容 (当type='TXT'或由MIDI转换)"
    )
    midi_storage_type: Mapped[MidiStorageType | None] = mapped_column(
        SQLAlchemyEnum(MidiStorageType, name="midi_storage_type_enum"),
        nullable=True,
        comment="MIDI文件存储类型 (当type='MIDI_REF')",
    )
    midi_path_or_url: Mapped[str | None] = mapped_column(
        String(512), nullable=True, comment="MIDI文件路径或URL (当type='MIDI_REF')"
    )
    original_midi_filename: Mapped[str | None] = mapped_column(
        String(255), nullable=True, comment="原始MIDI文件名 (当type='MIDI_REF')"
    )
    external_url: Mapped[str | None] = mapped_column(
        String(512), nullable=True, comment="外部谱曲链接 (当type='EXTERNAL_LINK')"
    )

    difficulty: Mapped[ScoreDifficulty | None] = mapped_column(
        SQLAlchemyEnum(ScoreDifficulty, name="score_difficulty_enum"),
        nullable=True,
        comment="难度等级",
    )
    tags: Mapped[list | dict | None] = mapped_column(
        JSON, nullable=True, comment='标签 (例如 ["pop", "piano solo"])'
    )  # SQLAlchemy 的 JSON 类型通常映射到 Python 的 list 或 dict
    cover_image_url: Mapped[str | None] = mapped_column(
        String(255), nullable=True, comment="谱曲封面图片URL"
    )
    duration_seconds: Mapped[int | None] = mapped_column(
        Integer, nullable=True, comment="曲谱预计演奏时长 (秒)"
    )
    is_premium_only: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, index=True, comment="是否仅限付费用户"
    )

    status: Mapped[ScoreStatus] = mapped_column(
        SQLAlchemyEnum(ScoreStatus, name="score_status_enum"),
        nullable=False,
        default=ScoreStatus.pending,
        index=True,
        comment="谱曲状态: draft-草稿, pending-待审核, approved-已通过, rejected-已拒绝, private-私人",
    )

    view_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, comment="查看次数"
    )
    download_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, comment="下载次数"
    )
    favorite_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="收藏次数 (通过 user_favorites 表聚合或定期更新)",
    )
    comment_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="评论数量 (通过 score_comments 表聚合或定期更新)",
    )
    average_rating: Mapped[DECIMAL] = mapped_column(
        DECIMAL(3, 2), nullable=False, default=0.00, comment="平均评分 (0.00 - 5.00)"
    )
    rating_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, comment="评分次数"
    )

    last_played_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="用户最后弹奏此谱时间 (考虑是否需要)",
    )

    approved_by_user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="审核操作的管理员ID",
    )
    approved_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="审核通过/拒绝时间"
    )
    approval_remarks: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="审核备注 (例如拒绝原因)"
    )  # 新增字段

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        index=True,
        comment="谱曲上传时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="谱曲信息更新时间",
    )

    # --- Relationships ---
    uploader: Mapped["User"] = relationship(
        "User",
        foreign_keys=[uploader_user_id],
        back_populates="uploaded_scores",  # 在 User 模型中需要添加 uploaded_scores 关系
    )
    approver: Mapped["User | None"] = relationship(
        "User",
        foreign_keys=[approved_by_user_id],
        back_populates="approved_scores",  # 在 User 模型中需要添加 approved_scores 关系
    )
    category: Mapped["Category"] = relationship("Category", back_populates="scores")

    # 与 UserFavorite 的多对多关系 (通过关联对象)
    # favorited_by_users 关系将在 UserFavorite 模型中定义，这里可以通过 UserFavorite.score 访问
    # 如果需要直接从 Score 访问收藏的用户列表，可以定义一个 secondary 关系，但通常通过关联对象更灵活
    user_favorites_assoc: Mapped[list["UserFavorite"]] = relationship(
        "UserFavorite", back_populates="score", cascade="all, delete-orphan"
    )

    # 与 UserScoreRating 的多对多关系 (通过关联对象)
    ratings_assoc: Mapped[list["UserScoreRating"]] = relationship(
        "UserScoreRating", back_populates="score", cascade="all, delete-orphan"
    )

    # 与 ScoreComment 的一对多关系
    comments: Mapped[list["ScoreComment"]] = relationship(
        "ScoreComment", back_populates="score", cascade="all, delete-orphan"
    )

    # 辅助属性，用于方便地获取收藏了此谱曲的用户列表 (如果需要)
    # @property
    # def favorited_by(self) -> list["User"]:
    #     return [assoc.user for assoc in self.user_favorites_assoc]

    # 辅助属性，用于方便地获取评价了此谱曲的用户及其评分 (如果需要)
    # @property
    # def ratings_summary(self) -> list[tuple["User", int, str | None]]:
    #     return [(assoc.user, assoc.rating, assoc.comment) for assoc in self.ratings_assoc]
