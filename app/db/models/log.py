# TanZouDaShiAPI/app/db/models/log.py
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    ForeignKey,
    BigInteger,
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP

from app.db.base_class import Base
# from .user import User # 避免循环导入


class UserSessionLog(Base):
    __tablename__ = "user_session_logs"

    id: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, autoincrement=True, comment="会话日志ID"
    )
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="用户ID",
    )
    session_start_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        index=True,
        server_default=func.now(),
        comment="会话开始时间",
    )
    session_end_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=True,
        index=True,
        comment="会话结束时间 (用户登出或会话超时时记录)",
    )
    duration_seconds: Mapped[int | None] = mapped_column(
        Integer, nullable=True, comment="会话时长（秒） (在会话结束时计算并填充)"
    )
    device_id: Mapped[str | None] = mapped_column(
        String(128), nullable=True, comment="设备ID"
    )
    ip_address: Mapped[str | None] = mapped_column(
        String(45), nullable=True, comment="IP地址"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="记录创建时间 (通常等于 session_start_at)",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="记录更新时间 (主要在 session_end_at 更新时触发)",
    )

    # --- Relationships ---
    user: Mapped["User"] = relationship("User", back_populates="session_logs")
