# TanZouDaShiAPI/app/db/models/reseller_finance.py
import enum
from datetime import datetime

from sqlalchemy import (
    Integer,
    String,
    Enum as SQLAlchemyEnum,
    ForeignKey,
    Text,
    DECIMAL,
    SmallInteger,  # 使用 SmallInteger 替代 TINYINT
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func  # Import for server-side SQL functions
from sqlalchemy.dialects.mysql import TIMESTAMP

from app.db.base_class import Base
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .user import User  # noqa
    from .license import LicenseKey  # noqa


class CommissionType(str, enum.Enum):
    direct = "direct"  # 直接销售佣金
    indirect_level_1 = "indirect_level_1"  # 一级间接佣金
    indirect_level_2 = "indirect_level_2"  # 二级间接佣金


class CommissionStatus(str, enum.Enum):
    pending = "pending"  # 待处理/待结算
    settled = "settled"  # 已结算
    frozen = "frozen"  # 已冻结
    cancelled = "cancelled"  # 已取消


class CommissionRecord(Base):
    __tablename__ = "commission_records"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="佣金记录唯一标识符"
    )
    beneficiary_reseller_user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="此笔佣金的受益代理ID",
    )
    source_license_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("license_keys.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
        comment="产生佣金的卡密ID",
    )
    source_reseller_user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
        comment="实际销售此卡密的源头代理ID",
    )
    source_order_id: Mapped[str | None] = mapped_column(
        String(64), nullable=True, comment="关联的订单号 (如果通过商城等系统销售)"
    )
    commission_amount: Mapped[DECIMAL] = mapped_column(
        DECIMAL(10, 2), nullable=False, comment="佣金金额"
    )
    commission_rate: Mapped[DECIMAL] = mapped_column(
        DECIMAL(5, 4), nullable=False, comment="计算时的佣金比例"
    )
    commission_base_amount: Mapped[DECIMAL] = mapped_column(
        DECIMAL(10, 2), nullable=False, comment="计算此佣金的基准金额"
    )
    commission_type: Mapped[CommissionType] = mapped_column(
        SQLAlchemyEnum(CommissionType, name="commission_type_enum"),
        nullable=False,
        index=True,
        comment="佣金类型",
    )
    depth_from_source: Mapped[int] = mapped_column(
        SmallInteger,
        nullable=False,
        comment="受益人相对于源头销售代理的层级 (0=自己, 1=上一级, 2=上两级)",
    )  # 使用 SmallInteger 替代 TINYINT
    status: Mapped[CommissionStatus] = mapped_column(
        SQLAlchemyEnum(CommissionStatus, name="commission_status_enum"),
        nullable=False,
        default=CommissionStatus.pending,
        index=True,
        comment="佣金状态",
    )
    settled_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="结算时间"
    )
    related_user_id: Mapped[int | None] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        comment="购买卡密的最终用户ID (如果适用)",
    )
    remarks: Mapped[str | None] = mapped_column(Text, nullable=True, comment="备注")

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="记录创建时间",
    )

    # --- Relationships ---
    beneficiary_reseller: Mapped["User"] = relationship(
        "User",
        foreign_keys=[beneficiary_reseller_user_id],
        back_populates="earned_commissions",  # 需要在 User 模型中添加 earned_commissions
    )
    source_license: Mapped["LicenseKey"] = relationship(
        "LicenseKey",
        foreign_keys=[source_license_id],
        back_populates="generated_commissions",  # 需要在 LicenseKey 模型中添加 generated_commissions
    )
    source_reseller: Mapped["User"] = relationship(
        "User",
        foreign_keys=[source_reseller_user_id],
        back_populates="sold_for_commissions",  # 需要在 User 模型中添加 sold_for_commissions
    )
    related_user: Mapped["User | None"] = relationship(
        "User",
        foreign_keys=[related_user_id],
        back_populates="activated_key_commissions",  # 需要在 User 模型中添加 activated_key_commissions
    )


class WithdrawalStatus(str, enum.Enum):
    pending = "pending"  # 待处理
    approved = "approved"  # 已批准
    rejected = "rejected"  # 已拒绝
    completed = "completed"  # 已完成 (已打款)
    failed = "failed"  # 打款失败


class WithdrawalRecord(Base):  # 任务要求 WithdrawalRecord，文档是 WithdrawalRequest
    __tablename__ = "withdrawal_records"  # 根据任务命名

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="提现请求唯一标识符"
    )
    reseller_user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="发起提现的代理用户ID",
    )
    amount: Mapped[DECIMAL] = mapped_column(
        DECIMAL(10, 2), nullable=False, comment="提现金额"
    )
    status: Mapped[WithdrawalStatus] = mapped_column(
        SQLAlchemyEnum(WithdrawalStatus, name="withdrawal_status_enum"),
        nullable=False,
        default=WithdrawalStatus.pending,
        index=True,
        comment="请求状态",
    )
    # 文档中 payment_method 和 payment_account 是 VARCHAR, 这里保持一致
    payment_method: Mapped[str | None] = mapped_column(
        String(50), nullable=True, comment="提现方式 (例如 支付宝, 微信, 银行卡)"
    )
    payment_account: Mapped[str | None] = mapped_column(
        String(255), nullable=True, comment="提现账户信息"
    )
    remarks: Mapped[str | None] = mapped_column(Text, nullable=True, comment="用户备注")
    admin_notes: Mapped[str | None] = mapped_column(
        Text, nullable=True, comment="管理员处理备注"
    )
    processed_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True), nullable=True, comment="处理时间"
    )

    created_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="请求创建时间",
    )
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        server_onupdate=func.now(),
        comment="请求更新时间",
    )

    # --- Relationships ---
    reseller: Mapped["User"] = relationship(
        "User",
        foreign_keys=[reseller_user_id],
        back_populates="withdrawal_requests",  # 需要在 User 模型中添加 withdrawal_requests
    )


# 需要在 User 模型中添加反向关系:
# earned_commissions: Mapped[list["CommissionRecord"]] = relationship("CommissionRecord", foreign_keys="[CommissionRecord.beneficiary_reseller_user_id]", back_populates="beneficiary_reseller")
# sold_for_commissions: Mapped[list["CommissionRecord"]] = relationship("CommissionRecord", foreign_keys="[CommissionRecord.source_reseller_user_id]", back_populates="source_reseller")
# activated_key_commissions: Mapped[list["CommissionRecord"]] = relationship("CommissionRecord", foreign_keys="[CommissionRecord.related_user_id]", back_populates="related_user")
# withdrawal_requests: Mapped[list["WithdrawalRecord"]] = relationship("WithdrawalRecord", back_populates="reseller")

# 需要在 LicenseKey 模型中添加反向关系:
# generated_commissions: Mapped[list["CommissionRecord"]] = relationship("CommissionRecord", back_populates="source_license")
