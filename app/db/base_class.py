# app/db/base_class.py
from sqlalchemy.ext.declarative import declarative_base

# SQLAlchemy Declarative Base
# 所有数据库模型都将继承自这个 Base 类
Base = declarative_base()

# 也可以在这里定义一个通用的基类，包含所有表共有的字段，例如 id, created_at, updated_at
# class BaseModel(Base):
#     __abstract__ = True  # 表示这个类不会被映射到数据库表
#
#     id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
#     created_at: Mapped[datetime] = mapped_column(
#         DateTime(timezone=True), default=lambda: datetime.now(timezone.utc)
#     )
#     updated_at: Mapped[datetime] = mapped_column(
#         DateTime(timezone=True),
#         default=lambda: datetime.now(timezone.utc),
#         onupdate=lambda: datetime.now(timezone.utc),
#     )
#
# 这样其他模型就可以继承自 BaseModel:
# class User(BaseModel):
#     __tablename__ = "users"
#     # ... 其他字段
