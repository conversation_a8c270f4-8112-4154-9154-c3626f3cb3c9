# TanZouDaShiAPI/app/schemas/ticket.py
from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

# 从模型中导入枚举类型，确保一致性
from app.db.models.ticket import TicketStatus, TicketPriority
from app.schemas.user import (
    UserResponse,
    PaginatedResponse,
)  # 用于嵌套显示用户信息 和 分页响应


# --- Ticket Schemas ---


class TicketBase(BaseModel):
    """工单基础模型"""

    title: str = Field(..., min_length=1, max_length=255, description="工单标题")
    description: str = Field(..., min_length=1, description="工单详细内容")
    priority: TicketPriority = Field(
        default=TicketPriority.medium, description="工单优先级"
    )
    category: Optional[str] = Field(None, max_length=50, description="工单分类")


class TicketCreate(TicketBase):
    """创建工单时使用的模型"""

    # user_id 将从当前认证用户获取，不在请求体中
    # attachments: Optional[List[dict]] = Field(None, description="附件信息列表，例如 [{'file_id': 'uuid', 'filename': 'name.png'}]")
    pass


class TicketUpdate(BaseModel):
    """更新工单时使用的模型 (例如管理员更新状态或指派人)"""

    status: Optional[TicketStatus] = Field(None, description="新的工单状态")
    priority: Optional[TicketPriority] = Field(None, description="新的工单优先级")
    assignee_user_id: Optional[int] = Field(None, description="新的负责人用户ID")
    category: Optional[str] = Field(None, max_length=50, description="新的工单分类")


class TicketMessageNestedResponse(BaseModel):
    """用于在工单响应中嵌套显示的最新消息摘要"""

    id: int
    sender_id: int
    content: str = Field(..., description="消息内容摘要")
    created_at: datetime
    is_internal: bool

    class Config:
        from_attributes = True


class TicketResponse(TicketBase):
    """工单响应模型"""

    id: int = Field(..., description="工单ID")
    ticket_number: str = Field(..., description="工单号")
    user_id: int = Field(..., description="提交用户ID")
    status: TicketStatus = Field(..., description="当前工单状态")
    assignee_user_id: Optional[int] = Field(None, description="负责人用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="最后更新时间")
    resolved_at: Optional[datetime] = Field(None, description="解决时间")
    closed_at: Optional[datetime] = Field(None, description="关闭时间")
    last_reply_at: Optional[datetime] = Field(None, description="最后回复时间")

    user: Optional[UserResponse] = Field(None, description="提交工单的用户信息")
    assignee: Optional[UserResponse] = Field(None, description="处理工单的负责人信息")
    # messages: Optional[List["TicketMessageResponse"]] = Field(None, description="工单的完整消息列表") # 完整列表可能数据量大
    latest_message_summary: Optional[TicketMessageNestedResponse] = Field(
        None, description="最新一条消息的摘要"
    )

    class Config:
        from_attributes = True  # Pydantic V2
        from_attributes = True  # Pydantic V2


class TicketDetailResponse(TicketResponse):
    """工单详情响应模型，包含完整的消息列表"""

    messages: List["TicketMessageResponse"] = Field(
        default_factory=list, description="工单的完整消息列表"
    )

    class Config:
        from_attributes = True


# --- TicketMessage Schemas ---


class TicketMessageBase(BaseModel):
    """工单消息基础模型"""

    content: str = Field(..., min_length=1, description="消息内容")
    # attachments: Optional[List[dict]] = Field(None, description="附件信息列表") # 附件处理逻辑可能更复杂


class TicketMessageCreate(TicketMessageBase):
    """创建工单消息时使用的模型"""

    # ticket_id 将从路径参数获取
    # sender_id 将从当前认证用户获取
    is_internal: Optional[bool] = Field(
        default=False, description="是否为内部消息 (仅管理员/客服可见)"
    )


class TicketMessageResponse(TicketMessageBase):
    """工单消息响应模型"""

    id: int = Field(..., description="消息ID")
    ticket_id: int = Field(..., description="关联的工单ID")
    sender_id: int = Field(..., description="发送者用户ID")
    is_internal: bool = Field(..., description="是否为内部消息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    sender: Optional[UserResponse] = Field(None, description="消息发送者用户信息")
    # ticket: Optional[TicketResponse] = Field(None, description="所属工单信息") # 避免循环引用，通常不在此处完整嵌套Ticket

    class Config:
        from_attributes = True  # Pydantic V2
        from_attributes = True  # Pydantic V2


class TicketUpdateByAdmin(TicketUpdate):
    """管理员更新工单时使用的模型"""

    # 继承自 TicketUpdate，已包含 status, priority, assignee_user_id
    # category 已在 TicketUpdate 中通过修改添加
    pass


class TicketListResponse(PaginatedResponse[TicketResponse]):
    """工单列表响应模型 (分页)"""

    pass


# Pydantic V2: 使用 model_rebuild 来处理前向引用
# 确保在所有相关模型定义完成后调用
TicketDetailResponse.model_rebuild()
TicketResponse.model_rebuild()  # TicketResponse 可能也依赖于 TicketMessageNestedResponse
TicketMessageResponse.model_rebuild()  # TicketMessageResponse 可能依赖 UserResponse
