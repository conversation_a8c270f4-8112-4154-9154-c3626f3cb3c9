# app/schemas/dashboard.py
from typing import Dict, List, Any, Optional
from datetime import datetime

from pydantic import BaseModel, Field


class UserStatsBase(BaseModel):
    """用户统计基础模型"""
    total: int = Field(..., description="总用户数")
    new_in_period: int = Field(..., description="期间内新增用户数")
    active_in_period: int = Field(..., description="期间内活跃用户数")


class ResellerStatsBase(BaseModel):
    """代理统计基础模型"""
    total: int = Field(..., description="总代理数")


class LicenseKeyStatsBase(BaseModel):
    """卡密统计基础模型"""
    total: int = Field(..., description="总卡密数")
    used: int = Field(..., description="已使用卡密数")
    new_activations: int = Field(..., description="期间内新激活数")
    activation_rate: float = Field(..., description="激活率百分比")


class ScoreStatsBase(BaseModel):
    """谱曲统计基础模型"""
    total: int = Field(..., description="总谱曲数")
    new_in_period: int = Field(..., description="期间内新增谱曲数")


class TicketStatsBase(BaseModel):
    """工单统计基础模型"""
    total: int = Field(..., description="总工单数")
    pending: int = Field(..., description="待处理工单数")
    new_in_period: int = Field(..., description="期间内新增工单数")


class RevenueStatsBase(BaseModel):
    """收入统计基础模型"""
    period_revenue: float = Field(..., description="期间内收入")
    total_commissions: float = Field(..., description="总佣金")
    pending_withdrawals: float = Field(..., description="待处理提现金额")


class DashboardOverviewResponse(BaseModel):
    """仪表盘概览响应"""
    users: UserStatsBase = Field(..., description="用户统计")
    resellers: ResellerStatsBase = Field(..., description="代理统计")
    license_keys: LicenseKeyStatsBase = Field(..., description="卡密统计")
    scores: ScoreStatsBase = Field(..., description="谱曲统计")
    tickets: TicketStatsBase = Field(..., description="工单统计")
    revenue: RevenueStatsBase = Field(..., description="收入统计")
    period_days: int = Field(..., description="统计周期天数")

    class Config:
        from_attributes = True


class DailyDataPoint(BaseModel):
    """每日数据点"""
    date: str = Field(..., description="日期 (YYYY-MM-DD)")
    count: int = Field(..., description="数量")


class DailyRevenuePoint(BaseModel):
    """每日收入数据点"""
    date: str = Field(..., description="日期 (YYYY-MM-DD)")
    revenue: float = Field(..., description="收入金额")


class UserStatisticsResponse(BaseModel):
    """用户统计详细响应"""
    role_distribution: Dict[str, int] = Field(..., description="按角色分布")
    status_distribution: Dict[str, int] = Field(..., description="按状态分布")
    source_distribution: Dict[str, int] = Field(..., description="按创建来源分布")
    daily_registrations: List[DailyDataPoint] = Field(..., description="每日注册趋势")
    period_days: int = Field(..., description="统计周期天数")

    class Config:
        from_attributes = True


class LicenseTypeRevenueItem(BaseModel):
    """卡密类型收入项"""
    license_type: str = Field(..., description="卡密类型名称")
    unit_price: float = Field(..., description="单价")
    activations: int = Field(..., description="激活次数")
    total_revenue: float = Field(..., description="总收入")


class CommissionStatItem(BaseModel):
    """佣金统计项"""
    total: float = Field(..., description="总金额")
    count: int = Field(..., description="记录数")


class RevenueStatisticsResponse(BaseModel):
    """收入统计详细响应"""
    license_type_revenue: List[LicenseTypeRevenueItem] = Field(..., description="按卡密类型收入")
    daily_revenue: List[DailyRevenuePoint] = Field(..., description="每日收入趋势")
    commission_statistics: Dict[str, CommissionStatItem] = Field(..., description="佣金统计")
    period_days: int = Field(..., description="统计周期天数")

    class Config:
        from_attributes = True


class ScoreStatisticsResponse(BaseModel):
    """谱曲统计详细响应"""
    category_distribution: Dict[str, int] = Field(..., description="按分类分布")
    difficulty_distribution: Dict[str, int] = Field(..., description="按难度分布")
    status_distribution: Dict[str, int] = Field(..., description="按状态分布")
    daily_uploads: List[DailyDataPoint] = Field(..., description="每日上传趋势")
    top_rated_scores: List[Dict[str, Any]] = Field(..., description="评分最高的谱曲")
    most_favorited_scores: List[Dict[str, Any]] = Field(..., description="收藏最多的谱曲")
    period_days: int = Field(..., description="统计周期天数")

    class Config:
        from_attributes = True


class TicketStatisticsResponse(BaseModel):
    """工单统计详细响应"""
    status_distribution: Dict[str, int] = Field(..., description="按状态分布")
    priority_distribution: Dict[str, int] = Field(..., description="按优先级分布")
    category_distribution: Dict[str, int] = Field(..., description="按分类分布")
    daily_tickets: List[DailyDataPoint] = Field(..., description="每日工单趋势")
    avg_response_time: float = Field(..., description="平均响应时间(小时)")
    avg_resolution_time: float = Field(..., description="平均解决时间(小时)")
    period_days: int = Field(..., description="统计周期天数")

    class Config:
        from_attributes = True


class ActivityItem(BaseModel):
    """活动项"""
    id: int = Field(..., description="活动ID")
    type: str = Field(..., description="活动类型")
    title: str = Field(..., description="活动标题")
    description: str = Field(..., description="活动描述")
    user_id: Optional[int] = Field(None, description="相关用户ID")
    username: Optional[str] = Field(None, description="相关用户名")
    created_at: datetime = Field(..., description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class RecentActivitiesResponse(BaseModel):
    """最近活动响应"""
    activities: List[ActivityItem] = Field(..., description="活动列表")
    total_count: int = Field(..., description="总活动数")

    class Config:
        from_attributes = True


class SystemHealthResponse(BaseModel):
    """系统健康状态响应"""
    database_status: str = Field(..., description="数据库状态")
    redis_status: Optional[str] = Field(None, description="Redis状态")
    disk_usage: Dict[str, Any] = Field(..., description="磁盘使用情况")
    memory_usage: Dict[str, Any] = Field(..., description="内存使用情况")
    cpu_usage: float = Field(..., description="CPU使用率")
    uptime: str = Field(..., description="运行时间")
    version: str = Field(..., description="系统版本")

    class Config:
        from_attributes = True


class ExportTaskResponse(BaseModel):
    """导出任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比")
    download_url: Optional[str] = Field(None, description="下载链接")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")

    class Config:
        from_attributes = True
