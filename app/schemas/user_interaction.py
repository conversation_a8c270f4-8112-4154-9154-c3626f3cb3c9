# TanZouDaShiAPI/app/schemas/user_interaction.py
from typing import Optional, List, ForwardRef  # ForwardRef 用于处理循环依赖
from pydantic import BaseModel, Field, conint, constr
from datetime import datetime

# 从模型文件导入 Enum 类型
from app.db.models.user_interaction import ScoreCommentStatus

# 引用其他模块的 schema
from .user import UserResponse  # 导入用于嵌套的用户响应模型

# from .score import ScoreResponse # 避免直接导入 ScoreResponse 导致循环，使用 ScoreNestedResponse
from app.schemas.user import PaginatedResponse as BasePaginatedResponse  # 用于分页响应


# 为 Score 创建一个简化的嵌套响应模型
class ScoreNestedResponse(BaseModel):
    id: int
    title: str
    cover_image_url: Optional[str] = None  # HttpUrl type can be used if validated

    class Config:
        from_attributes = True


# --- UserFavorite Schemas ---


class UserFavoriteBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    score_id: int = Field(..., description="谱曲ID")


class UserFavoriteCreate(UserFavoriteBase):
    pass


class UserFavoriteResponse(UserFavoriteBase):
    created_at: datetime = Field(..., description="收藏时间")
    user: Optional[UserResponse] = Field(None, description="收藏用户信息")
    score: Optional[ScoreNestedResponse] = Field(
        None, description="被收藏的谱曲简要信息"
    )

    class Config:
        from_attributes = True


# --- UserScoreRating Schemas ---


class UserScoreRatingBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    score_id: int = Field(..., description="谱曲ID")
    rating: conint(ge=1, le=5) = Field(..., description="评分 (1-5)")
    comment: Optional[constr(max_length=1000)] = Field(
        None, description="评论内容 (可选，最大长度1000)"
    )


class UserScoreRatingCreate(UserScoreRatingBase):
    pass


class UserScoreRatingUpdate(BaseModel):
    rating: Optional[conint(ge=1, le=5)] = Field(None, description="评分 (1-5)")
    comment: Optional[constr(max_length=1000)] = Field(
        None, description="评论内容 (可选，最大长度1000)"
    )


class UserScoreRatingResponse(UserScoreRatingBase):
    created_at: datetime = Field(..., description="评分创建时间")
    updated_at: datetime = Field(..., description="评分更新时间")
    user: Optional[UserResponse] = Field(None, description="评分用户信息")
    score: Optional[ScoreNestedResponse] = Field(
        None, description="被评分的谱曲简要信息"
    )

    class Config:
        from_attributes = True


# --- ScoreComment Schemas ---

# ForwardRef 用于处理 replies 和 parent_comment 的循环引用
ScoreCommentResponseFwdRef = ForwardRef("ScoreCommentResponse")


class ScoreCommentBase(BaseModel):
    score_id: int = Field(..., description="谱曲ID")
    # user_id 在创建时从当前用户获取，不在Base中强制
    parent_comment_id: Optional[int] = Field(
        None, description="父评论ID (用于回复嵌套评论)"
    )
    content: constr(min_length=1, max_length=2000) = Field(
        ..., description="评论内容 (1-2000字符)"
    )
    status: ScoreCommentStatus = Field(
        default=ScoreCommentStatus.visible, description="评论状态"
    )


class ScoreCommentCreate(ScoreCommentBase):
    user_id: int = Field(..., description="评论用户ID (将从当前认证用户获取)")


class ScoreCommentUpdate(BaseModel):
    content: Optional[constr(min_length=1, max_length=2000)] = Field(
        None, description="评论内容"
    )
    status: Optional[ScoreCommentStatus] = Field(
        None, description="评论状态 (管理员可修改)"
    )


class ScoreCommentResponse(ScoreCommentBase):
    id: int = Field(..., description="评论ID")
    user_id: int = Field(..., description="评论用户ID")
    created_at: datetime = Field(..., description="评论创建时间")
    updated_at: datetime = Field(..., description="评论更新时间")

    user: Optional[UserResponse] = Field(None, description="评论用户信息")
    score: Optional[ScoreNestedResponse] = Field(
        None, description="被评论的谱曲简要信息"
    )

    replies: Optional[List[ScoreCommentResponseFwdRef]] = Field(
        None, description="对此评论的回复列表 (子评论)"
    )
    parent_comment: Optional[ScoreCommentResponseFwdRef] = Field(
        None, description="此评论回复的父评论"
    )  # 如果是顶级评论则为null

    class Config:
        from_attributes = True


# 更新 ForwardRef
ScoreCommentResponse.update_forward_refs()
# Pydantic V2:
# ScoreCommentResponse.model_rebuild()


# --- Admin Schemas for ScoreComment ---


class AdminScoreCommentResponse(ScoreCommentResponse):
    """
    管理员接口使用的评论响应模型。
    继承自 ScoreCommentResponse，依赖 CRUD 层加载 user 和 score 信息。
    """

    pass


class PaginatedAdminScoreCommentResponse(
    BasePaginatedResponse[AdminScoreCommentResponse]
):
    """
    管理员接口评论列表的分页响应模型。
    """

    pass


# --- Schemas for Score Rating Summary ---
class ScoreRatingSummaryResponse(BaseModel):
    score_id: int = Field(..., description="谱曲ID")
    average_rating: Optional[float] = Field(None, description="平均评分 (1.0-5.0)")
    total_ratings: int = Field(0, description="总评分人数")

    class Config:
        from_attributes = True


# --- Schema for checking if score is favorited ---
class ScoreIsFavoritedResponse(BaseModel):
    score_id: int = Field(..., description="谱曲ID")
    is_favorited: bool = Field(..., description="当前用户是否已收藏该谱曲")


# --- Schema for updating comment status ---
class ScoreCommentStatusUpdate(BaseModel):
    status: ScoreCommentStatus = Field(..., description="新的评论状态")
