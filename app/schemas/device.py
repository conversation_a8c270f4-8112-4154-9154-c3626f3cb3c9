from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


# 中文注释：设备基础信息模型
class DeviceBase(BaseModel):
    device_id: str = Field(..., description="唯一设备标识符")
    device_alias: Optional[str] = Field(None, description="设备别名")


# 中文注释：用于创建新设备绑定的模型
class DeviceCreate(DeviceBase):
    # 继承自 DeviceBase，包含 device_id 和 device_alias
    # 创建时通常只需要这些基础信息，其他如 user_id, session_token 由后端逻辑处理
    pass


# 中文注释：用于注册设备的请求模型
# class DeviceRegisterRequest(BaseModel):
#     device_unique_id: str = Field(..., description="设备唯一标识符")
#     device_name: str = Field(..., description="设备名称")
#     device_model: Optional[str] = Field(None, description="设备型号")
#     system_info: Optional[str] = Field(None, description="系统信息")
#     app_version: Optional[str] = Field(None, description="应用版本")


# 中文注释：GET /me/devices 接口响应中的单个设备信息
class DeviceResponse(DeviceBase):
    bound_at: datetime = Field(..., description="设备绑定时间")
    last_active_at: Optional[datetime] = Field(None, description="设备最后活跃时间")
    current_session_active: Optional[bool] = Field(
        False, description="当前会话是否活跃"
    )

    class Config:
        from_attributes = True


# 中文注释：设备信息响应模型
# class DeviceInfoResponse(DeviceBase):
#     id: int = Field(..., description="设备数据库ID")
#     device_name: Optional[str] = Field(None, description="设备名称")
#     device_model: Optional[str] = Field(None, description="设备型号")
#     system_info: Optional[str] = Field(None, description="系统信息")
#     app_version: Optional[str] = Field(None, description="应用版本")
#     created_at: datetime = Field(..., description="创建时间")
#     updated_at: Optional[datetime] = Field(None, description="更新时间")
#     bound_at: Optional[datetime] = Field(None, description="设备绑定时间")
#     last_active_at: Optional[datetime] = Field(None, description="设备最后活跃时间")
#
#     class Config:
#         from_attributes = True


# 中文注释：GET /me/devices 接口响应中的元数据
class DeviceListMeta(BaseModel):
    current_bound_count: int = Field(..., description="当前已绑定设备数量")
    max_bound_count: int = Field(..., description="最大可绑定设备数量")


# 中文注释：GET /me/devices 接口的完整响应模型
class DeviceListResponse(BaseModel):
    data: List[DeviceResponse] = Field(..., description="绑定的设备列表")
    meta: DeviceListMeta = Field(..., description="设备列表元数据")


# 中文注释：PUT /me/devices/{deviceId} 请求体，用于更新设备别名
class DeviceUpdate(BaseModel):
    device_alias: str = Field(
        ..., min_length=1, max_length=50, description="新的设备别名"
    )


# 中文注释：DELETE /me/devices/{deviceId}/unbind 响应体
class DeviceUnbindResponse(BaseModel):
    message: str = Field(..., description="解绑操作结果消息")
    time_deducted_message: Optional[str] = Field(
        None, description="账户时长扣除信息 (如果有时限卡)"
    )
