from typing import Optional, List, TypeVar, Generic
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from app.db.models.user import UserRole, UserStatus  # 导入枚举类型


# 中文注释：用户基础模型
class UserBase(BaseModel):
    username: str = Field(..., description="用户名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone_number: Optional[str] = Field(None, description="手机号码")
    nickname: Optional[str] = Field(None, description="昵称")
    avatar_url: Optional[str] = Field(None, description="头像链接")


# 中文注释：用于创建用户的模型
class UserCreate(UserBase):
    password: str = Field(..., description="用户密码")
    verification_code: Optional[str] = Field(
        None, description="验证码，用于邮箱或短信验证"
    )


# 中文注释：用于更新用户信息的模型
class UserProfileUpdate(BaseModel):
    username: Optional[str] = Field(None, description="新用户名")
    email: Optional[EmailStr] = Field(None, description="新电子邮箱，更新可能需要验证")
    nickname: Optional[str] = Field(None, description="新昵称")
    avatar_url: Optional[str] = Field(None, description="新头像链接")
    # phone_number: Optional[str] = Field(None, description="新手机号码，更新可能需要验证")


# 中文注释：数据库中存储的用户模型 (包含密码哈希)
class UserInDBBase(UserBase):
    id: int = Field(..., description="用户ID")
    hashed_password: str = Field(..., description="哈希后的密码")
    role: str = Field(default="user", description="用户角色")
    status: str = Field(default="active", description="账户状态")
    email_verified_at: Optional[datetime] = Field(None, description="邮箱验证时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    creation_source: Optional[str] = Field(
        None, description="用户创建来源，例如 'license_activation'"
    )
    max_concurrent_devices: Optional[int] = Field(1, description="最大同时在线设备数")
    permanent_license_last_unbind_month: Optional[str] = Field(
        None, description="永久卡密最后解绑月份 YYYY-MM"
    )
    permanent_license_unbind_count_current_month: Optional[int] = Field(
        0, description="永久卡密当月已解绑次数"
    )

    class Config:
        from_attributes = True


# 中文注释：用于API响应的用户信息模型 (不含密码)
class UserResponse(UserBase):
    id: int = Field(..., description="用户ID", examples=[1, 123, 456])
    role: str = Field(
        description="用户角色",
        examples=["user", "admin", "reseller"]
    )
    status: str = Field(
        description="账户状态",
        examples=["active", "inactive", "banned"]
    )
    email_verified_at: Optional[datetime] = Field(
        None,
        description="邮箱验证时间",
        examples=["2024-01-01T10:00:00Z", None]
    )
    created_at: datetime = Field(
        description="创建时间",
        examples=["2024-01-01T10:00:00Z"]
    )

    class Config:
        from_attributes = True
        json_schema_extra = {
            "examples": [
                {
                    "id": 1,
                    "username": "testuser",
                    "nickname": "测试用户",
                    "email": "<EMAIL>",
                    "phone_number": "13800138000",
                    "avatar_url": "https://example.com/avatar.jpg",
                    "max_concurrent_devices": 3,
                    "permanent_license_unbind_count_current_month": 0,
                    "role": "user",
                    "status": "active",
                    "email_verified_at": "2024-01-01T10:00:00Z",
                    "created_at": "2024-01-01T10:00:00Z"
                },
                {
                    "id": 2,
                    "username": "admin",
                    "nickname": "管理员",
                    "email": "<EMAIL>",
                    "phone_number": None,
                    "avatar_url": None,
                    "max_concurrent_devices": 10,
                    "permanent_license_unbind_count_current_month": 0,
                    "role": "admin",
                    "status": "active",
                    "email_verified_at": "2024-01-01T10:00:00Z",
                    "created_at": "2024-01-01T10:00:00Z"
                }
            ]
        }


# 中文注释：GET /me 接口的响应模型中嵌套的卡密信息
class UserLicenseInfo(BaseModel):
    key_string: str = Field(..., description="卡密字符串")
    license_type_code: str = Field(..., description="卡密类型代码")
    license_type_name: str = Field(..., description="卡密类型名称")
    activated_at: Optional[datetime] = Field(None, description="激活时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

    class Config:
        from_attributes = True


# 中文注释：GET /me 接口的完整响应模型
class UserMeResponse(UserResponse):
    license_info: Optional[UserLicenseInfo] = Field(
        None, description="当前激活的卡密信息"
    )


# 中文注释：修改当前用户密码的请求体
class UserPasswordUpdate(BaseModel):
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., description="新密码")


# 中文注释：管理员创建用户时使用的模型
class AdminUserCreate(UserBase):
    password: str = Field(..., description="用户密码")
    role: UserRole = Field(UserRole.user, description="用户角色")
    status: UserStatus = Field(UserStatus.active, description="账户状态")
    max_concurrent_devices: Optional[int] = Field(
        None, description="最大同时在线设备数, 不填则使用默认值"
    )
    # creation_source 将在 CRUD 层面设置为 admin_created


# 中文注释：管理员更新用户信息时使用的模型
class AdminUserUpdate(BaseModel):
    username: Optional[str] = Field(None, description="新用户名 (唯一)")
    email: Optional[EmailStr] = Field(None, description="新电子邮箱 (唯一)")
    phone_number: Optional[str] = Field(None, description="新手机号码 (唯一)")
    nickname: Optional[str] = Field(None, description="新昵称")
    avatar_url: Optional[str] = Field(None, description="新头像链接")
    role: Optional[UserRole] = Field(None, description="新用户角色")
    status: Optional[UserStatus] = Field(None, description="新账户状态")
    max_concurrent_devices: Optional[int] = Field(
        None, description="新最大同时在线设备数"
    )
    # 注意：密码重置、邮箱/手机验证状态不在此处直接修改


# 中文注释：用于列表接口的分页响应模型
T = TypeVar("T")


class PaginatedResponse(BaseModel, Generic[T]):
    total: int = Field(..., description="总记录数")
    items: List[T] = Field(..., description="当前页的记录列表")
    page: Optional[int] = Field(None, description="当前页码")
    limit: Optional[int] = Field(None, description="每页记录数")
    # total_pages: Optional[int] = Field(None, description="总页数") # 可选，如果前端需要


# 中文注释：用户头像更新响应模型
class UserAvatarUpdateResponse(BaseModel):
    avatar_url: str = Field(..., description="更新后的头像URL")

    class Config:
        from_attributes = True
