from typing import Optional, Literal
from pydantic import BaseModel, EmailStr, Field
from .token import Token
from .user import UserResponse  # 用于登录响应中的 user_info
from .license import (
    LicenseInfoBase,
    ActivateLicenseAccountLicenseInfo,
)  # 用于登录和激活响应中的 license_info


# 中文注释：用户登录请求体 - 用户名密码登录
class LoginPasswordRequest(BaseModel):
    login_type: Literal["password"] = Field("password", description="登录类型")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    device_id: str = Field(..., description="唯一设备标识符")


# 中文注释：用户登录请求体 - 卡密登录
class LoginLicenseRequest(BaseModel):
    login_type: Literal["license"] = Field("license", description="登录类型")
    key_string: str = Field(..., description="卡密字符串")
    device_id: str = Field(..., description="唯一设备标识符")


# 中文注释：管理员登录请求体 - 用户名密码登录（无需设备码）
class AdminLoginPasswordRequest(BaseModel):
    login_type: Literal["password"] = Field("password", description="登录类型")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


# 中文注释：代理登录请求体 - 用户名密码登录（无需设备码）
class ResellerLoginPasswordRequest(BaseModel):
    login_type: Literal["password"] = Field("password", description="登录类型")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


# 中文注释：用户登录成功响应体
class LoginResponse(Token):  # 继承 Token 模型，包含 access_token, refresh_token 等
    user_info: UserResponse = Field(..., description="用户信息")
    license_info: Optional[LicenseInfoBase] = Field(
        None, description="卡密信息 (如果适用)"
    )
    device_status: str = Field(
        ...,
        description="设备状态 (例如 current_device_active, new_device_auto_bound)",
        examples=["current_device_active", "new_device_auto_bound"]
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    "session_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                    "token_type": "Bearer",
                    "expires_in": 1800,
                    "user_info": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "phone_number": "13800138000",
                        "avatar_url": "https://example.com/avatar.jpg",
                        "role": "user",
                        "status": "active",
                        "email_verified_at": "2024-01-01T10:00:00Z",
                        "created_at": "2024-01-01T10:00:00Z"
                    },
                    "license_info": {
                        "id": 1,
                        "key_string": "ABCD-EFGH-IJKL-MNOP",
                        "license_type_name": "月卡",
                        "license_type_code": "monthly",
                        "expires_at": "2024-02-01T10:00:00Z",
                        "status": "used"
                    },
                    "device_status": "current_device_active"
                }
            ]
        }


# 中文注释：管理员/代理登录成功响应体（无设备状态）
class AdminResellerLoginResponse(Token):  # 继承 Token 模型，包含 access_token, refresh_token 等
    user_info: UserResponse = Field(..., description="用户信息")
    license_info: Optional[LicenseInfoBase] = Field(
        None, description="卡密信息 (如果适用)"
    )


# 中文注释：卡密登录时，如果卡密未激活，引导激活的响应体
class LoginLicenseActionRequiredData(BaseModel):
    action_required: str = Field(
        "activate_license_account", description="需要执行的操作"
    )
    key_string: str = Field(..., description="需要激活的卡密字符串")


class LoginLicenseActionRequiredResponse(BaseModel):
    code: int = Field(..., description="自定义业务状态码，例如 40301")
    message: str = Field(..., description="提示信息")
    data: LoginLicenseActionRequiredData = Field(
        ..., description="需要执行操作的详细数据"
    )


# 中文注释：用户注册请求体
class RegisterRequest(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone_number: Optional[str] = Field(None, description="手机号码")
    verification_code: Optional[str] = Field(None, description="验证码")


# 中文注释：用户注册成功响应体 (类似登录响应，包含Token和用户信息)
class RegisterResponse(LoginResponse):
    pass


# 中文注释：用户登出请求体
class LogoutRequest(BaseModel):
    refresh_token: Optional[str] = Field(
        None, description="可选的刷新令牌，用于使其失效"
    )
    session_token: str = Field(
        ..., description="当前会话令牌，用于定位要登出的设备会话"
    )


# 中文注释：请求密码重置请求体
class PasswordResetRequestRequest(BaseModel):
    email: EmailStr = Field(..., description="用户注册的电子邮箱")


# 中文注释：执行密码重置请求体
class PasswordResetExecuteRequest(BaseModel):
    reset_token: str = Field(..., description="从邮件中获取的重置令牌或验证码")
    new_password: str = Field(..., description="新密码")


# 中文注释：发送验证码请求体
class VerificationCodeSendRequest(BaseModel):
    type: Literal["email", "sms"] = Field(..., description="验证码类型")
    recipient: str = Field(..., description="接收者 (邮箱地址或手机号码)")
    purpose: Literal["register", "login", "password_reset", "action_confirm"] = Field(
        ..., description="验证码用途"
    )


# 中文注释：会话心跳请求体
class HeartbeatRequest(BaseModel):
    session_token: str = Field(..., description="当前会话令牌")


# 中文注释：激活激活码并创建/关联账户请求体
class ActivateLicenseAccountRequest(BaseModel):
    key_string: str = Field(..., description="从 /login 引导响应中获取的卡密字符串")
    username: str = Field(..., description="新账户的用户名")
    password: str = Field(..., description="新账户的密码")
    email: Optional[EmailStr] = Field(None, description="可选的电子邮箱")
    phone_number: Optional[str] = Field(None, description="可选的手机号码")
    device_id: str = Field(..., description="唯一设备标识符，用于设备绑定")


# 中文注释：激活激活码并创建/关联账户响应体 - 用户信息部分
class ActivateLicenseAccountUserInfo(BaseModel):
    id: int = Field(..., description="新创建用户的ID")
    username: str = Field(..., description="新创建用户的用户名")


# 中文注释：激活激活码并创建/关联账户响应体
class ActivateLicenseAccountResponse(BaseModel):
    message: str = Field(..., description="操作结果消息")
    user_info: ActivateLicenseAccountUserInfo = Field(..., description="创建的用户信息")
    license_info: ActivateLicenseAccountLicenseInfo = Field(
        ..., description="绑定的卡密信息"
    )
