from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class FileUploadResponse(BaseModel):
    file_id: Optional[str] = None
    filename: str
    url: str
    file_type: str
    file_size_bytes: int

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "file_id": "uuid_or_int_file_id",
                "filename": "original_filename.jpg",
                "url": "/static/uploads/avatars/uuid_or_int_file_id.jpg",
                "file_type": "image/jpeg",
                "file_size_bytes": 102400,
            }
        }


class AttachmentUploadResponse(FileUploadResponse):
    db_id: Optional[int] = Field(None, description="附件在数据库中的ID")
    related_object_type: Optional[str] = Field(None, description="关联对象类型")
    related_object_id: Optional[int] = Field(None, description="关联对象ID")
    uploader_user_id: Optional[int] = Field(None, description="上传者用户ID")
    created_at: Optional[datetime] = Field(None, description="附件记录创建时间")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "db_id": 123,
                "file_id": "some_uuid_as_file_id",
                "filename": "invoice.pdf",
                "url": "/static/uploads/ticket_attachment/some_uuid_as_file_id.pdf",
                "file_type": "application/pdf",
                "file_size_bytes": 204800,
                "related_object_type": "ticket_reply",
                "related_object_id": 1,
                "uploader_user_id": 1,
                "created_at": "2023-10-26T10:30:00Z",
            }
        }


class AttachmentQueryResponse(BaseModel):
    id: int = Field(..., description="附件在数据库中的主键ID")
    related_object_type: Optional[str] = Field(None, description="关联对象类型")
    related_object_id: Optional[int] = Field(None, description="关联对象ID")
    uploader_user_id: int = Field(..., description="上传者用户ID")
    filename: str = Field(..., description="原始文件名")
    stored_filename: str = Field(..., description="存储在服务器上的文件名")
    storage_type: str = Field(..., description="存储类型 (例如 'local', 's3')")
    path_or_url: str = Field(..., description="文件路径或可访问URL")
    file_type: Optional[str] = Field(None, description="文件MIME类型")
    file_size_bytes: int = Field(..., description="文件大小 (字节)")
    created_at: datetime = Field(..., description="上传时间")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 123,
                "related_object_type": "ticket_reply",
                "related_object_id": 45,
                "uploader_user_id": 1,
                "filename": "screenshot.png",
                "stored_filename": "uuid_abcdef123.png",
                "storage_type": "local",
                "path_or_url": "/static/uploads/ticket_reply/uuid_abcdef123.png",
                "file_type": "image/png",
                "file_size_bytes": 153600,
                "created_at": "2023-10-26T10:30:00Z",
            }
        }
