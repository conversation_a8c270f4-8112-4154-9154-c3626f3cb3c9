from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from app.db.models.license import (
    LicenseKeyStatus,
    LicenseSettlementStatus,
    LicenseDurationUnit,
)
from app.schemas.user import UserResponse  # 用于嵌套用户信息


# 中文注释：卡密基础信息 (用于登录响应等)
class LicenseInfoBase(BaseModel):
    key_string: str = Field(..., description="卡密字符串")
    license_type_code: str = Field(..., description="卡密类型代码")
    license_type_name: str = Field(..., description="卡密类型名称")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    status: Optional[str] = Field(
        None, description="卡密状态 (例如 'used', 'available')"
    )

    class Config:
        from_attributes = True


# 中文注释：GET /me/license 接口响应中的当前激活卡密信息
class ActiveLicenseInfo(LicenseInfoBase):
    activated_at: Optional[datetime] = Field(None, description="激活时间")


# 中文注释：GET /me/license 接口响应中的历史卡密信息
class HistoricalLicenseInfo(LicenseInfoBase):
    activated_at: Optional[datetime] = Field(None, description="激活时间")
    # status 字段已在 LicenseInfoBase 中


# 中文注释：GET /me/license 接口的完整响应模型
class UserLicenseDetailsResponse(BaseModel):
    active_license: Optional[ActiveLicenseInfo] = Field(
        None, description="当前激活的卡密"
    )
    history: List[HistoricalLicenseInfo] = Field([], description="历史激活卡密列表")


# 中文注释：用户激活/续期卡密的请求体
class UserLicenseActivateRequest(BaseModel):
    key_string: str = Field(..., description="要激活或续期的卡密字符串")


# 中文注释：用户激活/续期卡密的响应体
class UserLicenseActivateResponse(BaseModel):
    message: str = Field(..., description="操作结果消息")
    license_info: LicenseInfoBase = Field(..., description="更新后的卡密信息")


# 中文注释：用于 /api/v1/auth/activate-license-account 接口响应中的卡密信息
class ActivateLicenseAccountLicenseInfo(BaseModel):
    key_string: str = Field(..., description="卡密字符串")
    license_type_code: str = Field(..., description="卡密类型代码")
    license_type_name: str = Field(..., description="卡密类型名称")
    expires_at: Optional[datetime] = Field(None, description="卡密过期时间")

    class Config:
        from_attributes = True


# --- Admin Schemas ---


# 中文注释：管理员创建卡密类型的基础 Schema
class LicenseTypeBaseAdmin(BaseModel):
    name: str = Field(..., description="类型名称 (供后台管理识别)")
    code: str = Field(..., description="类型代码 (程序内部和API使用)")
    description: Optional[str] = Field(None, description="类型描述")
    duration_value: int = Field(..., description="时长数值")
    duration_unit: LicenseDurationUnit = Field(..., description="时长单位")
    max_concurrent_devices: int = Field(1, description="该类型允许的最大同时在线设备数")
    price: Optional[float] = Field(None, description="建议售价 (供参考)")
    reseller_cost: Optional[float] = Field(None, description="代理拿货成本 (供参考)")
    is_active: bool = Field(True, description="是否启用该类型")
    is_publicly_available: bool = Field(True, description="是否允许代理/用户获取")


# 中文注释：管理员创建卡密类型 Schema
class LicenseTypeCreateAdmin(LicenseTypeBaseAdmin):
    pass


# 中文注释：管理员更新卡密类型 Schema
class LicenseTypeUpdateAdmin(BaseModel):
    name: Optional[str] = Field(None, description="类型名称")
    description: Optional[str] = Field(None, description="类型描述")
    duration_value: Optional[int] = Field(None, description="时长数值")
    duration_unit: Optional[LicenseDurationUnit] = Field(None, description="时长单位")
    max_concurrent_devices: Optional[int] = Field(
        None, description="最大同时在线设备数"
    )
    price: Optional[float] = Field(None, description="建议售价")
    reseller_cost: Optional[float] = Field(None, description="代理拿货成本")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_publicly_available: Optional[bool] = Field(None, description="是否公开可用")


# 中文注释：卡密类型响应 Schema (管理员视角)
class LicenseTypeResponseAdmin(LicenseTypeBaseAdmin):
    id: int = Field(..., description="类型唯一标识符")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# 中文注释：管理员批量生成卡密的请求体
class AdminLicenseKeyBatchCreate(BaseModel):
    license_type_id: int = Field(..., description="卡密类型ID")
    quantity: int = Field(
        ..., gt=0, le=1000, description="生成数量 (例如 1-1000)"
    )  # 限制一次生成的数量
    consignment_reseller_id: Optional[int] = Field(
        None, description="可选：分配给某个代理商的用户ID"
    )
    notes: Optional[str] = Field(None, description="备注信息 (例如，用于某活动)")
    # batch_id 将在后端自动生成


# 中文注释：管理员创建单个卡密的请求体 (如果需要的话)
class AdminLicenseKeyCreate(BaseModel):
    key_string: Optional[str] = Field(
        None, description="卡密字符串 (不填则自动生成)"
    )  # 允许管理员指定或自动生成
    license_type_id: int = Field(..., description="卡密类型ID")
    status: LicenseKeyStatus = Field(LicenseKeyStatus.available, description="卡密状态")
    user_id: Optional[int] = Field(
        None, description="使用者用户ID (如果直接分配给用户)"
    )
    activated_at: Optional[datetime] = Field(
        None, description="激活时间 (如果直接创建已激活卡)"
    )
    expires_at: Optional[datetime] = Field(
        None, description="过期时间 (如果直接创建已激活卡)"
    )
    issued_by_user_id: Optional[int] = Field(
        None, description="发放者用户ID (通常是管理员自己)"
    )
    consignment_reseller_id: Optional[int] = Field(
        None, description="持有此寄售卡密的代理ID"
    )
    settlement_status: LicenseSettlementStatus = Field(
        LicenseSettlementStatus.not_applicable, description="结算状态"
    )
    batch_id: Optional[str] = Field(None, description="批次号")
    notes: Optional[str] = Field(None, description="备注信息")


# 中文注释：管理员更新卡密信息的请求体
class AdminLicenseKeyUpdate(BaseModel):
    license_type_id: Optional[int] = Field(None, description="新的卡密类型ID")
    status: Optional[LicenseKeyStatus] = Field(None, description="新的卡密状态")
    user_id: Optional[int] = Field(
        None, description="新的使用者用户ID (允许置空以解绑)"
    )
    # activated_at: Optional[datetime] = Field(None, description="新的激活时间") # 通常不应随意修改
    expires_at: Optional[datetime] = Field(
        None, description="新的过期时间 (例如延长有效期)"
    )
    consignment_reseller_id: Optional[int] = Field(
        None, description="新的寄售代理ID (允许置空)"
    )
    settlement_status: Optional[LicenseSettlementStatus] = Field(
        None, description="新的结算状态"
    )
    notes: Optional[str] = Field(None, description="新的备注信息")
    # key_string 通常不允许修改


# 中文注释：卡密基础 Schema (用于其他 Schema 嵌套)
class LicenseKeyBase(BaseModel):
    id: int = Field(..., description="卡密ID")
    key_string: str = Field(..., description="卡密字符串")
    status: LicenseKeyStatus = Field(..., description="卡密状态")
    activated_at: Optional[datetime] = Field(None, description="激活时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    batch_id: Optional[str] = Field(None, description="批次号")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# 中文注释：卡密响应 Schema (管理员视角，包含更详细信息)
class LicenseKeyResponseAdmin(LicenseKeyBase):
    license_type: LicenseTypeResponseAdmin = Field(
        ..., description="卡密类型详情"
    )  # 嵌套类型信息
    user: Optional[UserResponse] = Field(None, description="使用者信息")  # 嵌套用户信息
    issuer: Optional[UserResponse] = Field(None, description="发放者信息")
    consignment_reseller: Optional[UserResponse] = Field(
        None, description="寄售代理信息"
    )
    settlement_status: LicenseSettlementStatus = Field(..., description="结算状态")
    renewal_source_key_id: Optional[int] = Field(None, description="续期来源卡密ID")


# 中文注释：用于替代 pydantic-sqlalchemy 生成的 LicenseKeyCreate，以适应业务逻辑
class LicenseKeyCreate(BaseModel):
    key_string: str
    license_type_id: int
    status: Optional[LicenseKeyStatus] = LicenseKeyStatus.available
    user_id: Optional[int] = None
    activated_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    issued_by_user_id: Optional[int] = None
    consignment_reseller_id: Optional[int] = None
    settlement_status: Optional[
        LicenseSettlementStatus
    ] = LicenseSettlementStatus.not_applicable
    batch_id: Optional[str] = None
    notes: Optional[str] = None


# 中文注释：用于替代 pydantic-sqlalchemy 生成的 LicenseKeyUpdate，以适应业务逻辑
class LicenseKeyUpdate(BaseModel):
    key_string: Optional[str] = None
    license_type_id: Optional[int] = None
    status: Optional[LicenseKeyStatus] = None
    user_id: Optional[int] = None
    activated_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    issued_by_user_id: Optional[int] = None
    consignment_reseller_id: Optional[int] = None
    settlement_status: Optional[LicenseSettlementStatus] = None
    batch_id: Optional[str] = None
    notes: Optional[str] = None
