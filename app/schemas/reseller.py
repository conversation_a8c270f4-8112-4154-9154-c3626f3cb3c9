# TanZouDaShiAPI/app/schemas/reseller.py
from datetime import datetime
from typing import Optional, List
from decimal import Decimal

from pydantic import BaseModel, Field

# 从 models 中导入 Enum 类型，以便在 Schema 中复用
from app.db.models.user import ResellerStatus as ResellerStatusEnum
from app.db.models.user import (
    UserRole as UserRoleEnum,
)  # 如果需要嵌套显示用户信息中的role

# --- ResellerLevel Schemas ---


class ResellerLevelBase(BaseModel):
    level_value: int = Field(..., description="级别数值 (例如 1, 2, 3)")
    name: str = Field(..., max_length=100, description="级别名称")
    description: Optional[str] = Field(None, description="级别描述")
    upgrade_threshold_sales: Optional[Decimal] = Field(
        None, description="升级到此级别的累计销售额门槛"
    )
    upgrade_threshold_sub_resellers: Optional[int] = Field(
        None, description="升级到此级别的直接下级代理数量门槛"
    )
    default_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="此级别的默认直接销售佣金比例 (0.0000 - 1.0000)"
    )
    default_sub_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="此级别的默认间接（下级）佣金比例"
    )
    default_second_tier_sub_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="默认二级间接佣金率 (来自间接下两级的销售)"
    )
    is_active: bool = Field(True, description="是否启用此级别")


class ResellerLevelCreate(ResellerLevelBase):
    pass


class ResellerLevelUpdate(BaseModel):
    level_value: Optional[int] = Field(None, description="级别数值")
    name: Optional[str] = Field(None, max_length=100, description="级别名称")
    description: Optional[str] = Field(None, description="级别描述")
    upgrade_threshold_sales: Optional[Decimal] = Field(
        None, description="升级销售额门槛"
    )
    upgrade_threshold_sub_resellers: Optional[int] = Field(
        None, description="升级下级代理数门槛"
    )
    default_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="默认直接佣金率"
    )
    default_sub_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="默认间接佣金率"
    )
    default_second_tier_sub_commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="默认二级间接佣金率"
    )
    is_active: Optional[bool] = Field(None, description="是否启用")


class ResellerLevelResponse(ResellerLevelBase):
    id: int = Field(..., description="级别唯一标识符")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# --- Reseller Schemas ---


# 嵌套的用户基本信息 Schema (如果需要在代理信息中展示)
class UserBaseInfoForReseller(BaseModel):
    id: int
    username: str
    nickname: Optional[str] = None
    email: Optional[str] = None
    role: UserRoleEnum  # 使用 models 中的 Enum

    class Config:
        from_attributes = True


class ResellerBase(BaseModel):
    # user_id 在创建时通常从路径或认证信息中获取，更新时也是
    parent_reseller_user_id: Optional[int] = Field(None, description="上级代理的用户ID")
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="特定用户的直接销售佣金比例 (覆盖级别默认值)"
    )
    balance: Decimal = Field(Decimal("0.00"), description="代理账户余额/可提现额度")
    reseller_level_value: Optional[int] = Field(
        None, description="代理级别数值 (关联到 reseller_levels.level_value)"
    )
    consignment_limit_count: int = Field(
        0, description="允许持有的最大寄售卡密数量 (0表示不允许)"
    )
    consignment_limit_value: Decimal = Field(
        Decimal("0.00"), description="允许持有的最大寄售卡密总价值 (0表示不允许)"
    )
    status: ResellerStatusEnum = Field(
        ResellerStatusEnum.active, description="代理资格状态"
    )
    total_sales_amount: Decimal = Field(
        Decimal("0.00"), description="累计销售总额（已结算部分）"
    )
    total_commission_earned: Decimal = Field(
        Decimal("0.00"), description="累计获得佣金总额"
    )
    remarks: Optional[str] = Field(None, description="代理备注信息")


class ResellerCreate(ResellerBase):
    user_id: int  # 创建时必须指定关联的用户ID


class ResellerUpdate(BaseModel):
    parent_reseller_user_id: Optional[int] = Field(
        None, description="上级代理的用户ID (通常由管理员调整)"
    )
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="特定销售佣金比例"
    )
    balance: Optional[Decimal] = Field(
        None, description="代理账户余额 (通常由系统或管理员调整)"
    )
    reseller_level_value: Optional[int] = Field(None, description="代理级别数值")
    consignment_limit_count: Optional[int] = Field(None, description="最大寄售卡密数量")
    consignment_limit_value: Optional[Decimal] = Field(
        None, description="最大寄售卡密总价值"
    )
    status: Optional[ResellerStatusEnum] = Field(None, description="代理资格状态")
    # total_sales_amount 和 total_commission_earned 通常由系统计算，不直接通过API更新
    remarks: Optional[str] = Field(None, description="代理备注信息")


class ResellerInfoResponse(ResellerBase):
    user_id: int = Field(..., description="关联的用户ID")
    user: Optional[UserBaseInfoForReseller] = Field(
        None, description="关联的用户基本信息"
    )  # 嵌套用户基本信息
    level: Optional[ResellerLevelResponse] = Field(
        None, description="代理级别详细信息"
    )  # 嵌套级别信息
    parent_reseller_username: Optional[str] = Field(
        None, description="上级代理用户名 (如果存在)"
    )  # 辅助字段，方便前端显示
    # 可以在这里添加更多统计信息，例如下线数量等，但这通常需要后端额外计算
    # direct_subordinate_count: Optional[int] = Field(None, description="直接下级代理数量")
    created_at: datetime = Field(..., description="成为代理的时间")
    updated_at: datetime = Field(..., description="代理信息更新时间")

    class Config:
        from_attributes = True


class ResellerProfileUpdateRequest(BaseModel):  # 代理更新自己信息时用
    # 假设代理只能更新部分非敏感信息，例如提现账户 (这里没有提现账户字段，可以加到 Reseller 模型)
    # 如果提现账户信息在 Reseller 模型中，可以在这里添加
    # withdrawal_payment_method: Optional[str] = Field(None, description="提现方式")
    # withdrawal_payment_account: Optional[str] = Field(None, description="提现账户")
    remarks: Optional[str] = Field(None, description="更新备注信息")
    # 其他可由代理自己更新的字段


class ResellerDashboardResponse(BaseModel):
    user_id: int
    username: str
    current_balance: Decimal = Field(..., description="当前账户余额")
    total_sales_amount: Decimal = Field(..., description="累计销售总额")
    total_commission_earned: Decimal = Field(..., description="累计获得佣金总额")
    pending_commission_amount: Optional[Decimal] = Field(
        None, description="待结算佣金总额"
    )  # 需要后端计算
    direct_subordinate_count: Optional[int] = Field(
        None, description="直接下级代理数量"
    )  # 需要后端计算
    total_license_sold_count: Optional[int] = Field(
        None, description="累计售出卡密数量"
    )  # 需要后端计算
    reseller_level_name: Optional[str] = Field(None, description="当前代理级别名称")

    class Config:
        from_attributes = True


# 用于管理员设置代理寄售额度的请求体
class ResellerConsignmentLimitsUpdateRequest(BaseModel):
    consignment_limit_count: int = Field(
        ..., ge=0, description="允许持有的最大寄售卡密数量"
    )
    consignment_limit_value: Decimal = Field(
        ..., ge=0, description="允许持有的最大寄售卡密总价值 (按拿货价计)"
    )


class ResellerConsignmentLimitsUpdateResponse(BaseModel):
    user_id: int
    consignment_limit_count: int
    consignment_limit_value: Decimal
    message: str = "代理寄售额度更新成功。"

    class Config:
        from_attributes = True


# 用于代理查看下级业绩的响应
class SubordinatePerformanceItem(BaseModel):
    sub_reseller_user_id: int
    sub_reseller_username: str
    sub_reseller_level_name: Optional[str] = None
    total_direct_sales_count_by_sub: int = 0
    total_direct_sales_value_by_sub: Decimal = Field(Decimal("0.00"))
    total_commission_contributed_to_me: Decimal = Field(Decimal("0.00"))
    period_start_date: Optional[str] = None  # YYYY-MM-DD
    period_end_date: Optional[str] = None  # YYYY-MM-DD

    class Config:
        from_attributes = True


class SubordinatePerformanceSummary(BaseModel):
    total_direct_subordinates_count: int = 0
    overall_commission_from_direct_subordinates: Decimal = Field(Decimal("0.00"))
    overall_sales_count_by_direct_subordinates: int = 0
    overall_sales_value_by_direct_subordinates: Decimal = Field(Decimal("0.00"))
    period_start_date: Optional[str] = None  # YYYY-MM-DD
    period_end_date: Optional[str] = None  # YYYY-MM-DD


class SubordinatePerformanceResponse(BaseModel):
    data: List[SubordinatePerformanceItem]
    # pagination: Pagination # 假设有一个通用的分页Schema
    summary_for_me: SubordinatePerformanceSummary

    class Config:
        from_attributes = True


# 管理员创建代理时的请求体
class AdminResellerCreateRequest(BaseModel):
    user_id: int = Field(..., description="要设置为代理的用户ID")
    parent_reseller_user_id: Optional[int] = Field(None, description="上级代理的用户ID")
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="特定用户的直接销售佣金比例 (覆盖级别默认值)"
    )
    reseller_level_value: Optional[int] = Field(
        None, description="代理级别数值 (关联到 reseller_levels.level_value)"
    )
    consignment_limit_count: int = Field(
        0, ge=0, description="允许持有的最大寄售卡密数量"
    )
    consignment_limit_value: Decimal = Field(
        Decimal("0.00"), ge=0, description="允许持有的最大寄售卡密总价值"
    )
    status: ResellerStatusEnum = Field(
        ResellerStatusEnum.active, description="代理资格状态"
    )
    remarks: Optional[str] = Field(None, description="代理备注信息")


# 管理员更新代理时的请求体
class AdminResellerUpdateRequest(BaseModel):
    parent_reseller_user_id: Optional[int] = Field(None, description="上级代理的用户ID")
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=1, description="特定佣金比例"
    )
    reseller_level_value: Optional[int] = Field(None, description="代理级别数值")
    # balance 字段不应在这里直接修改，应通过特定接口调整并记录
    consignment_limit_count: Optional[int] = Field(
        None, ge=0, description="最大寄售卡密数量"
    )
    consignment_limit_value: Optional[Decimal] = Field(
        None, ge=0, description="最大寄售卡密总价值"
    )
    status: Optional[ResellerStatusEnum] = Field(None, description="代理资格状态")
    remarks: Optional[str] = Field(None, description="代理备注信息")


# Admin or system updating reseller status
class ResellerStatusUpdate(BaseModel):
    status: ResellerStatusEnum = Field(..., description="新的代理账户状态")
