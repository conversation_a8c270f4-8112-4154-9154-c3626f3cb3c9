# TanZouDaShiAPI/app/schemas/log.py
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


# Base schema for common fields
class SessionLogBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    device_id: Optional[str] = Field(None, max_length=128, description="设备ID")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP地址")


# Schema for creating a new session log
# Although crud_session_log.create_user_session_log doesn't take this schema as direct input,
# the CRUDBase generic type requires it.
class SessionLogCreate(SessionLogBase):
    # Inherits user_id, device_id, ip_address
    # session_start_at is typically set by the database default or server logic
    pass


# Schema for updating a session log (specifically for ending a session)
# Used by crud_session_log.end_user_session_log
class SessionLogUpdate(BaseModel):
    session_end_at: Optional[datetime] = Field(None, description="会话结束时间")
    duration_seconds: Optional[int] = Field(None, description="会话时长（秒）")


# Schema representing a session log entry in API responses
class SessionLog(SessionLogBase):
    id: int = Field(..., description="会话日志ID")
    session_start_at: datetime = Field(..., description="会话开始时间")
    session_end_at: Optional[datetime] = Field(None, description="会话结束时间")
    duration_seconds: Optional[int] = Field(None, description="会话时长（秒）")
    created_at: datetime = Field(..., description="记录创建时间")
    updated_at: datetime = Field(..., description="记录更新时间")

    class Config:
        from_attributes = (
            True  # Use from_attributes instead of orm_mode for Pydantic v2
        )


# 用户会话日志响应（包含用户信息）
class UserSessionLogResponse(BaseModel):
    id: int = Field(..., description="会话日志ID")
    user_id: int = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    device_id: Optional[str] = Field(None, description="设备ID")
    ip_address: Optional[str] = Field(None, description="IP地址")
    session_start_at: datetime = Field(..., description="会话开始时间")
    session_end_at: Optional[datetime] = Field(None, description="会话结束时间")
    duration_seconds: Optional[int] = Field(None, description="会话时长（秒）")
    created_at: datetime = Field(..., description="记录创建时间")
    updated_at: datetime = Field(..., description="记录更新时间")

    class Config:
        from_attributes = True


# 用户会话日志列表响应
class UserSessionLogListResponse(BaseModel):
    logs: List[UserSessionLogResponse] = Field(..., description="会话日志列表")
    total_count: int = Field(..., description="总记录数")


# 系统日志基础模型
class SystemLogBase(BaseModel):
    level: str = Field(..., description="日志级别")
    message: str = Field(..., description="日志消息")
    module: Optional[str] = Field(None, description="模块名称")
    user_id: Optional[int] = Field(None, description="相关用户ID")
    ip_address: Optional[str] = Field(None, description="IP地址")


# 系统日志响应
class SystemLogResponse(SystemLogBase):
    id: int = Field(..., description="日志ID")
    timestamp: datetime = Field(..., description="时间戳")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

    class Config:
        from_attributes = True


# 系统日志列表响应
class SystemLogListResponse(BaseModel):
    logs: List[SystemLogResponse] = Field(..., description="系统日志列表")
    total_count: int = Field(..., description="总记录数")


# 审计日志基础模型
class AuditLogBase(BaseModel):
    user_id: int = Field(..., description="用户ID")
    action: str = Field(..., description="操作类型")
    resource: str = Field(..., description="资源类型")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")


# 审计日志响应
class AuditLogResponse(AuditLogBase):
    id: int = Field(..., description="审计日志ID")
    timestamp: datetime = Field(..., description="时间戳")
    username: Optional[str] = Field(None, description="用户名")
    details: Optional[Dict[str, Any]] = Field(None, description="操作详情")

    class Config:
        from_attributes = True


# 审计日志列表响应
class AuditLogListResponse(BaseModel):
    logs: List[AuditLogResponse] = Field(..., description="审计日志列表")
    total_count: int = Field(..., description="总记录数")
