# TanZouDaShiAPI/app/schemas/score.py
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl, field_validator
from datetime import datetime

# 从模型文件中导入 Enum 类型
from app.db.models.score import ScoreType, MidiStorageType, ScoreDifficulty, ScoreStatus

# 引用其他模块的 schema
# 确保 UserResponse 和 CategoryResponse (或其简化版) 可以在此被正确引用
# 实际项目中，需要确保这些 schema 文件存在且已定义相关类
from .category import CategoryNestedResponse  # 使用之前定义的嵌套版本


# 为 UserResponse 创建一个更简洁的版本，用于嵌套在 ScoreResponse 中
class UserNestedResponse(BaseModel):
    id: int
    username: str
    nickname: Optional[str] = None
    avatar_url: Optional[HttpUrl] = None

    class Config:
        from_attributes = True


# 谱曲基础模型
class ScoreBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="谱曲标题")
    description: Optional[str] = Field(None, description="谱曲描述")
    category_id: int = Field(..., description="所属分类ID")

    type: ScoreType = Field(..., description="谱曲类型")
    txt_content: Optional[str] = Field(None, description="TXT谱曲内容")
    midi_storage_type: Optional[MidiStorageType] = Field(
        None, description="MIDI文件存储类型"
    )
    midi_path_or_url: Optional[str] = Field(
        None, max_length=512, description="MIDI文件路径或URL"
    )
    original_midi_filename: Optional[str] = Field(
        None, max_length=255, description="原始MIDI文件名"
    )
    external_url: Optional[HttpUrl] = Field(None, description="外部谱曲链接")

    difficulty: Optional[ScoreDifficulty] = Field(None, description="难度等级")
    tags: Optional[List[str]] = Field(
        None, description='标签列表, 例如 ["pop", "piano solo"]'
    )  # 接受列表
    cover_image_url: Optional[HttpUrl] = Field(None, description="谱曲封面图片URL")
    duration_seconds: Optional[int] = Field(
        None, ge=0, description="曲谱预计演奏时长 (秒)"
    )
    is_premium_only: bool = Field(False, description="是否仅限付费用户")
    status: ScoreStatus = Field(default=ScoreStatus.pending, description="谱曲状态")

    # API 设计文档中提及 "score_data 可能是复杂对象"，但模型中未明确定义
    # score_data: Optional[Dict[str, Any]] = Field(None, description="复杂谱曲数据对象")


# 创建谱曲时使用的模型
class ScoreCreate(ScoreBase):
    uploader_user_id: int = Field(
        ..., description="上传者用户ID (将从当前认证用户获取)"
    )

    @field_validator("tags", mode="before")
    @classmethod
    def parse_tags(cls, v):
        if isinstance(v, str):
            # 如果是逗号分隔的字符串，则转换为列表
            return [tag.strip() for tag in v.split(",") if tag.strip()]
        if isinstance(v, list):
            return [str(tag).strip() for tag in v if str(tag).strip()]
        return v


# 更新谱曲时使用的模型
class ScoreUpdate(BaseModel):
    title: Optional[str] = Field(
        None, min_length=1, max_length=255, description="谱曲标题"
    )
    description: Optional[str] = Field(None, description="谱曲描述")
    category_id: Optional[int] = Field(None, description="所属分类ID")

    type: Optional[ScoreType] = Field(None, description="谱曲类型")
    txt_content: Optional[str] = Field(None, description="TXT谱曲内容")
    midi_storage_type: Optional[MidiStorageType] = Field(
        None, description="MIDI文件存储类型"
    )
    midi_path_or_url: Optional[str] = Field(
        None, max_length=512, description="MIDI文件路径或URL"
    )
    original_midi_filename: Optional[str] = Field(
        None, max_length=255, description="原始MIDI文件名"
    )
    external_url: Optional[HttpUrl] = Field(None, description="外部谱曲链接")

    difficulty: Optional[ScoreDifficulty] = Field(None, description="难度等级")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    cover_image_url: Optional[HttpUrl] = Field(None, description="谱曲封面图片URL")
    duration_seconds: Optional[int] = Field(
        None, ge=0, description="曲谱预计演奏时长 (秒)"
    )
    is_premium_only: Optional[bool] = Field(None, description="是否仅限付费用户")
    status: Optional[ScoreStatus] = Field(None, description="谱曲状态 (管理员可修改)")
    # score_data: Optional[Dict[str, Any]] = Field(None, description="复杂谱曲数据对象")

    @field_validator("tags", mode="before")
    @classmethod
    def parse_update_tags(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            return [tag.strip() for tag in v.split(",") if tag.strip()]
        if isinstance(v, list):
            return [str(tag).strip() for tag in v if str(tag).strip()]
        return v


# 用于API响应的谱曲信息模型
class ScoreResponse(ScoreBase):
    id: int = Field(..., description="谱曲唯一标识符")
    uploader_user_id: int = Field(..., description="上传者用户ID")

    view_count: int = Field(default=0, description="查看次数")
    download_count: int = Field(default=0, description="下载次数")
    favorite_count: int = Field(default=0, description="收藏次数")
    comment_count: int = Field(default=0, description="评论数量")
    # SQLAlchemy DECIMAL(3,2) 对应 Pydantic 的 condecimal
    average_rating: float = Field(
        default=0.00, description="平均评分 (0.00 - 5.00)", ge=0.0, le=5.0
    )
    rating_count: int = Field(default=0, description="评分次数")

    approved_by_user_id: Optional[int] = Field(None, description="审核操作的管理员ID")
    approved_at: Optional[datetime] = Field(None, description="审核通过/拒绝时间")
    approval_remarks: Optional[str] = Field(None, description="审核备注")  # 新增字段

    created_at: datetime = Field(..., description="谱曲上传时间")
    updated_at: datetime = Field(..., description="谱曲信息更新时间")

    # 嵌套关联对象信息
    uploader: Optional[UserNestedResponse] = Field(None, description="上传者用户信息")
    approver: Optional[UserNestedResponse] = Field(None, description="审核者用户信息")
    category: Optional[CategoryNestedResponse] = Field(None, description="所属分类信息")

    # 根据 API 设计文档，列表接口通常返回部分信息，详情接口返回完整信息
    # 这里定义的 ScoreResponse 应该是比较完整的版本

    class Config:
        from_attributes = True


# 用于列表展示的谱曲简要信息
class ScoreSummaryResponse(BaseModel):
    id: int
    title: str
    description_snippet: Optional[str] = None  # 可以通过 validator 从 description 生成
    uploader_info: Optional[UserNestedResponse] = Field(
        None, alias="uploader"
    )  # 使用 alias 匹配 ORM 属性
    # category_name: Optional[str] = Field(None, path="category.name") # 假设可以从 category.name 获取
    # category_slug: Optional[str] = Field(None, path="category.slug")
    type: ScoreType
    difficulty: Optional[ScoreDifficulty] = None
    view_count: int = 0
    favorite_count: int = 0
    average_rating: float = 0.00
    comment_count: int = 0
    cover_image_url: Optional[HttpUrl] = None
    is_premium_only: bool = False
    created_at: datetime
    tags: Optional[List[str]] = None

    # description_snippet 将在业务逻辑中生成，这里不使用 validator

    class Config:
        from_attributes = True
        # populate_by_name 替代了 allow_population_by_field_name
        populate_by_name = True


# 用于谱曲详情接口的响应，可能包含更多信息，如评论列表等（此处暂不嵌套评论）
class ScoreDetailResponse(ScoreResponse):
    # comments: Optional[List["ScoreCommentResponse"]] = Field(None, description="评论列表") # 稍后定义 ScoreCommentResponse
    pass


# 管理员更新谱曲时使用的模型
class AdminScoreUpdate(ScoreUpdate):  # 继承自 ScoreUpdate
    # 管理员可以修改 ScoreUpdate 中的所有字段，此外还可以修改：
    status: Optional[ScoreStatus] = Field(None, description="谱曲状态 (管理员可修改)")
    # approved_by_user_id: Optional[int] = Field(None, description="审核操作的管理员ID (通常在审核接口中处理)")
    # approval_remarks: Optional[str] = Field(None, description="审核备注 (通常在审核接口中处理)")
    # 注意：approved_by_user_id 和 approval_remarks 通常通过专门的审核接口（如 PATCH /status）更新，
    # 而不是在通用的 PUT 更新接口中。但如果需求允许，也可以在这里添加。
    # 为了与 PATCH /status 接口职责分开，这里暂时不直接暴露 approved_by_user_id 和 approval_remarks。
    # 管理员可以通过 status 字段直接更改状态，但审核人和备注的记录应由 PATCH /status 接口负责。
    # 如果 PUT 接口也需要直接设置这些，可以将上面两行取消注释。
    pass


# 新增分页相关的 Schema
class PageMeta(BaseModel):
    page: int = Field(..., description="当前页码", examples=[1, 2, 3])
    limit: int = Field(..., description="每页数量", examples=[10, 20, 50])
    total_items: int = Field(..., description="总项目数", examples=[100, 250, 1000])
    total_pages: int = Field(..., description="总页数", examples=[5, 13, 50])

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "page": 1,
                    "limit": 20,
                    "total_items": 100,
                    "total_pages": 5
                }
            ]
        }


class ScorePaginatedResponse(BaseModel):
    meta: PageMeta = Field(..., description="分页元数据")
    data: List[ScoreSummaryResponse] = Field(..., description="谱曲摘要列表")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "examples": [
                {
                    "meta": {
                        "page": 1,
                        "limit": 20,
                        "total_items": 100,
                        "total_pages": 5
                    },
                    "data": [
                        {
                            "id": 1,
                            "title": "卡农",
                            "description_snippet": "经典古典音乐作品，适合钢琴演奏...",
                            "uploader_info": {
                                "id": 1,
                                "username": "musiclover",
                                "nickname": "音乐爱好者",
                                "avatar_url": "https://example.com/avatar1.jpg"
                            },
                            "type": "txt",
                            "difficulty": "intermediate",
                            "view_count": 1250,
                            "favorite_count": 89,
                            "average_rating": 4.5,
                            "comment_count": 23,
                            "cover_image_url": "https://example.com/cover1.jpg",
                            "is_premium_only": False,
                            "created_at": "2024-01-01T10:00:00Z",
                            "tags": ["古典", "钢琴", "经典"]
                        },
                        {
                            "id": 2,
                            "title": "月光奏鸣曲",
                            "description_snippet": "贝多芬的经典作品，第一乐章...",
                            "uploader_info": {
                                "id": 2,
                                "username": "pianist",
                                "nickname": "钢琴家",
                                "avatar_url": "https://example.com/avatar2.jpg"
                            },
                            "type": "midi",
                            "difficulty": "advanced",
                            "view_count": 2100,
                            "favorite_count": 156,
                            "average_rating": 4.8,
                            "comment_count": 45,
                            "cover_image_url": "https://example.com/cover2.jpg",
                            "is_premium_only": True,
                            "created_at": "2024-01-02T14:30:00Z",
                            "tags": ["古典", "贝多芬", "奏鸣曲"]
                        }
                    ]
                }
            ]
        }


# 用于更新谱曲状态的 Schema
class ScoreStatusUpdate(BaseModel):
    status: ScoreStatus = Field(..., description="新的谱曲状态")
    approval_remarks: Optional[str] = Field(
        None, description="审核备注 (例如拒绝原因或通过说明)"
    )


# 用户的谱曲演奏尝试记录响应模型
# class UserScoreAttemptResponse(BaseModel):
#     id: int = Field(..., description="记录ID")
#     user_id: int = Field(..., description="用户ID")
#     score_id: int = Field(..., description="谱曲ID")
#     score: Optional[ScoreSummaryResponse] = Field(None, description="谱曲信息")
#     max_combo: int = Field(..., description="最大连击数")
#     accuracy: condecimal(max_digits=5, decimal_places=2) = Field(..., description="准确率百分比")
#     total_notes: int = Field(..., description="总音符数")
#     hit_notes: int = Field(..., description="击中的音符数")
#     miss_notes: int = Field(..., description="未击中的音符数")
#     score_points: int = Field(..., description="得分")
#     performance_rating: condecimal(max_digits=3, decimal_places=2) = Field(..., description="表现评级")
#     completed: bool = Field(..., description="是否完成演奏")
#     created_at: datetime = Field(..., description="创建时间")
#
#     class Config:
#         from_attributes = True
