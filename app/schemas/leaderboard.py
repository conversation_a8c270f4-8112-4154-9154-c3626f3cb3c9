# TanZouDaShiAPI/app/schemas/leaderboard.py
from pydantic import BaseModel, Field
from typing import Optional

# 假设 UserNestedResponse 已经存在于 user.py 中，或者我们在这里定义一个简化的
# 为了减少跨文件依赖的复杂性，这里暂时定义一个简单的 User 信息
# 更好的做法是从 app.schemas.user 导入 UserNestedResponse
# from .user import UserNestedResponse # 理想情况


class MinimalUserResponse(BaseModel):
    """排行榜中使用的最简用户信息"""

    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="头像链接")

    class Config:
        from_attributes = True


class UserLeaderboardEntry(BaseModel):
    """用户排行榜条目"""

    user: MinimalUserResponse = Field(..., description="用户信息")
    upload_count: int = Field(..., description="已审核通过的谱曲上传数量")

    class Config:
        from_attributes = True


class ScoreLeaderboardEntry(BaseModel):
    """谱曲排行榜条目 (如果需要不同于 ScoreResponse 的结构)"""

    # 当前任务要求返回 List[schemas.ScoreResponse]，所以这个可能暂时不需要
    # id: int
    # title: str
    # rank: int
    # value: Union[int, float] # 例如 view_count, average_rating
    # uploader_info: Optional[MinimalUserResponse] = None
    # cover_image_url: Optional[HttpUrl] = None
    pass
