from typing import Optional
from pydantic import BaseModel, Field


# 中文注释：基础Token模型，用于JWT令牌
class TokenBase(BaseModel):
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: int = Field(..., description="访问令牌有效期（秒）")


# 中文注释：包含刷新令牌的Token模型
class Token(TokenBase):
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    session_token: Optional[str] = Field(
        None, description="会话令牌，用于心跳和特定会话登出"
    )


# 中文注释：令牌载荷数据，用于解码JWT令牌
class TokenPayload(BaseModel):
    sub: Optional[int] = Field(None, description="主题，通常是用户ID")
    exp: Optional[int] = Field(None, description="过期时间戳")
    # 可以根据需要添加其他自定义声明
    username: Optional[str] = Field(None, description="用户名")
    scopes: list[str] = Field([], description="权限范围")


# 中文注释：刷新令牌请求体
class RefreshTokenRequest(BaseModel):
    refresh_token: str = Field(..., description="有效的刷新令牌")


# 中文注释：刷新令牌响应体 (仅包含新的 access_token)
class RefreshTokenResponse(BaseModel):
    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: int = Field(..., description="新的访问令牌有效期（秒）")
