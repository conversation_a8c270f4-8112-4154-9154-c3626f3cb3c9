from typing import Optional, TypeVar, Generic
from pydantic import BaseModel, Field

DataT = TypeVar("DataT")


# 中文注释：通用消息响应模型
class Msg(BaseModel):
    message: str = Field(
        ...,
        description="操作结果的文本消息",
        examples=["操作成功", "用户创建成功", "数据删除成功"]
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "message": "操作成功"
                },
                {
                    "message": "用户注册成功"
                },
                {
                    "message": "谱曲删除成功"
                }
            ]
        }


# 中文注释：通用成功响应模型 (包含 data 字段)
class GenericResponse(BaseModel, Generic[DataT]):
    code: int = Field(
        200,
        description="业务状态码，默认为200表示成功",
        examples=[200, 201]
    )
    message: str = Field(
        "操作成功",
        description="响应消息",
        examples=["操作成功", "数据获取成功", "创建成功"]
    )
    data: Optional[DataT] = Field(
        None,
        description="响应数据体，包含具体的业务数据"
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "code": 200,
                    "message": "操作成功",
                    "data": {
                        "id": 1,
                        "name": "示例数据"
                    }
                }
            ]
        }


# 中文注释：通用失败响应模型 (data 通常为 null)
class ErrorResponse(BaseModel):
    code: int = Field(
        ...,
        description="自定义错误码",
        examples=[400, 401, 403, 404, 422, 500]
    )
    message: str = Field(
        ...,
        description="错误描述信息",
        examples=[
            "请求参数错误",
            "用户名或密码不正确",
            "权限不足",
            "资源不存在",
            "数据验证失败",
            "服务器内部错误"
        ]
    )
    data: Optional[None] = Field(
        None,
        description="错误响应时数据通常为null"
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "code": 400,
                    "message": "请求参数错误",
                    "data": None
                },
                {
                    "code": 401,
                    "message": "用户名或密码不正确",
                    "data": None
                },
                {
                    "code": 403,
                    "message": "权限不足，无法访问此资源",
                    "data": None
                },
                {
                    "code": 404,
                    "message": "请求的资源不存在",
                    "data": None
                },
                {
                    "code": 422,
                    "message": "数据验证失败",
                    "data": None
                },
                {
                    "code": 500,
                    "message": "服务器内部错误",
                    "data": None
                }
            ]
        }
