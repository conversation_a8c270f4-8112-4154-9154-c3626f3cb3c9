# TanZouDaShiAPI/app/schemas/category.py
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime


# 基础模型，包含所有 Category 模型共有的字段
class CategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    slug: str = Field(
        ..., min_length=1, max_length=100, description="URL友好型名称 (slug)"
    )
    parent_category_id: Optional[int] = Field(
        None, description="父分类ID (NULL表示顶级分类)"
    )
    description: Optional[str] = Field(None, description="分类描述")
    icon_url: Optional[HttpUrl] = Field(None, description="分类图标URL")
    sort_order: int = Field(default=0, description="排序字段 (数字越小越靠前)")
    is_active: bool = Field(default=True, description="是否启用")


# 创建分类时使用的模型
class CategoryCreate(CategoryBase):
    pass


# 更新分类时使用的模型，所有字段都是可选的
class CategoryUpdate(BaseModel):
    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="分类名称"
    )
    slug: Optional[str] = Field(
        None, min_length=1, max_length=100, description="URL友好型名称 (slug)"
    )
    parent_category_id: Optional[int] = Field(None, description="父分类ID")
    description: Optional[str] = Field(None, description="分类描述")
    icon_url: Optional[HttpUrl] = Field(None, description="分类图标URL")
    sort_order: Optional[int] = Field(None, description="排序字段")
    is_active: Optional[bool] = Field(None, description="是否启用")


# 用于从数据库读取或API响应的模型
class CategoryResponse(CategoryBase):
    id: int = Field(..., description="分类唯一标识符", examples=[1, 2, 3])
    created_at: datetime = Field(
        ...,
        description="分类创建时间",
        examples=["2024-01-01T10:00:00Z"]
    )
    updated_at: datetime = Field(
        ...,
        description="分类信息更新时间",
        examples=["2024-01-01T10:00:00Z"]
    )

    # 嵌套的子分类和父分类信息 (可选，根据API设计文档决定是否完全展开)
    # children: Optional[List["CategoryResponse"]] = Field(None, description="子分类列表") # 避免循环导入，如果需要，在具体使用时定义或使用 forward refs
    # parent_category: Optional["CategoryResponse"] = Field(None, description="父分类信息")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "examples": [
                {
                    "id": 1,
                    "name": "古典音乐",
                    "slug": "classical",
                    "parent_category_id": None,
                    "description": "古典音乐作品分类，包含巴洛克、古典主义、浪漫主义等时期的作品",
                    "icon_url": "https://example.com/icons/classical.png",
                    "sort_order": 1,
                    "is_active": True,
                    "created_at": "2024-01-01T10:00:00Z",
                    "updated_at": "2024-01-01T10:00:00Z"
                },
                {
                    "id": 2,
                    "name": "流行音乐",
                    "slug": "pop",
                    "parent_category_id": None,
                    "description": "现代流行音乐作品",
                    "icon_url": "https://example.com/icons/pop.png",
                    "sort_order": 2,
                    "is_active": True,
                    "created_at": "2024-01-01T11:00:00Z",
                    "updated_at": "2024-01-01T11:00:00Z"
                },
                {
                    "id": 3,
                    "name": "钢琴独奏",
                    "slug": "piano-solo",
                    "parent_category_id": 1,
                    "description": "古典钢琴独奏作品",
                    "icon_url": "https://example.com/icons/piano.png",
                    "sort_order": 1,
                    "is_active": True,
                    "created_at": "2024-01-01T12:00:00Z",
                    "updated_at": "2024-01-01T12:00:00Z"
                }
            ]
        }


# 为了支持嵌套，我们可以定义一个简化的 Category 嵌套响应模型
class CategoryNestedResponse(BaseModel):
    id: int
    name: str
    slug: str
    icon_url: Optional[HttpUrl] = None

    class Config:
        from_attributes = True


# 完整响应，包含嵌套的子分类和父分类
class CategoryDetailResponse(CategoryResponse):
    children: Optional[List["CategoryDetailResponse"]] = Field(
        None, description="子分类列表"
    )
    parent_category: Optional["CategoryNestedResponse"] = Field(
        None, description="父分类信息"
    )

    class Config:
        from_attributes = True
