# TanZouDaShiAPI/app/schemas/transaction.py
from datetime import datetime
from typing import Optional
from decimal import Decimal

from pydantic import BaseModel, Field

# 从 models 中导入 Enum 类型，以便在 Schema 中复用
from app.db.models.reseller_finance import CommissionType as CommissionTypeEnum
from app.db.models.reseller_finance import CommissionStatus as CommissionStatusEnum
from app.db.models.reseller_finance import WithdrawalStatus as WithdrawalStatusEnum

# --- CommissionRecord Schemas ---


class CommissionRecordBase(BaseModel):
    # beneficiary_reseller_user_id 在创建时通常是当前用户或由系统确定
    source_license_id: int = Field(..., description="产生佣金的卡密ID")
    source_reseller_user_id: int = Field(..., description="实际销售此卡密的源头代理ID")
    source_order_id: Optional[str] = Field(
        None, max_length=64, description="关联的订单号"
    )
    commission_amount: Decimal = Field(..., description="佣金金额")
    commission_rate: Decimal = Field(..., ge=0, le=1, description="计算时的佣金比例")
    commission_base_amount: Decimal = Field(..., description="计算此佣金的基准金额")
    commission_type: CommissionTypeEnum = Field(..., description="佣金类型")
    depth_from_source: int = Field(..., description="受益人相对于源头销售代理的层级")
    status: CommissionStatusEnum = Field(
        CommissionStatusEnum.pending, description="佣金状态"
    )
    settled_at: Optional[datetime] = Field(None, description="结算时间")
    related_user_id: Optional[int] = Field(None, description="购买卡密的最终用户ID")
    remarks: Optional[str] = Field(None, description="备注")


class CommissionRecordCreate(CommissionRecordBase):
    beneficiary_reseller_user_id: int  # 创建时必须指定受益代理ID


class CommissionRecordUpdate(BaseModel):  # 主要由管理员操作
    status: Optional[CommissionStatusEnum] = Field(None, description="佣金状态")
    settled_at: Optional[datetime] = Field(None, description="结算时间")
    remarks: Optional[str] = Field(None, description="更新备注")


class CommissionRecordResponse(CommissionRecordBase):
    id: int = Field(..., description="佣金记录唯一标识符")
    beneficiary_reseller_user_id: int = Field(..., description="受益代理ID")
    beneficiary_reseller_username: Optional[str] = Field(
        None, description="受益代理用户名"
    )  # 新增
    # 为方便前端显示，可以加入关联对象的简要信息
    source_license_key_string: Optional[str] = Field(
        None, description="源卡密号"
    )  # 已存在
    source_reseller_username: Optional[str] = Field(
        None, description="源头销售代理用户名"
    )  # 已存在
    related_username: Optional[str] = Field(
        None, description="最终激活用户用户名"
    )  # 已存在
    created_at: datetime = Field(..., description="记录创建时间")

    class Config:
        from_attributes = True


# --- WithdrawalRecord Schemas ---


class WithdrawalRecordBase(BaseModel):
    amount: Decimal = Field(..., gt=0, description="提现金额")
    # reseller_user_id 在创建时通常是当前用户
    status: WithdrawalStatusEnum = Field(
        WithdrawalStatusEnum.pending, description="请求状态"
    )
    payment_method: Optional[str] = Field(
        None, max_length=50, description="提现方式 (例如 支付宝, 微信, 银行卡)"
    )
    payment_account: Optional[str] = Field(
        None, max_length=255, description="提现账户信息"
    )
    remarks: Optional[str] = Field(None, description="用户备注")
    admin_notes: Optional[str] = Field(
        None, description="管理员处理备注 (通常由管理员填写)"
    )
    processed_at: Optional[datetime] = Field(
        None, description="处理时间 (通常由管理员填写)"
    )


class WithdrawalRecordCreate(BaseModel):  # 代理提交提现申请时用
    amount: Decimal = Field(..., gt=0, description="提现金额")
    payment_method: str = Field(..., max_length=50, description="提现方式")
    payment_account: str = Field(..., max_length=255, description="提现账户信息")
    remarks: Optional[str] = Field(None, description="用户备注")


class WithdrawalRecordUpdate(
    BaseModel
):  # 保留，但管理员操作使用 WithdrawalRecordUpdateByAdmin
    status: Optional[WithdrawalStatusEnum] = Field(None, description="请求状态")
    admin_notes: Optional[str] = Field(None, description="管理员处理备注")
    processed_at: Optional[datetime] = Field(None, description="处理时间")
    # payment_method 和 payment_account 一般不应在更新时修改，除非是修正错误
    payment_method: Optional[str] = Field(None, max_length=50, description="提现方式")
    payment_account: Optional[str] = Field(
        None, max_length=255, description="提现账户信息"
    )


class WithdrawalRecordUpdateByAdmin(BaseModel):  # 管理员处理提现请求时使用
    status: WithdrawalStatusEnum = Field(
        ..., description="新的提现状态 (approved 或 rejected)"
    )
    admin_notes: Optional[str] = Field(None, description="管理员备注")
    # processed_at 将在后端自动记录


class WithdrawalRecordResponse(WithdrawalRecordBase):
    id: int = Field(..., description="提现请求唯一标识符")
    reseller_user_id: int = Field(..., description="发起提现的代理用户ID")
    reseller_username: Optional[str] = Field(
        None, description="发起提现的代理用户名"
    )  # 方便前端显示
    created_at: datetime = Field(..., description="请求创建时间")
    updated_at: datetime = Field(..., description="请求更新时间")

    class Config:
        from_attributes = True


# 管理员批准提现请求时，可能不需要请求体
# class WithdrawalApproveRequest(BaseModel):
#     pass


# 管理员拒绝提现请求时
class WithdrawalRejectRequest(BaseModel):
    admin_notes: str = Field(..., description="拒绝原因或备注")


# 管理员标记提现完成时
# class WithdrawalCompleteRequest(BaseModel):
#     admin_notes: Optional[str] = Field(None, description="完成备注，如交易流水号")
class WithdrawalRecordCreateWithUser(WithdrawalRecordCreate):
    reseller_user_id: int = Field(..., description="发起提现的代理用户ID")


# Admin processing withdrawal request
class WithdrawalProcessRequest(BaseModel):
    status: WithdrawalStatusEnum = Field(
        ..., description="要设置的提现状态 (approved 或 rejected)"
    )
    remarks: Optional[str] = Field(None, description="处理备注")
