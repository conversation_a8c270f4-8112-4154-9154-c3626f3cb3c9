# 弹奏大师 - 数据库设计 (MySQL)

本文档详细描述了"弹奏大师"项目的数据库结构设计，基于 MySQL 数据库。

## 设计原则

*   **规范性**：遵循数据库设计范式，减少数据冗余。
*   **可扩展性**：设计时考虑未来功能的扩展需求。
*   **安全性**：关键字段进行适当的保护和索引。
*   **性能**：针对常用查询优化索引设计。

## 表结构详情

### 1. `users` (用户信息表)

存储所有用户（包括普通用户、代理、管理员）的基本信息。

| 字段名                   | 类型                                       | 约束/属性                               | 描述                                     | 备注                                                                 |
| :----------------------- | :----------------------------------------- | :-------------------------------------- | :--------------------------------------- | :------------------------------------------------------------------- |
| `id`                     | INT                                        | PK, AUTO_INCREMENT                      | 用户唯一标识符                           |                                                                      |
| `username`               | VARCHAR(50)                                | UNIQUE, NOT NULL                        | 用户名 (用于登录)                        | 建议正则验证格式                                                     |
| `password_hash`          | VARCHAR(255)                               | NOT NULL                                | 加密后的密码                             | 使用强哈希算法 (如 Argon2, bcrypt)                                   |
| `email`                  | VARCHAR(100)                               | UNIQUE, NULLABLE                        | 电子邮箱                                 | 用于通知、密码找回等，建议验证唯一性                                   |
| `phone_number`           | VARCHAR(20)                                | UNIQUE, NULLABLE                        | 手机号码                                 | 可选，用于短信验证或通知                                             |
| `avatar_url`             | VARCHAR(255)                               | NULLABLE                                | 用户头像URL                              |                                                                      |
| `nickname`               | VARCHAR(50)                                | NULLABLE                                | 用户昵称                                 |                                                                      |
| `role`                   | ENUM('admin', 'reseller', 'user')          | NOT NULL, DEFAULT 'user'                | 用户角色                                 | `admin`-管理员, `reseller`-代理, `user`-普通用户                     |
| `status`                 | ENUM('active', 'inactive', 'banned', 'pending_verification') | NOT NULL, DEFAULT 'active'              | 账户状态                                 | `pending_verification` - 待邮箱/手机验证                             |
| `max_concurrent_devices` | INT                                        | NOT NULL, DEFAULT 1                     | 最大同时在线设备数                       |                                                                      |
| `creation_source`        | ENUM('registration', 'license_activation', 'admin_created') | NOT NULL, DEFAULT 'registration'    | 用户创建来源                             | `registration`-常规注册, `license_activation`-激活码激活创建, `admin_created`-管理员创建 |
| `registration_ip`        | VARCHAR(45)                                | NULLABLE                                | 注册时IP地址                             |                                                                      |
| `last_login_ip`          | VARCHAR(45)                                | NULLABLE                                | 最后登录IP地址                           |                                                                      |
| `last_login_at`          | TIMESTAMP                                  | NULLABLE                                | 最后登录时间                             |                                                                      |
| `email_verified_at`      | TIMESTAMP                                  | NULLABLE                                | 邮箱验证时间                             |                                                                      |
| `phone_verified_at`      | TIMESTAMP                                  | NULLABLE                                | 手机验证时间                             |                                                                      |
| `created_at`             | TIMESTAMP                                  | DEFAULT CURRENT_TIMESTAMP               | 账户创建时间                             |                                                                      |
| `updated_at`             | TIMESTAMP                                  | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 账户信息更新时间                         |                                                                      |
| `deleted_at`             | TIMESTAMP                                  | NULLABLE                                | 软删除标记时间                           | 用于软删除                                                           |
| `permanent_license_unbind_count_current_month` | INT                                        | DEFAULT 0, NULLABLE                   | 永久卡用户当月已解绑设备次数             | 每月1日重置                                                          |
| `permanent_license_last_unbind_month` | VARCHAR(7)                                 | NULLABLE                                | 永久卡用户上次解绑的月份 (YYYY-MM)       | 用于月度解绑次数重置判断                                             |

**索引建议:**
*   `username` (UNIQUE)
*   `email` (UNIQUE)
*   `phone_number` (UNIQUE)
*   `role`
*   `status`

### 2. `license_keys` (卡密表)

存储所有生成的卡密信息。

| 字段名                     | 类型                                                     | 约束/属性                               | 描述                                     | 备注                                                                 |
| :------------------------- | :------------------------------------------------------- | :-------------------------------------- | :--------------------------------------- | :------------------------------------------------------------------- |
| `id`                       | INT                                                      | PK, AUTO_INCREMENT                      | 卡密唯一标识符                           |                                                                      |
| `key_string`               | VARCHAR(64)                                              | UNIQUE, NOT NULL, INDEX                 | 卡密字符串                               | 建议生成包含字母数字的随机字符串，增加唯一性前缀                     |
| `license_type_id`          | INT                                                      | FK, NOT NULL (REFERENCES `license_types`(`id`)) | 卡密类型ID                               | 关联到 `license_types` 表                                            |
| `status`                   | ENUM('available', 'used', 'expired', 'disabled', 'consigned_available') | NOT NULL, DEFAULT 'available', INDEX    | 卡密状态                                 | `disabled`-管理员禁用; `consigned_available`-已分配给代理待售 |
| `user_id`                  | INT                                                      | FK, NULLABLE, INDEX                     | 使用该卡密的用户ID (REFERENCES `users`(`id`)) | 卡密被激活后**必须**关联用户。                                       |
| `activated_at`             | TIMESTAMP                                                | NULLABLE                                | 激活时间                                 | 卡密激活并成功关联到用户账户的时间。                                   |
| `expires_at`               | TIMESTAMP                                                | NULLABLE, INDEX                         | 过期时间                                 | 根据 `activated_at` 和关联的 `license_types` 表中的时长配置计算。续期采用有效期叠加规则。 |
| `issued_by_user_id`        | INT                                                      | FK, NULLABLE                            | 发放该卡密的管理员/代理ID (REFERENCES `users`(`id`)) | 记录最初生成此卡密的管理员或（购买此卡的）代理                       |
| `consignment_reseller_id`  | INT                                                      | FK, NULLABLE (REFERENCES `users`(`id`)) | 持有此寄售卡密的代理ID                   | 如果此卡为寄售模式，则记录代理ID。                                   |
| `settlement_status`        | ENUM('pending', 'settled', 'not_applicable')             | NOT NULL, DEFAULT 'not_applicable'      | 寄售卡密成本结算状态                     | `pending`-激活待结算, `settled`-已结算, `not_applicable`-非寄售卡 |
| `batch_id`                 | VARCHAR(36)                                              | NULLABLE, INDEX                         | 批次号                                   | 用于追踪同一批生成的卡密，可使用UUID                                 |
| `renewal_source_key_id`  | INT                                                      | FK, NULLABLE                            | 续期来源卡密ID (REFERENCES `license_keys`(`id`)) | 如果是续期卡密，指向被续期的旧卡密ID                               |
| `notes`                    | TEXT                                                     | NULLABLE                                | 备注信息                                 | 例如，特殊活动卡密                                                   |
| `created_at`               | TIMESTAMP                                                | DEFAULT CURRENT_TIMESTAMP               | 卡密创建时间                             |                                                                      |
| `updated_at`               | TIMESTAMP                                                | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 卡密信息更新时间                         |                                                                      |

**索引建议:**
*   `key_string` (UNIQUE)
*   `status`
*   `expires_at`
*   `user_id`
*   `issued_by_user_id`
*   `consignment_reseller_id`
*   `settlement_status`
*   `batch_id`
*   `license_type_id`

### 3. `license_types` (卡密类型定义表)

存储管理员定义的各种卡密类型及其属性。

| 字段名                   | 类型                                               | 约束/属性           | 描述                                     | 备注                                                                 |
| :----------------------- | :------------------------------------------------- | :------------------ | :--------------------------------------- | :------------------------------------------------------------------- |
| `id`                     | INT                                                | PK, AUTO_INCREMENT  | 类型唯一标识符                           |                                                                      |
| `name`                   | VARCHAR(100)                                       | NOT NULL, UNIQUE    | 类型名称 (供后台管理识别)                | 例如 "标准月卡", "7天体验卡", "3小时畅玩卡"                          |
| `code`                   | VARCHAR(50)                                        | NOT NULL, UNIQUE    | 类型代码 (程序内部和API使用)             | 例如 "MONTHLY_STD", "TRIAL_7D", "HOURLY_3H"                          |
| `description`            | TEXT                                               | NULLABLE            | 类型描述                                 |                                                                      |
| `duration_value`         | INT                                                | NOT NULL            | 时长数值                                 | 配合 `duration_unit` 使用                                            |
| `duration_unit`          | ENUM('days', 'hours', 'minutes', 'permanent')      | NOT NULL            | 时长单位                                 | `permanent` 表示永久有效                                             |
| `max_concurrent_devices` | INT                                                | NOT NULL, DEFAULT 1 | 该类型允许的最大同时在线设备数           |                                                                      |
| `price`                  | DECIMAL(10,2)                                      | NULLABLE            | 建议售价 (供参考)                        |                                                                      |
| `reseller_cost`          | DECIMAL(10,2)                                      | NULLABLE            | 代理拿货成本 (供参考)                    |                                                                      |
| `is_active`              | BOOLEAN                                            | NOT NULL, DEFAULT TRUE | 是否启用该类型                           | 禁用的类型无法生成新的卡密                                           |
| `is_publicly_available`  | BOOLEAN                                            | NOT NULL, DEFAULT TRUE | 是否允许代理/用户获取                    | 控制特殊类型只能由管理员发放                                         |
| `created_at`             | TIMESTAMP                                          | DEFAULT CURRENT_TIMESTAMP | 创建时间                                 |                                                                      |
| `updated_at`             | TIMESTAMP                                          | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                 |                                                                      |

**索引建议:**
*   `code` (UNIQUE)
*   `is_active`

### 4. `resellers` (代理信息表)
### 3a. `reseller_levels` (代理级别定义表)

存储管理员定义的代理级别及其属性。

| 字段名                            | 类型          | 约束/属性                        | 描述                                      | 备注                                                     |
| :-------------------------------- | :------------ | :------------------------------- | :---------------------------------------- | :------------------------------------------------------- |
| `id`                              | INT           | PK, AUTO_INCREMENT               | 级别唯一标识符                            |                                                          |
| `level_value`                     | INT           | UNIQUE, NOT NULL                 | 级别数值                                  | 例如 1, 2, 3。用于内部逻辑和可能的排序。                 |
| `name`                            | VARCHAR(100)  | NOT NULL, UNIQUE                 | 级别名称 (供后台管理和显示)               | 例如 "普通代理", "银牌代理", "金牌代理"                  |
| `description`                     | TEXT          | NULLABLE                         | 级别描述                                  |                                                          |
| `upgrade_threshold_sales`         | DECIMAL(15,2) | NULLABLE                         | 升级到此级别的累计销售额门槛              |                                                          |
| `upgrade_threshold_sub_resellers` | INT           | NULLABLE                         | 升级到此级别的直接下级代理数量门槛        |                                                          |
| `default_commission_rate`         | DECIMAL(5,4)  | NULLABLE                         | 此级别的默认直接销售佣金比例              | 可能会被特定规则覆盖。                                   |
| `default_sub_commission_rate`     | DECIMAL(5,4)  | NULLABLE                         | 此级别的默认间接（下级）佣金比例          | 例如，L1代理从其直接L2下级销售中获得的佣金比例             |
| `default_second_tier_sub_commission_rate` | DECIMAL(5,4) | NULLABLE, DEFAULT NULL           | 默认二级间接佣金率 (来自间接下两级的销售) | 例如，L1代理从其L3下下级（通过L2）销售中获得的佣金比例     |
| `is_active`                       | BOOLEAN       | NOT NULL, DEFAULT TRUE           | 是否启用此级别                            |                                                          |
| `created_at`                      | TIMESTAMP     | DEFAULT CURRENT_TIMESTAMP        | 创建时间                                  |                                                          |
| `updated_at`                      | TIMESTAMP     | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                  |                                                          |

**索引建议:**
*   `level_value` (UNIQUE)
*   `name` (UNIQUE)
*   `is_active`

### 4a. `resellers` (代理信息表)

存储代理用户的扩展信息。代理本身也是`users`表中的一条记录。

| 字段名                     | 类型          | 约束/属性                        | 描述                                           | 备注                                                               |
| :------------------------- | :------------ | :------------------------------- | :--------------------------------------------- | :----------------------------------------------------------------- |
| `user_id`                  | INT           | PK, FK (REFERENCES `users`(`id`)) | 关联的用户ID                                   |                                                                    |
| `parent_reseller_user_id`  | INT           | FK, NULLABLE (REFERENCES `users`(`id`)) | 上级代理的用户ID                               | 用于实现多级代理结构                                               |
| `commission_rate`          | DECIMAL(5,4)  | NULLABLE                         | 特定用户的直接销售佣金比例                     | 可选，用于覆盖 `reseller_levels` 中的默认值。                     |
| `balance`                  | DECIMAL(12,2) | NOT NULL, DEFAULT 0.00           | 代理账户余额/可提现额度                        |                                                                    |
| `reseller_level_value`     | INT           | FK, NULLABLE (REFERENCES `reseller_levels`(`level_value`)) | 代理级别数值                               | 关联到 `reseller_levels` 表的 `level_value`。如果为NULL，则为未定级代理。 |
| `consignment_limit_count`  | INT           | NOT NULL, DEFAULT 0              | 允许持有的最大寄售卡密数量                     | 0表示不允许寄售。                                                  |
| `consignment_limit_value`  | DECIMAL(12,2) | NOT NULL, DEFAULT 0.00           | 允许持有的最大寄售卡密总价值（按拿货价计）     | 0表示不允许寄售。                                                  |
| `status`                   | ENUM('active', 'inactive', 'frozen') | NOT NULL, DEFAULT 'active'       | 代理资格状态                                   | `frozen` - 冻结，可能由于违规等                                    |
| `total_sales_amount`       | DECIMAL(15,2) | NOT NULL, DEFAULT 0.00           | 累计销售总额（已结算部分）                     | 用于统计和升级                                                     |
| `total_commission_earned`  | DECIMAL(15,2) | NOT NULL, DEFAULT 0.00           | 累计获得佣金总额                               |                                                                    |
| `remarks`                  | TEXT          | NULLABLE                         | 代理备注信息                                   | 例如，签约渠道，特殊约定等                                         |
| `created_at`               | TIMESTAMP     | DEFAULT CURRENT_TIMESTAMP        | 成为代理的时间                                 |                                                                    |
| `updated_at`               | TIMESTAMP     | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 代理信息更新时间                               |                                                                    |

**索引建议:**
*   `parent_reseller_user_id`
*   `reseller_level_value`
*   `status`

### 5. `reseller_licenses_quotas` (代理卡密额度表)

如果代理生成卡密需要额度限制，可以使用此表。

| 字段名              | 类型                                               | 约束/属性                        | 描述                                 | 备注                                               |
| :------------------ | :------------------------------------------------- | :------------------------------- | :----------------------------------- | :------------------------------------------------- |
| `id`                | INT                                                | PK, AUTO_INCREMENT               | 额度记录ID                           |                                                    |
| `reseller_user_id`  | INT                                                | FK, NOT NULL (REFERENCES `users`(`id`)) | 代理的用户ID                         |                                                    |
| `license_type_id`   | INT                                                | FK, NOT NULL (REFERENCES `license_types`(`id`)) | 卡密类型ID                           | 关联到 `license_types` 表                          |
| `quota_available`   | INT                                                | NOT NULL, DEFAULT 0              | 可用额度数量                         |                                                    |
| `quota_used`        | INT                                                | NOT NULL, DEFAULT 0              | 已用额度数量                         |                                                    |
| `last_refilled_at`  | TIMESTAMP                                          | NULLABLE                         | 最后补充额度时间                     |                                                    |
| `notes`             | TEXT                                               | NULLABLE                         | 额度备注                             | 例如，活动赠送额度                                 |
| `created_at`        | TIMESTAMP                                          | DEFAULT CURRENT_TIMESTAMP        | 记录创建时间                         |                                                    |
| `updated_at`        | TIMESTAMP                                          | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间                         |                                                    |

**索引建议:**
*   UNIQUE (`reseller_user_id`, `license_type_id`)

### 6. `commission_records` (佣金记录表)

记录代理的佣金产生和结算情况。

| 字段名                | 类型                               | 约束/属性                        | 描述                                         | 备注                                                                  |
| :-------------------- | :--------------------------------- | :------------------------------- | :------------------------------------------- | :-------------------------------------------------------------------- |
| `id`                  | INT                                | PK, AUTO_INCREMENT               | 记录ID                                       |                                                                       |
| `beneficiary_reseller_user_id` | INT                                                | FK, NOT NULL (REFERENCES `users`(`id`)) | 此笔佣金的受益代理ID (原 reseller_user_id)   |                                                                       |
| `source_license_id`          | INT                                                | FK, NOT NULL (REFERENCES `license_keys`(`id`)) | 产生佣金的卡密ID                             |                                                                       |
| `source_reseller_user_id`    | INT                                                | FK, NOT NULL (REFERENCES `users`(`id`)) | 实际销售此卡密的源头代理ID                   |                                                                       |
| `source_order_id`            | VARCHAR(64)                                        | NULLABLE                                 | 关联的订单号 (如果通过商城等系统销售)        |                                                                       |
| `commission_amount`          | DECIMAL(10,2)                                      | NOT NULL                                 | 佣金金额                                     |                                                                       |
| `commission_rate`            | DECIMAL(5,4)                                       | NOT NULL                                 | 计算时的佣金比例                             |                                                                       |
| `commission_base_amount`     | DECIMAL(10,2)                                      | NOT NULL                                 | 计算此佣金的基准金额                         | 例如，源头销售代理的卡密拿货成本                                        |
| `commission_type`            | ENUM('direct', 'indirect_level_1', 'indirect_level_2') | NOT NULL                                 | 佣金类型                                     | `direct`-直接销售佣金, `indirect_level_1`-一级间接佣金, `indirect_level_2`-二级间接佣金 |
| `depth_from_source`          | TINYINT                                            | NOT NULL                                 | 受益人相对于源头销售代理的层级               | 0=自己(直接), 1=上一级, 2=上两级                                      |
| `status`                     | ENUM('pending', 'settled', 'frozen', 'cancelled')  | NOT NULL, DEFAULT 'pending'              | 佣金状态                                     | `settled`-已结算, `frozen`-冻结, `cancelled`-取消                 |
| `settled_at`                 | TIMESTAMP                                          | NULLABLE                                 | 结算时间                                     |                                                                       |
| `related_user_id`            | INT                                                | FK, NULLABLE (REFERENCES `users`(`id`))  | 购买卡密的最终用户ID (如果适用)              |                                                                       |
| `remarks`                    | TEXT                                               | NULLABLE                                 | 备注                                         |                                                                       |
| `created_at`                 | TIMESTAMP                                          | DEFAULT CURRENT_TIMESTAMP                | 记录创建时间                                 |                                                                       |

**索引建议:**
*   `beneficiary_reseller_user_id`
*   `source_license_id`
*   `source_reseller_user_id`
*   `commission_type`
*   `status`
### 7a. `reseller_discounts` (代理折扣规则表)

存储针对不同代理/级别和卡密类型的特定折扣规则。

| 字段名                  | 类型                            | 约束/属性                                  | 描述                                                                 | 备注                                                                                                             |
| :---------------------- | :------------------------------ | :----------------------------------------- | :------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------- |
| `id`                    | INT                             | PK, AUTO_INCREMENT                         | 折扣规则唯一ID                                                       |                                                                                                                  |
| `description`           | VARCHAR(255)                    | NOT NULL                                   | 对此折扣规则的描述                                                   | 例如 "一级代理月卡8折优惠", "VIP代理专属年卡固定价"                                                              |
| `reseller_level_value`  | INT                             | FK, NULLABLE (REFERENCES `reseller_levels`(`level_value`)) | 适用的代理级别数值                                               | 如果为NULL，则此规则不按级别区分。                                                                               |
| `reseller_user_id`      | INT                             | FK, NULLABLE (REFERENCES `users`(`id`))      | 适用的特定代理用户ID                                                 | 如果为NULL，则此规则适用于某个级别或所有代理（取决于其他字段配置）。                                               |
| `license_type_id`       | INT                             | FK, NULLABLE (REFERENCES `license_types`(`id`)) | 适用的特定卡密类型ID                                                 | 如果为NULL，则此规则适用于指定代理/级别的所有卡密类型。                                                            |
| `discount_type`         | ENUM('percentage', 'fixed_price') | NOT NULL                                   | 折扣类型                                                             | 'percentage': 百分比折扣, 'fixed_price': 固定拿货价                                                                |
| `discount_value`        | DECIMAL(10,4)                   | NOT NULL                                   | 折扣值                                                               | 如果是百分比, 例如 0.2000 (表示20%折扣)。如果是固定价格, 则为具体金额。使用4位小数以支持更精细的百分比。             |
| `priority`              | INT                             | NOT NULL, DEFAULT 0                        | 规则优先级                                                           | 数字越大优先级越高。用于处理一个代理可能匹配多条规则的情况。                                                 |
| `start_date`            | DATETIME                        | NULLABLE                                   | 规则生效开始时间                                                     |                                                                                                                  |
| `end_date`              | DATETIME                        | NULLABLE                                   | 规则生效结束时间                                                     |                                                                                                                  |
| `is_active`             | BOOLEAN                         | NOT NULL, DEFAULT TRUE                     | 是否启用此规则                                                       |                                                                                                                  |
| `created_at`            | TIMESTAMP                       | DEFAULT CURRENT_TIMESTAMP                  | 创建时间                                                             |                                                                                                                  |
| `updated_at`            | TIMESTAMP                       | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间                                                             |                                                                                                                  |

**索引建议:**
*   UNIQUE (`reseller_level_value`, `reseller_user_id`, `license_type_id`, `start_date`, `end_date`) (考虑实际唯一性约束的复杂性，或者通过程序逻辑保证)
*   `is_active`
*   `priority`
*   `reseller_user_id`
*   `license_type_id`
*   `status`

### 7. `withdrawal_requests` (代理提现请求表)

| 字段名             | 类型                                           | 约束/属性                        | 描述             | 备注                                                      |
| :----------------- | :--------------------------------------------- | :------------------------------- | :--------------- | :-------------------------------------------------------- |
| `id`               | INT                                            | PK, AUTO_INCREMENT               | 请求ID           |                                                           |
| `reseller_user_id` | INT                                            | FK, NOT NULL (REFERENCES `users`(`id`)) | 代理用户ID       |                                                           |
| `amount`           | DECIMAL(10,2)                                  | NOT NULL                         | 提现金额         |                                                           |
| `status`           | ENUM('pending', 'approved', 'rejected', 'completed', 'failed') | NOT NULL, DEFAULT 'pending'      | 请求状态         | `completed`-已打款, `failed`-打款失败                     |
| `payment_method`   | VARCHAR(50)                                    | NULLABLE                         | 提现方式         | 例如 支付宝, 微信, 银行卡                                 |
| `payment_account`  | VARCHAR(255)                                   | NULLABLE                         | 提现账户信息     |                                                           |
| `remarks`          | TEXT                                           | NULLABLE                         | 用户备注         |                                                           |
| `admin_notes`      | TEXT                                           | NULLABLE                         | 管理员处理备注   |                                                           |
| `processed_at`     | TIMESTAMP                                      | NULLABLE                         | 处理时间         |                                                           |
| `created_at`       | TIMESTAMP                                      | DEFAULT CURRENT_TIMESTAMP        | 请求创建时间     |                                                           |
| `updated_at`       | TIMESTAMP                                      | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 请求更新时间     |                                                           |

**索引建议:**
*   `reseller_user_id`
*   `status`

### 8. `categories` (谱曲分类表)

存储谱曲的分类信息，支持多级分类。

| 字段名                | 类型         | 约束/属性                               | 描述                                | 备注                                   |
| :-------------------- | :----------- | :-------------------------------------- | :---------------------------------- | :------------------------------------- |
| `id`                  | INT          | PK, AUTO_INCREMENT                      | 分类唯一标识符                      |                                        |
| `name`                | VARCHAR(100) | NOT NULL, UNIQUE (同级目录下唯一)       | 分类名称                            |                                        |
| `slug`                | VARCHAR(100) | NOT NULL, UNIQUE (全局唯一)             | URL友好型名称 (slug)                | 例如 "pop-music"                       |
| `parent_category_id`  | INT          | FK, NULLABLE (REFERENCES `categories`(`id`)) | 父分类ID                            | NULL表示顶级分类                       |
| `description`         | TEXT         | NULLABLE                                | 分类描述                            |                                        |
| `icon_url`            | VARCHAR(255) | NULLABLE                                | 分类图标URL                         |                                        |
| `sort_order`          | INT          | NOT NULL, DEFAULT 0                     | 排序字段                            | 数字越小越靠前                         |
| `is_active`           | BOOLEAN      | NOT NULL, DEFAULT TRUE                  | 是否启用                            |                                        |
| `created_at`          | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 分类创建时间                        |                                        |
| `updated_at`          | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 分类信息更新时间                    |                                        |

**索引建议:**
*   `slug` (UNIQUE)
*   `parent_category_id`
*   `is_active`

### 9. `scores` (谱曲信息表)

存储核心的谱曲数据。

| 字段名                  | 类型                                                | 约束/属性                               | 描述                                     | 备注                                                                    |
| :---------------------- | :-------------------------------------------------- | :-------------------------------------- | :--------------------------------------- | :---------------------------------------------------------------------- |
| `id`                    | INT                                                 | PK, AUTO_INCREMENT                      | 谱曲唯一标识符                           |                                                                         |
| `title`                 | VARCHAR(255)                                        | NOT NULL, INDEX                         | 谱曲标题                                 |                                                                         |
| `description`           | TEXT                                                | NULLABLE                                | 谱曲描述 (例如作者、风格简介)            |                                                                         |
| `uploader_user_id`      | INT                                                 | FK, NOT NULL (REFERENCES `users`(`id`))   | 上传者用户ID                             |                                                                         |
| `category_id`           | INT                                                 | FK, NOT NULL (REFERENCES `categories`(`id`)) | 所属分类ID                               |                                                                         |
| `type`                  | ENUM('TXT', 'MIDI_REF', 'EXTERNAL_LINK')            | NOT NULL                                | 谱曲类型                                 | `MIDI_REF`-关联服务器MIDI文件, `EXTERNAL_LINK`-外部链接谱 |
| `txt_content`           | LONGTEXT                                            | NULLABLE                                | TXT谱曲内容 (当type='TXT'或由MIDI转换)   |                                                                         |
| `midi_storage_type`     | ENUM('local', 's3', 'oss')                          | NULLABLE                                | MIDI文件存储类型 (当type='MIDI_REF')     |                                                                         |
| `midi_path_or_url`      | VARCHAR(512)                                        | NULLABLE                                | MIDI文件路径或URL (当type='MIDI_REF')    | 存储相对路径或云存储URL                                                 |
| `original_midi_filename`| VARCHAR(255)                                        | NULLABLE                                | 原始MIDI文件名 (当type='MIDI_REF')       |                                                                         |
| `external_url`          | VARCHAR(512)                                        | NULLABLE                                | 外部谱曲链接 (当type='EXTERNAL_LINK')    |                                                                         |
| `difficulty`            | ENUM('beginner', 'easy', 'medium', 'hard', 'expert') | NULLABLE                                | 难度等级                                 |                                                                         |
| `tags`                  | JSON                                                | NULLABLE                                | 标签 (例如 ["pop", "piano solo"])        | 便于搜索和推荐                                                          |
| `cover_image_url`       | VARCHAR(255)                                        | NULLABLE                                | 谱曲封面图片URL                          |                                                                         |
| `duration_seconds`      | INT                                                 | NULLABLE                                | 曲谱预计演奏时长 (秒)                    |                                                                         |
| `is_premium_only`       | BOOLEAN                                             | NOT NULL, DEFAULT FALSE                 | 是否仅限付费用户                         |                                                                         |
| `status`                | ENUM('pending', 'approved', 'rejected', 'private', 'draft') | NOT NULL, DEFAULT 'pending'             | 谱曲状态                                 | `draft`-草稿, `private`-私人(仅自己可见)                                |
| `view_count`            | INT                                                 | NOT NULL, DEFAULT 0                     | 查看次数                                 |                                                                         |
| `download_count`        | INT                                                 | NOT NULL, DEFAULT 0                     | 下载次数                                 |                                                                         |
| `favorite_count`        | INT                                                 | NOT NULL, DEFAULT 0                     | 收藏次数                                 | 通过 `user_favorites` 表聚合或定期更新                                  |
| `comment_count`         | INT                                                 | NOT NULL, DEFAULT 0                     | 评论数量                                 | 通过 `score_comments` 表聚合或定期更新                                  |
| `average_rating`        | DECIMAL(3,2)                                        | NOT NULL, DEFAULT 0.00                  | 平均评分                                 | 0.00 - 5.00                                                             |
| `rating_count`          | INT                                                 | NOT NULL, DEFAULT 0                     | 评分次数                                 |                                                                         |
| `last_played_at`        | TIMESTAMP                                           | NULLABLE                                | 用户最后弹奏此谱时间 (考虑是否需要)        | 可能需要额外表记录用户弹奏历史                                          |
| `created_at`            | TIMESTAMP                                           | DEFAULT CURRENT_TIMESTAMP               | 谱曲上传时间                             |                                                                         |
| `updated_at`            | TIMESTAMP                                           | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 谱曲信息更新时间                         |                                                                         |
| `approved_by_user_id`   | INT                                                 | FK, NULLABLE (REFERENCES `users`(`id`))   | 审核操作的管理员ID                       |                                                                         |
| `approved_at`           | TIMESTAMP                                           | NULLABLE                                | 审核通过/拒绝时间                        |                                                                         |

**索引建议:**
*   `title`
*   `uploader_user_id`
*   `category_id`
*   `status`
*   `type`
*   `is_premium_only`
*   `tags` (如果数据库支持JSON索引)
*   `created_at`

### 10. `user_favorites` (用户收藏表)

记录用户收藏的谱曲，多对多关系。

| 字段名       | 类型      | 约束/属性                                     | 描述         | 备注 |
| :----------- | :-------- | :-------------------------------------------- | :----------- | :--- |
| `user_id`    | INT       | PK, FK (REFERENCES `users`(`id`) ON DELETE CASCADE) | 用户ID       |      |
| `score_id`   | INT       | PK, FK (REFERENCES `scores`(`id`) ON DELETE CASCADE) | 谱曲ID       |      |
| `created_at` | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP                     | 收藏时间     |      |

**主键:** (`user_id`, `score_id`)

**索引建议:**
*   `user_id`
*   `score_id`

### 11. `user_score_ratings` (用户谱曲评分表)

| 字段名       | 类型         | 约束/属性                                     | 描述         | 备注        |
| :----------- | :----------- | :-------------------------------------------- | :----------- | :---------- |
| `user_id`    | INT          | PK, FK (REFERENCES `users`(`id`) ON DELETE CASCADE) | 用户ID       |             |
| `score_id`   | INT          | PK, FK (REFERENCES `scores`(`id`) ON DELETE CASCADE) | 谱曲ID       |             |
| `rating`     | TINYINT      | NOT NULL                                      | 评分 (1-5)   | 1到5星      |
| `comment`    | TEXT         | NULLABLE                                      | 评论内容     | 可选的评论  |
| `created_at` | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP                     | 评分时间     |             |
| `updated_at` | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间     |             |

**主键:** (`user_id`, `score_id`)

**索引建议:**
*   `score_id` (用于快速查询某谱曲的所有评分)

### 12. `score_comments` (谱曲评论表)

如果评分和评论分离，或者需要层级评论。

| 字段名                | 类型         | 约束/属性                                     | 描述             | 备注                 |
| :-------------------- | :----------- | :-------------------------------------------- | :--------------- | :------------------- |
| `id`                  | INT          | PK, AUTO_INCREMENT                          | 评论ID           |                      |
| `score_id`            | INT          | FK, NOT NULL (REFERENCES `scores`(`id`) ON DELETE CASCADE) | 谱曲ID           |                      |
| `user_id`             | INT          | FK, NOT NULL (REFERENCES `users`(`id`))       | 评论用户ID       |                      |
| `parent_comment_id`   | INT          | FK, NULLABLE (REFERENCES `score_comments`(`id`)) | 父评论ID         | 用于回复嵌套评论     |
| `content`             | TEXT         | NOT NULL                                      | 评论内容         |                      |
| `status`              | ENUM('visible', 'hidden', 'pending_review') | NOT NULL, DEFAULT 'visible'             | 评论状态         |                      |
| `created_at`          | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP                     | 评论创建时间     |                      |
| `updated_at`          | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 评论更新时间     |                      |

**索引建议:**
*   `score_id`
*   `user_id`
*   `parent_comment_id`
*   `status`

### 13. `user_keybindings` (用户键位备份表)

存储用户自定义的键位配置。

| 字段名         | 类型         | 约束/属性                               | 描述             | 备注                            |
| :------------- | :----------- | :-------------------------------------- | :--------------- | :------------------------------ |
| `id`           | INT          | PK, AUTO_INCREMENT                      | 备份唯一标识符   |                                 |
| `user_id`      | INT          | FK, NOT NULL (REFERENCES `users`(`id`))   | 用户ID           |                                 |
| `binding_name` | VARCHAR(100) | NOT NULL, DEFAULT 'Default'             | 备份名称         | 用户可自定义，如 "我的配置1"    |
| `binding_data` | JSON         | NOT NULL                                | 键位配置数据     | 存储具体的键位映射 JSON 对象    |
| `device_type`  | VARCHAR(50)  | NULLABLE                                | 适用设备类型     | 例如 "PC", "Mobile", "KeyboardX" |
| `is_default`   | BOOLEAN      | NOT NULL, DEFAULT FALSE                 | 是否为默认配置   | 每个用户可有一个默认配置        |
| `created_at`   | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 备份创建时间     |                                 |
| `updated_at`   | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 备份信息更新时间 |                                 |

**索引建议:**
*   `user_id`
*   UNIQUE (`user_id`, `binding_name`) (如果名称在用户内唯一)
*   (`user_id`, `is_default`)

### 14. `support_tickets` (用户工单表)

用于用户提交问题和寻求支持。

| 字段名            | 类型                                                 | 约束/属性                               | 描述                                     | 备注                                                               |
| :---------------- | :--------------------------------------------------- | :-------------------------------------- | :--------------------------------------- | :----------------------------------------------------------------- |
| `id`              | INT                                                  | PK, AUTO_INCREMENT                      | 工单唯一标识符                           |                                                                    |
| `ticket_number`   | VARCHAR(20)                                          | UNIQUE, NOT NULL                        | 工单号 (可自定义生成规则)                | 例如 `TK-YYYYMMDD-XXXX`                                            |
| `user_id`         | INT                                                  | FK, NOT NULL (REFERENCES `users`(`id`))   | 提交用户ID                               |                                                                    |
| `title`           | VARCHAR(255)                                         | NOT NULL                                | 工单标题                                 |                                                                    |
| `description`     | TEXT                                                 | NOT NULL                                | 工单详细内容                             |                                                                    |
| `status`          | ENUM('open', 'in_progress', 'awaiting_reply', 'resolved', 'closed', 'reopened') | NOT NULL, DEFAULT 'open'                | 工单状态                                 | `awaiting_reply` - 等待用户回复                                    |
| `priority`        | ENUM('low', 'medium', 'high', 'urgent')              | NOT NULL, DEFAULT 'medium'              | 优先级                                   |                                                                    |
| `category`        | VARCHAR(50)                                          | NULLABLE                                | 工单分类                                 | 例如 "技术问题", "账号问题", "建议反馈"                            |
| `assigned_to_user_id` | INT                                              | FK, NULLABLE (REFERENCES `users`(`id`))   | 分配给处理的客服/管理员ID                |                                                                    |
| `created_at`      | TIMESTAMP                                            | DEFAULT CURRENT_TIMESTAMP               | 工单创建时间                             |                                                                    |
| `updated_at`      | TIMESTAMP                                            | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 工单信息更新时间                         |                                                                    |
| `resolved_at`     | TIMESTAMP                                            | NULLABLE                                | 解决时间                                 |                                                                    |
| `closed_at`       | TIMESTAMP                                            | NULLABLE                                | 关闭时间                                 |                                                                    |
| `last_reply_at`   | TIMESTAMP                                            | NULLABLE                                | 最后回复时间                             |                                                                    |

**索引建议:**
*   `ticket_number` (UNIQUE)
*   `user_id`
*   `status`
*   `priority`
*   `assigned_to_user_id`

### 15. `ticket_replies` (工单回复表)

存储工单的回复内容。

| 字段名       | 类型         | 约束/属性                                       | 描述             | 备注                                    |
| :----------- | :----------- | :---------------------------------------------- | :--------------- | :-------------------------------------- |
| `id`         | INT          | PK, AUTO_INCREMENT                              | 回复唯一标识符   |                                         |
| `ticket_id`  | INT          | FK, NOT NULL (REFERENCES `support_tickets`(`id`) ON DELETE CASCADE) | 关联工单ID       |                                         |
| `user_id`    | INT          | FK, NOT NULL (REFERENCES `users`(`id`))           | 回复用户ID       | 可以是客户、管理员或客服                |
| `content`    | TEXT         | NOT NULL                                        | 回复内容         |                                         |
| `is_internal`| BOOLEAN      | NOT NULL, DEFAULT FALSE                         | 是否内部回复     | 内部回复对提交工单的用户不可见          |
| `created_at` | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP                       | 回复创建时间     |                                         |
| `updated_at` | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP       | 回复更新时间     |                                         |

**索引建议:**
*   `ticket_id`
*   `user_id`

### 16. `attachments` (附件表)

通用附件表，可用于工单附件、谱曲相关文件（非MIDI主体）等。

| 字段名            | 类型        | 约束/属性                               | 描述             | 备注                                     |
| :---------------- | :---------- | :-------------------------------------- | :--------------- | :--------------------------------------- |
| `id`              | INT         | PK, AUTO_INCREMENT                      | 附件ID           |                                          |
| `related_object_type` | VARCHAR(50) | NOT NULL                                | 关联对象类型     | 例如 'ticket_reply', 'score_submission'  |
| `related_object_id` | INT         | NOT NULL                                | 关联对象ID       |                                          |
| `uploader_user_id`| INT         | FK, NOT NULL (REFERENCES `users`(`id`))   | 上传用户ID       |                                          |
| `filename`        | VARCHAR(255)| NOT NULL                                | 原始文件名       |                                          |
| `stored_filename` | VARCHAR(255)| NOT NULL, UNIQUE                        | 存储文件名       | 通常是UUID或哈希处理后的文件名           |
| `storage_type`    | ENUM('local', 's3', 'oss') | NOT NULL, DEFAULT 'local'               | 存储类型         |                                          |
| `path_or_url`     | VARCHAR(512)| NOT NULL                                | 文件路径或URL    |                                          |
| `file_type`       | VARCHAR(50) | NULLABLE                                | 文件MIME类型     | 例如 'image/jpeg', 'application/pdf'     |
| `file_size_bytes` | BIGINT      | NULLABLE                                | 文件大小 (字节)  |                                          |
| `created_at`      | TIMESTAMP   | DEFAULT CURRENT_TIMESTAMP               | 上传时间         |                                          |

**索引建议:**
*   (`related_object_type`, `related_object_id`)
*   `uploader_user_id`
*   `stored_filename` (UNIQUE)

### 17. `system_settings` (系统设置表)

存储系统级别的配置参数。

| 字段名         | 类型         | 约束/属性           | 描述             | 备注                                           |
| :------------- | :----------- | :------------------ | :--------------- | :--------------------------------------------- |
| `setting_key`  | VARCHAR(100) | PK                  | 设置键           | 例如 'default_trial_duration', 'max_upload_size' |
| `setting_value`| TEXT         | NOT NULL            | 设置值           |                                                |
| `description`  | VARCHAR(255) | NULLABLE            | 键的描述         |                                                |
| `updated_at`   | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 最后更新时间     |                                                |

### 18. `audit_logs` (操作审计日志表)

记录关键操作日志，用于审计和追踪。

| 字段名          | 类型         | 约束/属性                               | 描述                                | 备注                                                |
| :-------------- | :----------- | :-------------------------------------- | :---------------------------------- | :-------------------------------------------------- |
| `id`            | BIGINT       | PK, AUTO_INCREMENT                      | 日志ID                              |                                                     |
| `user_id`       | INT          | FK, NULLABLE (REFERENCES `users`(`id`))   | 操作用户ID (系统操作可为NULL)       |                                                     |
| `action`        | VARCHAR(100) | NOT NULL                                | 操作类型 (例如 'USER_LOGIN', 'CREATE_LICENSE') |                                                     |
| `target_type`   | VARCHAR(50)  | NULLABLE                                | 操作对象类型 (例如 'user', 'license_key') |                                                     |
| `target_id`     | VARCHAR(255) | NULLABLE                                | 操作对象ID                          |                                                     |
| `details`       | JSON         | NULLABLE                                | 操作详情 (例如请求参数，修改前后的值) |                                                     |
| `ip_address`    | VARCHAR(45)  | NULLABLE                                | 操作者IP地址                        |                                                     |
| `user_agent`    | TEXT         | NULLABLE                                | 操作者User Agent                    |                                                     |
| `status`        | ENUM('success', 'failure') | NOT NULL                    | 操作结果                            |                                                     |
| `failure_reason`| TEXT         | NULLABLE                                | 失败原因 (如果status='failure')     |                                                     |
| `created_at`    | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 操作时间                            |                                                     |

**索引建议:**
*   `user_id`
*   `action`
*   `target_type`, `target_id`
*   `created_at`

### 19. `notifications` (通知表)

用于站内信、系统通知等。

| 字段名        | 类型        | 约束/属性                               | 描述                                     | 备注                                                |
| :------------ | :---------- | :-------------------------------------- | :--------------------------------------- | :-------------------------------------------------- |
| `id`          | INT         | PK, AUTO_INCREMENT                      | 通知ID                                   |                                                     |
| `user_id`     | INT         | FK, NOT NULL (REFERENCES `users`(`id`))   | 接收通知的用户ID                         |                                                     |
| `type`        | VARCHAR(50) | NOT NULL                                | 通知类型 (例如 'new_reply', 'license_expiry') |                                                     |
| `title`       | VARCHAR(255)| NOT NULL                                | 通知标题                                 |                                                     |
| `content`     | TEXT        | NOT NULL                                | 通知内容                                 |                                                     |
| `is_read`     | BOOLEAN     | NOT NULL, DEFAULT FALSE                 | 是否已读                                 |                                                     |
| `read_at`     | TIMESTAMP   | NULLABLE                                | 阅读时间                                 |                                                     |
| `related_url` | VARCHAR(512)| NULLABLE                                | 相关链接 (点击通知后跳转的URL)           |                                                     |
| `created_at`  | TIMESTAMP   | DEFAULT CURRENT_TIMESTAMP               | 创建时间                                 |                                                     |

**索引建议:**
*   (`user_id`, `is_read`)
*   `type`

### 20. `user_bound_devices` (用户绑定设备表)

记录用户已绑定的设备信息，并跟踪其最后活跃状态和当前会话。用于实现最大绑定设备数限制、设备管理和在线状态跟踪。

| 字段名                 | 类型         | 约束/属性                               | 描述                                                                 | 备注                                                                                                |
| :--------------------- | :----------- | :-------------------------------------- | :------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------- |
| `id`                   | BIGINT       | PK, AUTO_INCREMENT                      | 绑定记录唯一标识符                                                   |                                                                                                     |
| `user_id`              | INT          | FK, NOT NULL (REFERENCES `users`(`id`))   | 用户ID                                                               |                                                                                                     |
| `device_id`            | VARCHAR(128) | NOT NULL                                | 设备唯一标识符                                                       | 客户端生成并保证唯一性。**用户维度内应唯一** (一个设备ID在一个用户下只能绑定一次)。                   |
| `device_alias`         | VARCHAR(100) | NULLABLE                                | 用户为设备设置的别名                                                 | 例如 “我的台式机”                                                                                   |
| `session_token`        | VARCHAR(255) | UNIQUE, NOT NULL                        | 当前活动会话的令牌                                                   | 用于严格控制同时在线和强制下线，例如 JWT 的 jti。                                                   |
| `ip_address`           | VARCHAR(45)  | NULLABLE                                | 最近一次使用该设备登录或活跃的IP地址                                 |                                                                                                     |
| `user_agent`           | TEXT         | NULLABLE                                | 最近一次使用该设备登录或活跃的客户端User Agent                       |                                                                                                     |
| `bound_at`             | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 设备绑定时间                                                         |                                                                                                     |
| `last_active_at`       | TIMESTAMP    | NOT NULL, INDEX                         | 设备最后活跃时间                                                     | 通过心跳接口或每次API请求时更新                                                                     |
| `created_at`           | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 记录创建时间                                                         |                                                                                                     |
| `updated_at`           | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间                                                         |                                                                                                     |

**索引建议:**
*   UNIQUE (`user_id`, `device_id`)
*   `user_id`
*   `session_token` (UNIQUE)
*   `last_active_at`

### 21. `user_device_unbind_logs` (用户设备解绑日志表)

记录用户解绑设备的操作历史，用于审计、统计和问题排查。

| 字段名                  | 类型         | 约束/属性                               | 描述                                                 | 备注                                                              |
| :---------------------- | :----------- | :-------------------------------------- | :--------------------------------------------------- | :---------------------------------------------------------------- |
| `id`                    | BIGINT       | PK, AUTO_INCREMENT                      | 日志唯一标识符                                       |                                                                   |
| `user_id`               | INT          | FK, NOT NULL (REFERENCES `users`(`id`))   | 用户ID                                               |                                                                   |
| `device_id`             | VARCHAR(128) | NOT NULL                                | 被解绑的设备ID                                       |                                                                   |
| `device_alias_at_unbind`| VARCHAR(100) | NULLABLE                                | 解绑时设备的别名                                     |                                                                   |
| `unbind_reason`         | VARCHAR(255) | NULLABLE                                | 解绑原因（例如：用户主动解绑，管理员操作）           |                                                                   |
| `license_type_code_at_unbind`| VARCHAR(50)  | NULLABLE                            | 解绑时用户激活的卡密类型代码 (e.g., `MONTHLY_STD`) | 用于判断解绑策略                                                  |
| `time_deducted_seconds` | INT          | NULLABLE                                | (针对时限卡) 因此次解绑扣除的时长 (秒)               | 例如 86400 (24小时)                                               |
| `is_permanent_unbind`   | BOOLEAN      | NOT NULL, DEFAULT FALSE                 | 是否为永久卡用户的解绑操作                           |                                                                   |
| `unbind_ip_address`     | VARCHAR(45)  | NULLABLE                                | 执行解绑操作时的IP地址                               |                                                                   |
| `created_at`            | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP               | 解绑操作发生时间                                     |                                                                   |

**索引建议:**
*   `user_id`
*   `device_id`
*   `created_at`

### 22. `leaderboards` (排行榜表)

存储预先计算好的各类排行榜数据。

| 字段名         | 类型                                                       | 约束/属性           | 描述                                     | 备注                                                                 |
| :------------- | :--------------------------------------------------------- | :------------------ | :--------------------------------------- | :------------------------------------------------------------------- |
| `id`             | INT                                                        | PK, AUTO_INCREMENT  | 排行榜记录唯一ID                         |                                                                      |
| `board_type`     | ENUM('song_plays', 'user_uploads_favorites', 'user_online_time') | NOT NULL, INDEX     | 榜单类型                                 | `song_plays`-歌曲弹奏次数, `user_uploads_favorites`-用户上传谱面被收藏总数, `user_online_time`-用户累计在线时长 |
| `period_type`    | ENUM('daily', 'weekly', 'monthly', 'total')                | NOT NULL, INDEX     | 榜单周期                                 | `daily`-日榜, `weekly`-周榜, `monthly`-月榜, `total`-总榜             |
| `period_key`     | VARCHAR(20)                                                | NOT NULL, INDEX     | 周期标识                                 | 例如: 日榜 YYYY-MM-DD, 周榜 YYYY-WW, 月榜 YYYY-MM, 总榜 'all_time'    |
| `rank`           | INT                                                        | NOT NULL            | 排名                                     |                                                                      |
| `entity_id`      | INT                                                        | NOT NULL, INDEX     | 实体ID                                   | 根据 `board_type`，可能为 `scores.id` 或 `users.id`                  |
| `score_value`    | BIGINT                                                     | NOT NULL            | 排序依据的数值                           | 例如弹奏次数、收藏数、在线秒数                                       |
| `display_data`   | JSON                                                       | NULLABLE            | 用于榜单显示的冗余数据                   | 例如歌曲名、用户名、头像等，减少查询JOIN                             |
| `updated_at`     | TIMESTAMP                                                  | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 此排名记录的最后更新时间                 |                                                                      |

**索引建议:**
*   UNIQUE (`board_type`, `period_type`, `period_key`, `rank`)
*   UNIQUE (`board_type`, `period_type`, `period_key`, `entity_id`)
*   `entity_id`

### 23. `song_play_logs` (歌曲弹奏日志表)

记录用户弹奏歌曲的行为。

| 字段名         | 类型         | 约束/属性                        | 描述             | 备注                               |
| :------------- | :----------- | :------------------------------- | :--------------- | :--------------------------------- |
| `id`             | BIGINT       | PK, AUTO_INCREMENT               | 日志ID           |                                    |
| `score_id`       | INT          | FK, NOT NULL (REFERENCES `scores`(`id`)), INDEX | 谱曲ID           |                                    |
| `user_id`        | INT          | FK, NULLABLE (REFERENCES `users`(`id`)), INDEX | 用户ID           | 匿名用户弹奏时可为NULL             |
| `played_at`      | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP, INDEX | 弹奏时间         |                                    |
| `device_id`      | VARCHAR(128) | NULLABLE                         | 设备ID           | 用于统计或分析，非强制             |
| `ip_address`     | VARCHAR(45)  | NULLABLE                         | IP地址           |                                    |
| `play_duration_seconds` | INT   | NULLABLE                         | 弹奏时长（秒）   | 可选，如果需要统计有效弹奏         |

**索引建议:**
*   `score_id`
*   `user_id`
*   `played_at`

### 24. `user_session_logs` (用户会话日志表)

记录用户的登录会话时长，用于统计在线时长。

| 字段名             | 类型         | 约束/属性                        | 描述             | 备注                                   |
| :----------------- | :----------- | :------------------------------- | :--------------- | :------------------------------------- |
| `id`                 | BIGINT       | PK, AUTO_INCREMENT               | 会话日志ID       |                                        |
| `user_id`            | INT          | FK, NOT NULL (REFERENCES `users`(`id`)), INDEX | 用户ID           |                                        |
| `session_start_at`   | TIMESTAMP    | NOT NULL, INDEX                  | 会话开始时间     |                                        |
| `session_end_at`     | TIMESTAMP    | NULLABLE, INDEX                  | 会话结束时间     | 用户登出或会话超时时记录               |
| `duration_seconds`   | INT          | NULLABLE                         | 会话时长（秒）   | 在会话结束时计算并填充                 |
| `device_id`          | VARCHAR(128) | NULLABLE                         | 设备ID           |                                        |
| `ip_address`         | VARCHAR(45)  | NULLABLE                         | IP地址           |                                        |
| `created_at`         | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP        | 记录创建时间     | 通常等于 `session_start_at`            |
| `updated_at`         | TIMESTAMP    | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间     | 主要在 `session_end_at` 更新时触发 |

**索引建议:**
*   `user_id`
*   `session_start_at`
*   `session_end_at`

## 关系图 (ERD) 概念

实体关系图 (Entity Relationship Diagram, ERD) 是一种用于可视化数据库结构的图表。它展示了数据库中的主要实体（通常对应数据库表）、实体的属性（通常对应表中的列）以及实体之间的关系（例如一对一、一对多、多对多）。

下面是“弹奏大师”项目数据库的ERD，使用 Mermaid 语法绘制，并对主要实体和关系进行了汉化注释。

```mermaid
erDiagram
    users ||--o{ license_keys : "激活 (activates)"
    users ||--o{ license_keys : "持有寄售 (holds consigned)"
    users ||--o{ resellers : "是代理 (is_a reseller)"
    users ||--o{ scores : "上传乐谱 (uploads scores)"
    users ||--o{ user_favorites : "收藏 (has favorites)"
    users ||--o{ user_keybindings : "拥有键位 (has keybindings)"
    users ||--o{ support_tickets : "提交工单 (submits tickets)"
    users ||--o{ ticket_replies : "回复工单 (replies to tickets)"
    users ||--o{ score_comments : "评论乐谱 (comments on scores)"
    users ||--o{ user_score_ratings : "评价乐谱 (rates scores)"
    users ||--o{ audit_logs : "执行操作 (performs action)"
    users ||--o{ notifications : "接收通知 (receives notifications)"
    users ||--o{ attachments : "上传附件 (uploads attachments)"
    users ||--o{ user_bound_devices : "绑定设备 (has bound devices)"
    users ||--o{ user_device_unbind_logs : "记录设备解绑 (logs unbind for device)"
    users ||--o{ reseller_discounts : "应用折扣规则 (applies discount for specific user)"
    users ||--o{ leaderboards : "上榜 (is_on_leaderboard_entity)"
    users ||--o{ song_play_logs : "弹奏歌曲 (plays_song)"
    users ||--o{ user_session_logs : "有会话 (has_session)"

    reseller_levels ||--o{ resellers : "定义级别 (defines level for)"
    reseller_levels ||--o{ reseller_discounts : "应用折扣规则 (applies discount for level)"

    license_types ||--o{ license_keys : "定义类型 (defines type for)"
    license_types ||--o{ reseller_licenses_quotas : "定义额度类型 (defines type for quota)"
    license_types ||--o{ reseller_discounts : "应用折扣规则 (applies discount for type)"

    resellers }o--|| users : "有上级代理 (has parent reseller)"
    resellers ||--o{ reseller_licenses_quotas : "拥有额度 (has quota for)"
    resellers ||--o{ commission_records : "赚取佣金 (earns commission)"
    resellers ||--o{ withdrawal_requests : "请求提现 (requests withdrawal)"

    reseller_levels {
        int id PK "级别ID (主键)"
        int level_value UK "级别数值 (唯一)"
        varchar name UK "级别名称 (唯一)"
        text description "描述"
        decimal upgrade_threshold_sales "升级销售额门槛"
        int upgrade_threshold_sub_resellers "升级下级代理数门槛"
        decimal default_commission_rate "默认直接佣金率"
        decimal default_sub_commission_rate "默认间接佣金率"
        decimal default_second_tier_sub_commission_rate "默认二级间接佣金率"
        boolean is_active "是否启用"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    resellers {
        int user_id PK "用户ID (外键 users.id)"
        int parent_reseller_user_id FK "上级代理用户ID (外键 users.id)"
        decimal commission_rate "特定用户直接销售佣金率"
        decimal balance "账户余额"
        int reseller_level_value FK "代理级别数值 (外键 reseller_levels.level_value)"
        int consignment_limit_count "最大寄售卡密数量"
        decimal consignment_limit_value "最大寄售卡密总价值"
        enum status "'active', 'inactive', 'frozen' (代理资格状态)"
        decimal total_sales_amount "累计销售总额"
        decimal total_commission_earned "累计获得佣金总额"
        text remarks "备注"
        timestamp created_at "成为代理时间"
        timestamp updated_at "信息更新时间"
    }

    reseller_discounts {
        int id PK "折扣规则ID (主键)"
        varchar description "描述"
        int reseller_level_value FK "适用代理级别数值 (外键)"
        int reseller_user_id FK "适用特定代理用户ID (外键)"
        int license_type_id FK "适用特定卡密类型ID (外键)"
        enum discount_type "'percentage', 'fixed_price' (折扣类型)"
        decimal discount_value "折扣值"
        int priority "优先级"
        datetime start_date "生效开始时间"
        datetime end_date "生效结束时间"
        boolean is_active "是否启用"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    license_types {
        int id PK "类型ID (主键)"
        varchar name UK "类型名称 (唯一)"
        varchar code UK "类型代码 (唯一, 程序用)"
        text description "描述"
        int duration_value "时长数值"
        enum duration_unit "'days', 'hours', 'minutes', 'permanent' (时长单位)"
        int max_concurrent_devices "最大同时在线设备数"
        decimal price "建议售价"
        decimal reseller_cost "代理成本"
        boolean is_active "是否启用"
        boolean is_publicly_available "是否公开给代理/用户"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    reseller_licenses_quotas {
        int id PK "额度记录ID (主键)"
        int reseller_user_id FK "代理用户ID (外键)"
        int license_type_id FK "卡密类型ID (外键)"
        int quota_available "可用额度"
        int quota_used "已用额度"
        timestamp last_refilled_at "最后补充时间"
        text notes "备注"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    license_keys {
        int id PK "卡密ID (主键)"
        varchar key_string UK "卡密字符串 (唯一)"
        int license_type_id FK "卡密类型ID (外键)"
        enum status "'available', 'used', 'expired', 'disabled', 'consigned_available' (状态)"
        int user_id FK "使用者ID (外键)"
        timestamp activated_at "激活时间"
        timestamp expires_at "过期时间"
        int issued_by_user_id FK "发放者ID (外键)"
        int consignment_reseller_id FK "寄售代理ID (外键 users.id)"
        enum settlement_status "'pending', 'settled', 'not_applicable' (结算状态)"
        varchar batch_id "批次号"
        int renewal_source_key_id FK "续期来源卡密ID (外键)"
        text notes "备注"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
    license_keys }o--|| users : "由...发放 (issued_by)"
    license_keys ||--o{ commission_records : "产生佣金 (generates_for)"
    license_keys }o--|| license_keys : "续期自 (renews)"

    categories ||--o{ scores : "包含乐谱 (contains scores)"
    categories }o--|| categories : "有父分类 (has parent category)"

    scores ||--o{ user_favorites : "被收藏于 (is_in favorites)"
    scores ||--o{ score_comments : "拥有评论 (has comments)"
    scores ||--o{ user_score_ratings : "被评价于 (is_rated_in)"
    scores }o--|| users : "由...审核 (approved_by)"
    scores ||--o{ leaderboards : "上榜 (is_on_leaderboard_entity)"
    scores ||--o{ song_play_logs : "被弹奏 (is_played_in_log)"

    support_tickets ||--o{ ticket_replies : "拥有回复 (has replies)"
    support_tickets }o--|| users : "分配给 (assigned_to)"

    ticket_replies ||--o{ attachments : "包含附件 (has attachments)"
    score_comments }o--|| score_comments : "回复父评论 (replies_to_parent)"

    user_bound_devices {
        bigint id PK "绑定记录ID (主键)"
        int user_id FK "用户ID (外键)"
        varchar device_id "设备唯一标识符 (用户内唯一)"
        varchar device_alias "设备别名"
        varchar session_token UK "当前会话令牌 (唯一)"
        varchar ip_address "IP地址"
        text user_agent "User Agent"
        timestamp bound_at "绑定时间"
        timestamp last_active_at "最后活跃时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    user_device_unbind_logs {
        bigint id PK "解绑日志ID (主键)"
        int user_id FK "用户ID (外键)"
        varchar device_id "设备ID"
        varchar device_alias_at_unbind "解绑时设备别名"
        varchar unbind_reason "解绑原因"
        varchar license_type_code_at_unbind "解绑时卡密类型代码"
        int time_deducted_seconds "扣除时长(秒)"
        boolean is_permanent_unbind "是否永久卡解绑"
        varchar unbind_ip_address "解绑IP"
        timestamp created_at "创建时间"
    }

    users {
        int id PK "用户ID (主键)"
        varchar username UK "用户名 (唯一)"
        varchar password_hash "密码哈希"
        varchar email UK "邮箱 (唯一)"
        varchar phone_number UK "手机号 (唯一)"
        varchar avatar_url "头像URL"
        varchar nickname "昵称"
        enum role "'admin', 'reseller', 'user' (角色)"
        enum status "'active', 'inactive', 'banned', 'pending_verification' (状态)"
        int max_concurrent_devices "最大同时在线设备数"
        enum creation_source "'registration', 'license_activation', 'admin_created' (创建来源)"
        varchar registration_ip "注册IP"
        varchar last_login_ip "最后登录IP"
        timestamp last_login_at "最后登录时间"
        timestamp email_verified_at "邮箱验证时间"
        timestamp phone_verified_at "手机验证时间"
        int permanent_license_unbind_count_current_month "永久卡当月解绑次数"
        varchar permanent_license_last_unbind_month "永久卡上次解绑月份"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
        timestamp deleted_at "软删除时间"
    }

    system_settings {
        varchar setting_key PK "设置键 (主键)"
        text setting_value "设置值"
        varchar description "描述"
        timestamp updated_at "更新时间"
    }

    attachments {
        int id PK "附件ID (主键)"
        varchar related_object_type "关联对象类型"
        int related_object_id "关联对象ID"
        int uploader_user_id FK "上传用户ID (外键)"
        varchar filename "原始文件名"
        varchar stored_filename "存储文件名"
        enum storage_type "'local', 's3', 'oss' (存储类型)"
        varchar path_or_url "路径或URL"
        varchar file_type "文件MIME类型"
        bigint file_size_bytes "文件大小(字节)"
        timestamp created_at "创建时间"
    }

    leaderboards {
        int id PK "排行榜记录ID (主键)"
        enum board_type "'song_plays', 'user_uploads_favorites', 'user_online_time' (榜单类型)"
        enum period_type "'daily', 'weekly', 'monthly', 'total' (榜单周期)"
        varchar period_key "周期标识"
        int rank "排名"
        int entity_id "实体ID (users.id 或 scores.id)"
        bigint score_value "排序分值"
        json display_data "冗余显示数据"
        timestamp updated_at "更新时间"
    }

    song_play_logs {
        bigint id PK "日志ID (主键)"
        int score_id FK "谱曲ID (外键 scores.id)"
        int user_id FK "用户ID (外键 users.id)"
        timestamp played_at "弹奏时间"
        varchar device_id "设备ID"
        varchar ip_address "IP地址"
        int play_duration_seconds "弹奏时长(秒)"
    }

    user_session_logs {
        bigint id PK "会话日志ID (主键)"
        int user_id FK "用户ID (外键 users.id)"
        timestamp session_start_at "会话开始时间"
        timestamp session_end_at "会话结束时间"
        int duration_seconds "会话时长(秒)"
        varchar device_id "设备ID"
        varchar ip_address "IP地址"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
```

## 总结

此数据库设计旨在为"弹奏大师"项目提供一个全面且可扩展的基础。在实际开发过程中，可能需要根据具体业务逻辑和性能测试结果进行调整和优化。例如，对于高频读写的表，可以考虑引入缓存机制；对于统计类数据，可以考虑使用定时任务进行预计算和聚合。