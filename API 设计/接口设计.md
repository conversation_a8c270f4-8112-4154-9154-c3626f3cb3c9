# 弹奏大师 - API 接口设计

本文档详细描述了"弹奏大师"项目的API接口设计，推荐使用 Python 的 FastAPI 或 Flask 框架实现。

## 设计原则

*   **RESTful风格**：遵循RESTful设计原则，使用标准的HTTP方法。
*   **模块化**：按功能模块和用户角色划分API，保持结构清晰。
*   **安全性**：所有涉及用户数据和操作的接口均需认证和授权。
*   **版本控制**：API应考虑版本化，例如 `/api/v1/...`。
*   **统一响应格式**：所有API响应应遵循统一的JSON结构，包含状态码、消息和数据。
    ```json
    // 成功响应示例
    {
        "code": 200, // 或自定义成功码，如0
        "message": "操作成功",
        "data": { ... } // 具体数据
    }

    // 失败响应示例
    {
        "code": 40001, // 自定义错误码
        "message": "请求参数错误: 用户名不能为空",
        "data": null
    }
    ```
*   **详细的错误处理**：提供明确的错误码和错误信息。
*   **文档化**：API应有完善的文档（如Swagger/OpenAPI）。

## 通用约定

*   **认证**：默认使用 JWT (JSON Web Tokens) 进行认证。JWT在用户登录或卡密激活成功后颁发，包含 `access_token` 和 `refresh_token`。
    *   `access_token`：用于访问受保护的API，有效期较短（例如1小时）。通过 HTTP Header `Authorization: Bearer <token>` 传递。
    *   `refresh_token`：用于在 `access_token` 过期后获取新的 `access_token`，有效期较长（例如7天或30天）。
*   **分页**：对于列表类型的API，支持分页参数：
    *   `page` (int, optional, default 1): 当前页码。
    *   `limit` (int, optional, default 20): 每页数量，可设置最大值（如100）。
    *   响应中应包含总记录数 `total_items` 和总页数 `total_pages`。
*   **排序**：对于列表类型的API，支持排序参数：
    *   `sort_by` (string, optional): 排序字段，例如 `created_at`。
    *   `order` (string, optional, enum: `asc`, `desc`, default `desc`): 排序方式。
*   **速率限制**：对关键接口（如登录、注册、卡密生成/验证）实施速率限制，防止恶意攻击。
*   **幂等性**：对于创建或修改操作的接口，应考虑幂等性设计，防止重复提交造成数据不一致（例如，使用客户端生成的唯一请求ID）。

## 接口详情

### 零、全局配置接口 (可选, 通常由管理员后台管理)

*   `GET /config/public`：获取前端应用所需的公共配置信息。
    *   响应：例如 `{"max_upload_size_mb": 10, "supported_midi_formats": [".mid", ".midi"], "site_name": "弹奏大师"}`

### 1. 认证接口 (`/api/v1/auth`)

#### 1.1 用户登录
*   `POST /login`
    *   描述：用户通过用户名密码或已激活并关联账户的卡密登录。**`device_id` 为必填项。** 登录时会检查设备绑定状态。
        *   如果设备已绑定：正常登录，更新设备活跃信息。
        *   如果设备未绑定且用户绑定名额未满：自动绑定该设备并登录。
        *   如果设备未绑定且用户绑定名额已满：登录失败，提示用户管理设备。
        *   **如果使用未激活的有效卡密，此接口将引导客户端至专门的激活账户接口。**
    *   请求体：
        ```json
        // 用户名密码登录
        {
            "login_type": "password", // "password" or "license"
            "username": "user1",
            "password": "hashed_or_plain_password", // 根据安全策略决定传输是否加密
            "device_id": "unique_device_identifier" // 必填
        }
        // 卡密登录
        {
            "login_type": "license",
            "key_string": "XXXX-XXXX-XXXX-XXXX",
            "device_id": "unique_device_identifier" // 必填
        }
        ```
    *   后端逻辑补充：
        1.  **对于 `login_type: "license"`**:
            *   校验卡密 `key_string` 的有效性（存在、状态为 `available` 或 `used`）。
            *   如果卡密有效且状态为 `available` (即未被激活使用过，也未关联任何 `user_id`)：
                *   返回特定响应 (例如 `200 OK` 但包含特殊 `action_required` 字段，或自定义状态码如 `403 Forbidden` 并附带错误码和消息)，指示客户端需要调用 `/api/v1/auth/activate-license-account` 接口。
                *   响应体示例:
                    ```json
                    {
                        "code": 40301, // 自定义错误码，表示需要激活
                        "message": "激活码有效但尚未激活，请先激活账户。",
                        "data": {
                            "action_required": "activate_license_account",
                            "key_string": "XXXX-XXXX-XXXX-XXXX" // 将卡密传回给客户端
                        }
                    }
                    ```
                *   **终止后续登录流程。**
            *   如果卡密有效且状态为 `used` 并已关联 `user_id`：继续正常登录流程，使用该 `user_id`。
            *   如果卡密无效、已过期或已禁用：返回 `401 Unauthorized`。
        2.  **对于 `login_type: "password"` 或已验证的卡密登录**:
            *   认证用户凭证 (用户名/密码，或卡密关联的用户)。获取 `user_id`。
            *   查询 `users` 表获取 `max_concurrent_devices`。
            *   查询 `user_bound_devices` 表获取该用户已绑定的设备列表 (`current_bound_devices`)。
            *   **设备检查与绑定逻辑**:
                *   `is_device_already_bound = current_bound_devices.find(d => d.device_id === request.device_id)`
                *   **If** `is_device_already_bound`:
                    *   登录成功。生成 `access_token`, `refresh_token`, `session_token`。
                    *   更新 `user_bound_devices` 中该设备的 `last_active_at`, `session_token`, `ip_address`, `user_agent`。
                    *   在 `user_session_logs` 中创建一条新的会话记录，包含 `user_id`, `session_start_at`=NOW(), `device_id`, `ip_address`。
                    *   `device_status_response = "current_device_active"`
                *   **Else** (新设备):
                    *   **If** `current_bound_devices.length < user.max_concurrent_devices`:
                        *   自动绑定新设备: 在 `user_bound_devices` 表中插入新记录 (包含 `user_id`, `device_id`, `session_token`, `ip_address`, `user_agent`, `bound_at`=NOW(), `last_active_at`=NOW())。可设置默认别名如 "新设备 [绑定日期时间]"。
                        *   登录成功。生成 `access_token`, `refresh_token`, `session_token`。
                        *   在 `user_session_logs` 中创建一条新的会话记录，包含 `user_id`, `session_start_at`=NOW(), `device_id`, `ip_address`。
                        *   `device_status_response = "new_device_auto_bound"`
                    *   **Else** (绑定名额已满):
                        *   登录失败。返回 `429 Too Many Requests` 或 `403 Forbidden`，错误码 `MAX_BOUND_DEVICES_REACHED`。
                        *   提示用户：“已达到最大绑定设备数，请先解绑其他设备。”
                        *   **终止后续登录流程。**
    *   响应 (成功 `200 OK` - 针对已验证用户)：
        ```json
        {
            "access_token": "your_jwt_access_token",
            "refresh_token": "your_jwt_refresh_token",
            "session_token": "your_unique_session_token", // 用于心跳和特定会话登出
            "token_type": "Bearer",
            "expires_in": 3600, // access_token 有效期 (秒)
            "user_info": {
                "id": 1,
                "username": "user1",
                "email": "<EMAIL>",
                "role": "user",
                "avatar_url": "...",
                "nickname": "..."
            },
            "license_info": { // 如果是卡密登录或用户已有激活卡密
                "key_string": "XXXX-XXXX-XXXX-XXXX",
                "license_type_code": "MONTHLY_STD",
                "license_type_name": "标准月卡",
                "expires_at": "2024-12-31T23:59:59Z",
                "status": "used"
            },
            "device_status": "current_device_active" // 或 "new_device_auto_bound"
        }
        ```
    *   错误响应：`400 Bad Request` (参数错误, 如device_id缺失), `401 Unauthorized` (凭证无效/用户不存在/密码错误/卡密无效或已使用/账户状态异常), `403 Forbidden` (如上述需要激活的场景，或账户被禁止登录，或错误码 `MAX_BOUND_DEVICES_REACHED`), `429 Too Many Requests` (也可用于 `MAX_BOUND_DEVICES_REACHED`)。

#### 1.2 用户注册
*   `POST /register`
    *   描述：新用户注册 (如果系统允许用户自由注册)。
    *   请求体：
        ```json
        {
            "username": "newuser",
            "password": "strong_password",
            "email": "<EMAIL>", // 可选，根据业务需求
            "phone_number": "13800138000", // 可选
            "verification_code": "123456" // 如果需要邮箱或短信验证码
        }
        ```
    *   响应 (成功 `201 Created`): 返回用户信息和JWT令牌，类似登录响应。
    *   错误响应：`400 Bad Request` (参数校验失败，如用户名已存在、邮箱格式错误、密码强度不足), `409 Conflict` (用户名或邮箱已存在)。

#### 1.3 刷新令牌
*   `POST /refresh-token`
    *   描述：使用 `refresh_token` 获取新的 `access_token`。
    *   请求体：
        ```json
        {
            "refresh_token": "your_valid_refresh_token"
        }
        ```
    *   响应 (成功 `200 OK`)：
        ```json
        {
            "access_token": "new_jwt_access_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        ```
    *   错误响应：`401 Unauthorized` (refresh_token无效或过期)。

#### 1.4 用户登出
*   `POST /logout` (需要JWT认证 - `access_token`)
*   描述：用户登出。后端应将当前 `access_token` 或 `refresh_token` 加入黑名单（如果采用此机制），并从 `user_bound_devices` 表中清除对应设备的 `session_token`。同时，更新对应的 `user_session_logs` 记录。
*   请求体：
    ```json
    {
        "refresh_token": "optional_refresh_token_to_invalidate", // 使 refresh_token 失效
        "session_token": "current_session_token" // 必填，用于定位要登出的设备会话
    }
    ```
*   后端逻辑补充：
    1.  根据 `session_token` 从 `user_bound_devices` 表中找到对应的设备记录。
    2.  清除该记录的 `session_token` (将其设为 NULL 或空字符串)。
    3.  查找 `user_session_logs` 中与当前用户 (`user_id`) 和设备 (`device_id` from `user_bound_devices`) 相关的最新一条未结束的会话。
    4.  更新该会话记录的 `session_end_at` 为当前时间，并计算 `duration_seconds`。
    5.  将 `access_token` 和 (如果提供了) `refresh_token` 加入黑名单。
*   响应 (成功 `200 OK` 或 `204 No Content`)。
    *   错误响应：`401 Unauthorized`。

#### 1.5 请求密码重置
*   `POST /password/request-reset`
    *   描述：用户请求重置密码，通常通过邮箱发送重置链接或验证码。
    *   请求体：
        ```json
        {
            "email": "<EMAIL>"
        }
        ```
    *   响应 (成功 `200 OK`): `{"message": "密码重置邮件已发送，请查收"}`
    *   错误响应：`400 Bad Request`, `404 Not Found` (邮箱不存在)。

#### 1.6 执行密码重置
*   `POST /password/reset`
    *   描述：用户通过邮件中的令牌或验证码设置新密码。
    *   请求体：
        ```json
        {
            "reset_token": "some_reset_token_from_email", // 或 verification_code
            "new_password": "new_strong_password"
        }
        ```
    *   响应 (成功 `200 OK`): `{"message": "密码已成功重置"}`
    *   错误响应：`400 Bad Request` (令牌无效/过期，密码不符合要求)。

#### 1.7 发送验证码 (邮箱/短信)
*   `POST /verification-code/send`
    *   描述：发送用于注册、登录、操作验证等的验证码。
    *   请求体：
        ```json
        {
            "type": "email", // "email" or "sms"
            "recipient": "<EMAIL>", // 或手机号
            "purpose": "register" // "register", "login", "password_reset", "action_confirm"
        }
        ```
    *   响应 (成功 `200 OK`): `{"message": "验证码已发送"}`
    *   错误响应：`400 Bad Request`, `429 Too Many Requests` (发送频率限制)。

#### 1.8 会话心跳
*   `POST /heartbeat` (需要JWT认证 - `access_token`)
    *   描述：客户端定期调用此接口以保持用户会话活跃，更新 `user_bound_devices` 表中对应设备会话的 `last_active_at` 时间。
    *   请求体：
        ```json
        {
            "session_token": "current_session_token" // 从登录响应中获取, 必填
        }
        ```
    *   后端逻辑：
        1.  验证 `access_token`。
        2.  根据请求体中的 `session_token` 和 `access_token` 中的用户信息 (user_id)，找到 `user_bound_devices` 表中的记录。
        3.  更新该记录的 `last_active_at` 为当前时间。
        4.  (可选，如果会话超时逻辑依赖此更新) 同时，可以考虑更新 `user_session_logs` 中对应会话的 `session_end_at` 为预期超时时间或保持活动状态。更常见的做法是，如果长时间没有心跳，则后台任务将标记会话结束。
    *   响应 (成功 `200 OK` 或 `204 No Content`)。
    *   错误响应：`401 Unauthorized`, `404 Not Found` (会话不存在或已失效)。

#### 1.9 (新增) 激活激活码并创建/关联账户
*   `POST /api/v1/auth/activate-license-account`
    *   描述: 当用户通过 `POST /login` 使用一个有效的、但尚未关联任何用户的激活码时，客户端将被引导调用此接口来完成账户的创建并与该激活码绑定。**此操作也会绑定当前设备。**
    *   请求体:
        ```json
        {
            "key_string": "XXXX-XXXX-XXXX-XXXX", // 从 /login 引导响应中获取
            "username": "newuser_from_license",
            "password": "strong_password_for_new_account",
            "email": "<EMAIL>", // 可选
            "phone_number": "optional_phone_number", // 可选
            "device_id": "unique_device_identifier" // 必填，用于设备绑定
        }
        ```
    *   后端逻辑:
        1.  校验 `key_string`：确保卡密存在、状态为 `available` 或 `consigned_available`，且 `user_id` 为 NULL。如果无效，返回 `400 Bad Request` 或 `404 Not Found`。
        2.  校验 `username`：确保唯一性。如果已存在，返回 `409 Conflict`。
        3.  校验 `password`：确保符合强度要求。
        4.  校验 `email` (如果提供)：确保格式正确且唯一 (如果系统要求邮箱唯一)。
        5.  校验 `phone_number` (如果提供)：确保格式正确且唯一 (如果系统要求手机号唯一)。
        6.  创建新用户：在 `users` 表中插入新记录，`creation_source` 设置为 `license_activation`。获取新 `user_id`。
        7.  **处理卡密激活与结算（针对寄售）**:
            *   获取卡密详细信息，包括 `license_keys.consignment_reseller_id` 和 `license_keys.license_type_id`。
            *   **If `license_keys.consignment_reseller_id` IS NOT NULL and `license_keys.status` IS `consigned_available`**:
                *   此为寄售卡密激活。
                *   根据 `license_keys.license_type_id` 和 `consignment_reseller_id`（代理ID）查询适用的 `reseller_discounts` 规则，或回退到 `license_types.reseller_cost`，计算此卡密的实际拿货成本 (cost_price)。
                *   从该代理ID (`consignment_reseller_id`) 对应的 `resellers.balance` 中扣除 `cost_price`。
                    *   **如果余额不足**：此激活操作理论上应失败，或进入待处理状态，或通知管理员。具体策略需确定（例如：允许激活但标记代理欠费）。为简化，此处假设余额充足或允许欠费标记。
                *   更新 `license_keys.settlement_status` 为 `settled` (如果成功扣款) 或 `pending_settlement_due_to_insufficient_funds` (如果余额不足但允许激活)。
        8.  更新卡密基础信息：
            *   将 `license_keys.user_id` 更新为新创建用户的 ID。
            *   将 `license_keys.status` 更新为 `used`。
            *   设置 `license_keys.activated_at` 为当前时间。
            *   根据关联的 `license_types` 表中的时长配置计算并设置 `license_keys.expires_at`。
        9.  **触发佣金计算 (如果适用)**:
            *   如果 `license_keys.consignment_reseller_id` (即销售代理 `source_reseller_user_id`) 存在:
                *   **确定佣金基准**: `commission_base_amount` 通常是此销售代理对此卡密的拿货成本。
                *   **直接佣金 (平台给销售代理的额外奖励，可选)**:
                    *   如果平台配置了给销售代理的直接销售奖励佣金率 (例如在 `reseller_levels` 中有 `direct_commission_rate_for_own_sale`)，则计算并记录 `commission_type='direct'` 的佣金给 `source_reseller_user_id`。
                *   **一级间接佣金 (给L2)**:
                    *   找到销售代理 (`source_reseller_user_id`) 的上级 `parent_reseller_user_id` (我们称其为 L2 代理)。
                    *   如果 L2 代理存在，获取 L2 代理所在 `reseller_levels` 的 `default_sub_commission_rate`。
                    *   计算佣金 `amount = commission_base_amount * default_sub_commission_rate`。
                    *   在 `commission_records` 表中为 L2 代理创建一条记录:
                        *   `beneficiary_reseller_user_id` = L2 代理ID
                        *   `source_license_id` = 当前激活的卡密ID
                        *   `source_reseller_user_id` = 销售代理ID (L3)
                        *   `commission_amount` = 计算出的佣金
                        *   `commission_rate` = 使用的 `default_sub_commission_rate`
                        *   `commission_base_amount` = 拿货成本
                        *   `commission_type` = `'indirect_level_1'`
                        *   `depth_from_source` = 1
                        *   `status` = `'pending'`
                *   **二级间接佣金 (给L1)**:
                    *   找到 L2 代理的上级 `parent_reseller_user_id` (我们称其为 L1 代理)。
                    *   如果 L1 代理存在，获取 L1 代理所在 `reseller_levels` 的 `default_second_tier_sub_commission_rate`。
                    *   计算佣金 `amount = commission_base_amount * default_second_tier_sub_commission_rate`。
                    *   在 `commission_records` 表中为 L1 代理创建一条记录 (类似L2的记录，但 `commission_type` = `'indirect_level_2'`, `depth_from_source` = 2)。
        10. **绑定设备**:
            *   查询新用户的 `max_concurrent_devices` (通常默认为卡密类型定义的，或者用户表的默认值)。
            *   在 `user_bound_devices` 表中为新用户插入当前设备记录 (包含 `user_id`, `device_id`, `bound_at`=NOW(), `last_active_at`=NOW())。**注意：此时不生成 session_token，因为用户还未正式登录。**
        11. 提示用户账户创建成功，让用户使用新凭证通过 `/login` 接口登录 (登录时会生成 session_token)。
    *   响应 (成功 `201 Created`):
        ```json
        {
            "message": "账户创建成功并已绑定激活码，请使用新账户信息登录。",
            "user_info": {
                "id": 123, // 新用户ID
                "username": "newuser_from_license"
            },
            "license_info": {
                "key_string": "XXXX-XXXX-XXXX-XXXX",
                "license_type_code": "MONTHLY_STD", // 从 license_types 表获取
                "license_type_name": "标准月卡", // 从 license_types 表获取
                "expires_at": "YYYY-MM-DDTHH:MM:SSZ" // 卡密过期时间
            }
        }
        ```
    *   错误响应：`400 Bad Request` (参数校验失败，如卡密无效、用户名格式错误、密码太弱), `404 Not Found` (卡密不存在), `409 Conflict` (用户名或邮箱已存在), `500 Internal Server Error` (数据库操作失败等)。

### 2. 用户接口 (`/api/v1/users`) - 需要JWT认证

#### 2.1 获取当前用户信息
*   `GET /me`
    *   描述：获取当前登录用户的详细信息。
    *   响应 (成功 `200 OK`)：包含用户ID、用户名、邮箱、角色、头像、昵称、账户状态、会员信息等。
        ```json
        {
            "id": 1,
            "username": "user1",
            "email": "<EMAIL>",
            "phone_number": "138...",
            "avatar_url": "...",
            "nickname": "Piano Master",
            "role": "user",
            "status": "active",
            "email_verified_at": "...",
            "created_at": "...",
            "license_info": { // 当前激活的卡密信息
                "key_string": "XXXX-XXXX-XXXX-XXXX",
                "license_type_code": "MONTHLY_STD",
                "license_type_name": "标准月卡",
                "activated_at": "...",
                "expires_at": "..."
            }
        }
        ```

#### 2.2 更新当前用户信息
*   `PUT /me`
    *   描述：更新当前用户的个人信息（如昵称、头像；邮箱、手机号更新可能需要二次验证）。
    *   请求体：
        ```json
        {
            "nickname": "New Nickname",
            "avatar_url": "new_avatar_link.jpg"
            // "email": "<EMAIL>", // 更新邮箱可能需要独立接口或验证流程
            // "phone_number": "139..."
        }
        ```
    *   响应 (成功 `200 OK`)：更新后的用户信息。
    *   错误响应：`400 Bad Request` (参数校验失败)。

#### 2.3 修改当前用户密码
*   `PUT /me/password`
    *   描述：当前登录用户修改自己的密码。
    *   请求体：
        ```json
        {
            "current_password": "old_password",
            "new_password": "new_strong_password"
        }
        ```
    *   响应 (成功 `200 OK` 或 `204 No Content`): `{"message": "密码修改成功"}`
    *   错误响应：`400 Bad Request` (当前密码错误, 新密码不符合要求)。

#### 2.4 绑定/修改邮箱
*   `POST /me/email/bind` (发送验证码到新邮箱)
*   `PUT /me/email` (使用验证码确认修改)

#### 2.5 绑定/修改手机号
*   `POST /me/phone/bind` (发送验证码到新手机号)
*   `PUT /me/phone` (使用验证码确认修改)

#### 2.6 查看当前用户的卡密信息
*   `GET /me/license`
    *   描述：查看当前用户已激活的卡密详情和历史激活记录。
    *   响应 (成功 `200 OK`)：
        ```json
        {
            "active_license": {
                "key_string": "XXXX-XXXX-XXXX-XXXX",
                "license_type_code": "MONTHLY_STD",
                "license_type_name": "标准月卡",
                "activated_at": "2023-10-01T10:00:00Z",
                "expires_at": "2023-11-01T10:00:00Z"
                // duration_days 已移除, 时长信息在 license_types 表
            },
            "history": [
                {
                    "key_string": "YYYY-YYYY-YYYY-YYYY",
                    "license_type_code": "TRIAL_7D",
                    "license_type_name": "7天体验卡",
                    "activated_at": "2023-09-28T10:00:00Z",
                    "expires_at": "2023-10-01T10:00:00Z",
                    "status": "expired"
                }
            ]
        }
        ```

#### 2.7 用户使用新卡密激活/续期
*   `POST /me/license/activate` (或 `/me/license/renew`)
    *   描述：用户输入新的卡密字符串进行激活或为当前账户续期。续期时，新卡密的有效期将在原卡密到期时间基础上进行叠加。
    *   请求体：
        ```json
        {
            "key_string": "YYYY-YYYY-YYYY-YYYY"
        }
        ```
    *   后端逻辑补充 (针对卡密激活部分，续期逻辑保持不变):
        1.  获取当前登录用户的 `user_id`。
        2.  校验 `key_string`：确保卡密存在，状态为 `available` 或 `consigned_available`，且 `user_id` 为 NULL (如果是首次激活该卡密)。如果卡密已有关联 `user_id` 但不等于当前用户，则报错。
        3.  **处理卡密激活与结算（针对寄售）**:
            *   获取卡密详细信息，包括 `license_keys.consignment_reseller_id` 和 `license_keys.license_type_id`。
            *   **If `license_keys.consignment_reseller_id` IS NOT NULL and `license_keys.status` IS `consigned_available`**:
                *   此为寄售卡密激活。
                *   根据 `license_keys.license_type_id` 和 `consignment_reseller_id`（代理ID）查询适用的 `reseller_discounts` 规则，或回退到 `license_types.reseller_cost`，计算此卡密的实际拿货成本 (cost_price)。
                *   从该代理ID (`consignment_reseller_id`) 对应的 `resellers.balance` 中扣除 `cost_price`。
                    *   **如果余额不足**：此激活操作理论上应失败，或进入待处理状态，或通知管理员。具体策略需确定。
                *   更新 `license_keys.settlement_status` 为 `settled` (如果成功扣款) 或其他相应状态。
        4.  更新卡密基础信息：
            *   将 `license_keys.user_id` 更新为当前登录用户的 ID。
            *   将 `license_keys.status` 更新为 `used`。
            *   设置 `license_keys.activated_at` 为当前时间。
            *   根据关联的 `license_types` 表中的时长配置计算并设置 `license_keys.expires_at`。对于续期，则在原 `expires_at` 基础上叠加。
        5.  **触发佣金计算 (如果适用)**:
            *   此处的佣金计算逻辑与 `/api/v1/auth/activate-license-account` 中第9步的佣金计算逻辑完全相同。
            *   销售代理 (`source_reseller_user_id`) 同样是 `license_keys.consignment_reseller_id` (如果是寄售卡被激活) 或 `license_keys.issued_by_user_id` (如果是代理购买后首次被其客户激活，需确认此场景是否也触发向上佣金)。通常，佣金是基于寄售代理的销售行为。
        6.  (可选) 如果卡密类型发生变化，可能需要更新用户的 `max_concurrent_devices` 等属性。
    *   响应 (成功 `200 OK`)：包含新的JWT (如果卡密类型变化导致权限变更) 和更新后的卡密信息。
        ```json
        {
            "message": "卡密激活成功！",
            "license_info": {
                "key_string": "YYYY-YYYY-YYYY-YYYY",
                "license_type_code": "YEARLY_STD",
                "license_type_name": "标准年卡",
                "expires_at": "2025-11-01T10:00:00Z"
            }
            // 可选: 返回新的 access_token 如果权限有变动
        }
        ```
    *   错误响应：`400 Bad Request` (卡密格式错误), `404 Not Found` (卡密不存在), `409 Conflict` (卡密已被使用/已过期/类型不匹配无法续期等)。

#### 2.8 用户键位备份接口 (`/me/keybindings`)
*   `GET /me/keybindings`：获取用户所有键位备份列表。
    *   响应 (成功 `200 OK`)：键位备份对象列表。
*   `POST /me/keybindings`：创建新的键位备份。
    *   请求体：`{"binding_name": "My Awesome Setup", "binding_data": {"key_A": "C4", ...}, "device_type": "PC"}`
    *   响应 (成功 `201 Created`)：新创建的键位备份对象。
*   `GET /me/keybindings/{binding_id}`：获取指定ID的键位备份详情。
*   `PUT /me/keybindings/{binding_id}`：更新指定键位备份。
    *   请求体：同创建，但包含所有字段或仅需更新的字段。
    *   响应 (成功 `200 OK`)：更新后的键位备份对象。
*   `DELETE /me/keybindings/{binding_id}`：删除指定键位备份。
    *   响应 (成功 `204 No Content`)。
*   `POST /me/keybindings/{binding_id}/set-default`：将指定键位备份设为默认。

#### 2.9 用户收藏谱曲接口 (`/me/favorites/scores`)
*   `GET /me/favorites/scores`：获取用户收藏的谱曲列表 (分页、排序)。
    *   查询参数：`page`, `limit`, `sort_by` (`title`, `created_at`), `order`。
    *   响应 (成功 `200 OK`)：谱曲对象列表，包含谱曲基本信息。
*   `POST /me/favorites/scores/{score_id}`：收藏指定谱曲。
    *   响应 (成功 `201 Created` 或 `200 OK` 如果已收藏)。
    *   错误响应：`404 Not Found` (谱曲不存在)。
*   `DELETE /me/favorites/scores/{score_id}`：取消收藏指定谱曲。
    *   响应 (成功 `204 No Content`)。

#### 2.10 用户谱曲评分与评论 (`/me/scores/{score_id}/rating`)
*   `POST /me/scores/{score_id}/rating`: 用户对谱曲进行评分/评论。
    *   请求体：`{"rating": 5, "comment": "太棒了！"}` (rating 1-5)
    *   响应 (成功 `201 Created` 或 `200 OK` 如果是更新自己的评分)。
*   `PUT /me/scores/{score_id}/rating`: 用户修改自己的评分/评论。
*   `DELETE /me/scores/{score_id}/rating`: 用户删除自己的评分/评论。
*   `GET /me/ratings`: 获取用户所有的评分/评论记录。

#### 2.11 用户上传历史
*   `GET /me/uploads/scores`：获取当前用户上传的谱曲列表 (分页、排序、筛选状态)。

#### 2.12 用户通知列表
*   `GET /me/notifications`
    *   描述：获取当前用户的通知列表 (分页)。
    *   查询参数: `page`, `limit`, `is_read` (boolean)
    *   响应 (成功 `200 OK`): 通知对象列表。
*   `POST /me/notifications/mark-all-as-read`
    *   描述：将所有未读通知标记为已读。
*   `POST /me/notifications/{notification_id}/mark-as-read`
    *   描述：将指定通知标记为已读。
*   `DELETE /me/notifications/{notification_id}`
    *   描述：删除指定通知。

#### 2.13 用户设备管理接口 (`/me/devices`) - 新增

*   `GET /me/devices`
    *   描述：获取当前用户已绑定的设备列表。
    *   响应 (成功 `200 OK`):
        ```json
        {
          "data": [
            {
              "device_id": "unique_device_identifier_1",
              "device_alias": "我的笔记本",
              "bound_at": "2025-06-01T10:00:00Z",
              "last_active_at": "2025-06-01T14:00:00Z",
              "current_session_active": true // 基于 session_token 是否有效判断
            },
            {
              "device_id": "unique_device_identifier_2",
              "device_alias": "办公室电脑",
              "bound_at": "2025-05-20T08:30:00Z",
              "last_active_at": "2025-05-30T18:00:00Z",
              "current_session_active": false
            }
          ],
          "meta": {
            "current_bound_count": 2,
            "max_bound_count": 3
          }
        }
        ```

*   `PUT /me/devices/{deviceId}`
    *   描述：修改用户指定绑定设备的别名。
    *   URL参数: `{deviceId}` - 要修改的设备ID (来自 `user_bound_devices` 表的 `device_id` 字段)。
    *   请求体：
        ```json
        {
          "device_alias": "客厅电脑"
        }
        ```
    *   响应 (成功 `200 OK`): 更新后的设备信息。
        ```json
        {
          "device_id": "unique_device_identifier_1",
          "device_alias": "客厅电脑",
          "bound_at": "2025-06-01T10:00:00Z",
          "last_active_at": "2025-06-01T14:00:00Z"
        }
        ```
    *   错误响应：`400 Bad Request` (别名格式错误), `403 Forbidden` (非本人设备), `404 Not Found` (设备未绑定)。

*   `DELETE /me/devices/{deviceId}/unbind`
    *   描述：用户解绑指定的设备。
    *   URL参数: `{deviceId}` - 要解绑的设备ID。
    *   后端逻辑:
        1.  验证 `deviceId` 是否属于当前用户并且已绑定。
        2.  查询用户当前已绑定的设备数量。如果这是最后一个绑定设备且用户有有效的付费许可，则禁止解绑 (返回 `400 Bad Request`，错误码 `CANNOT_UNBIND_LAST_DEVICE_WITH_ACTIVE_LICENSE`)。
        3.  获取用户当前激活的有效卡密信息 (`license_keys` -> `license_types`)，判断是永久卡还是有时限卡。
        4.  **对于有时限卡**:
            *   从 `license_keys` 表找到用户当前激活的有效卡密。
            *   将其 `expires_at` 减去 24 小时。扣除后的 `expires_at` 不能早于当前时间 (如果早于，则设为当前时间)。
            *   记录扣除的时长到 `user_device_unbind_logs`。
        5.  **对于永久卡** (`license_types.duration_unit == 'permanent'`):
            *   获取当前月份 (YYYY-MM)。
            *   如果 `users.permanent_license_last_unbind_month` 不是当前月份，则将 `users.permanent_license_unbind_count_current_month` 重置为 0。
            *   检查 `users.permanent_license_unbind_count_current_month` 是否已达到 1 次。
            *   若未达到上限: 允许解绑；`users.permanent_license_unbind_count_current_month` 加 1；更新 `users.permanent_license_last_unbind_month` 为当前月份。
            *   若已达到上限: 解绑失败，返回 `400 Bad Request`，错误码 `PERMANENT_UNBIND_LIMIT_REACHED`。
        6.  在 `user_device_unbind_logs` 中记录解绑操作。
        7.  从 `user_bound_devices` 表中删除该设备记录。
    *   响应 (成功 `200 OK` 或 `204 No Content`):
        ```json
        {
          "message": "设备解绑成功。",
          "time_deducted_message": "您的账户时长已扣除24小时。" // (仅当有时限卡扣时显示)
        }
        ```
    *   错误响应：`400 Bad Request` (如 `PERMANENT_UNBIND_LIMIT_REACHED`, `CANNOT_UNBIND_LAST_DEVICE_WITH_ACTIVE_LICENSE`), `403 Forbidden`, `404 Not Found`。

### 3. 谱曲接口 (`/api/v1/scores`)

#### 3.1 获取谱曲列表 (公开)
*   `GET /`
    *   描述：获取公开的、已审核的谱曲列表。
    *   查询参数：
        *   `page`, `limit`, `sort_by` (`created_at`, `view_count`, `favorite_count`, `title`, `difficulty`), `order`
        *   `category_id` (int): 分类ID。
        *   `category_slug` (string): 分类slug。
        *   `search` (string): 关键词搜索 (标题, 描述, 标签)。
        *   `difficulty` (enum: `beginner`, `easy`, `medium`, `hard`, `expert`): 难度筛选。
        *   `tags` (string, comma-separated): 标签筛选, 例如 `pop,piano`。
        *   `is_premium_only` (boolean): 是否仅看付费曲谱。
    *   响应 (成功 `200 OK`)：谱曲对象列表，包含分页信息。
        每个谱曲对象包含：`id`, `title`, `description_snippet`, `uploader_info` (`username`, `avatar_url`), `category_name`, `type`, `difficulty`, `view_count`, `favorite_count`, `average_rating`, `comment_count`, `cover_image_url`, `is_premium_only`, `created_at`。

#### 3.2 获取指定谱曲详情 (公开)
*   `GET /{score_id}`
    *   描述：获取指定谱曲的详细信息。如果是付费谱曲且用户未付费/登录，可能只返回部分信息或提示。
    *   响应 (成功 `200 OK`)：谱曲完整对象，包括 `txt_content` (如果用户有权访问) 或 MIDI 文件信息。
        *   如果 `type` 是 `MIDI_REF`，且用户有权下载，提供 `midi_download_url`。
        *   包含上传者信息、分类信息、标签、完整描述、评分统计、评论列表 (分页)。
    *   错误响应：`404 Not Found` (谱曲不存在或未审核通过且非上传者)。
    *   **注意**：此接口被调用时应增加 `view_count`。

#### 3.3 获取谱曲的 TXT 内容 (需要权限)
*   `GET /{score_id}/content`
    *   描述：获取谱曲的 TXT 内容。需要用户登录，且如果是付费谱曲，用户需要已付费。
    *   响应 (成功 `200 OK`): `{"txt_content": "谱曲内容..."}`
    *   错误响应: `401 Unauthorized`, `403 Forbidden` (无权访问该谱曲内容), `404 Not Found`。

#### 3.4 下载谱曲文件 (MIDI) (需要权限)
*   `GET /{score_id}/download`
    *   描述：下载谱曲对应的MIDI文件（如果 `type='MIDI_REF'`）。需要用户登录，且如果是付费谱曲，用户需要已付费。
    *   响应：直接返回文件流 (`application/midi`) 或包含临时下载链接的JSON。
    *   错误响应：`401 Unauthorized`, `403 Forbidden`, `404 Not Found` (谱曲或文件不存在)。
    *   **注意**：此接口被调用时应增加 `download_count`。

#### 3.5 用户上传谱曲 (需要JWT认证)

*   **通用上传流程**：
    1.  客户端先请求一个预签名URL（如果使用对象存储如S3/OSS）或直接准备上传。
    2.  客户端上传文件 (TXT 内容或 MIDI 文件)。
    3.  客户端调用服务端接口，传递文件信息（URL或内容）和其他元数据（标题、描述等）。

*   `POST /uploads/txt` (或者统一为 `POST /uploads`，通过参数区分类型)
    *   描述：用户上传TXT格式的谱曲。
    *   请求体 (form-data 或 JSON)：
        ```json
        {
            "title": "My New TXT Score",
            "description": "An awesome piece.",
            "category_id": 1, // 或 category_slug
            "txt_content": "C4 D4 E4...",
            "difficulty": "medium",
            "tags": ["original", "pop"],
            "cover_image_url": "optional_link_to_cover.jpg",
            "status": "private" // "private" (仅自己可见), "pending" (提交审核)
        }
        ```
    *   响应 (成功 `201 Created` 或 `202 Accepted` 如果需要异步处理/审核)：新创建的谱曲对象 (或任务ID)。
    *   错误响应：`400 Bad Request` (参数校验失败), `413 Payload Too Large` (内容过大)。

*   `POST /uploads/midi` (或者统一为 `POST /uploads`)
    *   描述：用户上传MIDI文件，服务器端处理（如转换、存储）。
    *   请求体 (multipart/form-data)：
        *   `midi_file`: MIDI文件本身。
        *   `title` (string)
        *   `description` (string, optional)
        *   `category_id` (int)
        *   `difficulty` (enum, optional)
        *   `tags` (string, comma-separated, optional)
        *   `cover_image_file` (file, optional): 封面图片文件。
        *   `status` (string, default `pending`): `private` 或 `pending`。
    *   后端逻辑：
        1.  验证文件类型和大小。
        2.  保存MIDI文件到指定位置（本地或云存储），文件名建议UUID化，记录原始文件名。
        3.  (可选) 调用MIDI转TXT工具，将结果存入 `scores.txt_content`。如果转换失败，可以只保存MIDI引用。
        4.  在 `scores` 表中创建记录，`type='MIDI_REF'`，存储 `midi_path_or_url` 和 `original_midi_filename`。
    *   响应 (成功 `201 Created` 或 `202 Accepted`)：新创建的谱曲对象 (或任务ID)。
    *   错误响应：`400 Bad Request`, `413 Payload Too Large`, `500 Internal Server Error` (MIDI处理失败)。

*   `POST /uploads/pre-signed-url` (如果使用对象存储)
    *   描述：请求一个用于上传MIDI文件或封面图的预签名URL。
    *   请求体: `{"filename": "my_score.mid", "content_type": "audio/midi"}`
    *   响应: `{"upload_url": "presigned_s3_url", "access_url": "final_access_url_after_upload"}`
    *   客户端拿到 `upload_url` 后自行上传文件，上传成功后，再调用 `/uploads/midi/finalize` 或类似接口，传递 `access_url` 和其他元数据。

#### 3.6 更新用户自己上传的谱曲信息 (需要JWT认证, 权限检查: 谱曲上传者)
*   `PUT /{score_id}`
    *   描述：用户更新自己上传的谱曲信息。
    *   请求体：同上传接口，但只包含需要更新的字段。
    *   响应 (成功 `200 OK`)：更新后的谱曲对象。
    *   错误响应：`403 Forbidden` (非谱曲上传者), `404 Not Found`。

#### 3.7 删除用户自己上传的谱曲 (需要JWT认证, 权限检查: 谱曲上传者或管理员)
*   `DELETE /{score_id}`
    *   描述：用户删除自己上传的谱曲 (软删除或硬删除根据策略)。
    *   响应 (成功 `204 No Content`)。
    *   错误响应：`403 Forbidden`, `404 Not Found`。

#### 3.8 获取谱曲的评论列表 (公开)
*   `GET /{score_id}/comments`
    *   查询参数：`page`, `limit`, `sort_by` (`created_at`), `order`。
    *   响应 (成功 `200 OK`)：评论对象列表，包含用户信息和评论内容。

#### 3.9 用户提交谱曲评论 (需要JWT认证)
*   `POST /{score_id}/comments`
    *   请求体：`{"content": "This is a great score!", "parent_comment_id": null}` (parent_comment_id 用于回复)
    *   响应 (成功 `201 Created`)：新创建的评论对象。

#### 3.10 用户修改/删除自己的评论 (需要JWT认证, 权限检查: 评论者)
*   `PUT /{score_id}/comments/{comment_id}`
*   `DELETE /{score_id}/comments/{comment_id}`

#### 3.11 记录歌曲弹奏 (需要JWT认证)
*   `POST /{score_id}/log-play`
    *   描述：当用户开始或完成一次歌曲弹奏时，客户端调用此接口记录弹奏行为。此数据将用于计算歌曲弹奏排行榜。
    *   请求体 (可选, 如果需要更详细信息)：
        ```json
        {
            "device_id": "optional_device_identifier",
            "play_duration_seconds": 180 // 可选，如果记录完成的弹奏并统计时长
        }
        ```
    *   后端逻辑：
        1.  验证 `score_id` 是否存在。
        2.  获取当前登录用户的 `user_id` (如果用户已登录)。如果未登录，`user_id` 可以为 NULL (匿名弹奏)。
        3.  在 `song_play_logs` 表中插入一条新记录，包含 `score_id`, `user_id` (可空), `played_at` (当前时间), 以及请求体中提供的可选信息如 `device_id`, `ip_address` (从请求中获取), `play_duration_seconds`。
    *   响应 (成功 `202 Accepted` 或 `204 No Content`): 通常不需要返回数据体，表示记录成功即可。
    *   错误响应：`401 Unauthorized` (如果强制要求登录才能记录), `404 Not Found` (谱曲不存在)。

### 4. 排行榜接口 (`/api/v1/leaderboards`) - 公开或需JWT认证

#### 4.1 获取排行榜数据
*   `GET /`
    *   描述：获取指定类型的排行榜数据。
    *   查询参数：
        *   `board_type` (string, required, enum: `song_plays`, `user_uploads_favorites`, `user_online_time`): 榜单类型。
            *   `song_plays`: 歌曲弹奏次数榜。
            *   `user_uploads_favorites`: 用户上传谱面被收藏总数榜。
            *   `user_online_time`: 用户累计在线时长榜。
        *   `period_type` (string, required, enum: `daily`, `weekly`, `monthly`, `total`): 榜单周期。
        *   `period_key` (string, optional): 周期标识。
            *   对于 `daily`: `YYYY-MM-DD` (例如 `2025-06-01`)。如果未提供，默认为当天。
            *   对于 `weekly`: `YYYY-WW` (例如 `2025-22`，表示2025年第22周)。如果未提供，默认为当周。
            *   对于 `monthly`: `YYYY-MM` (例如 `2025-06`)。如果未提供，默认为当月。
            *   对于 `total`: 通常不需要 `period_key`，或固定为 `all_time`。
        *   `page` (int, optional, default 1): 当前页码。
        *   `limit` (int, optional, default 20): 每页数量 (例如 Top 100，则 limit=100)。
    *   响应 (成功 `200 OK`):
        ```json
        {
            "board_info": {
                "board_type": "song_plays",
                "period_type": "daily",
                "period_key": "2025-06-01",
                "title": "歌曲弹奏日榜 (2025-06-01)" // 后端生成可读标题
            },
            "data": [
                {
                    "rank": 1,
                    "entity_id": 123, // score_id 或 user_id
                    "score_value": 5800, // 例如弹奏次数
                    "display_data": { // 预存的冗余数据，具体内容根据 board_type 不同
                        // 如果 board_type = 'song_plays'
                        "song_title": "天空之城",
                        "artist_name": "久石让",
                        "cover_image_url": "http://example.com/cover1.jpg"
                        // 如果 board_type = 'user_uploads_favorites' 或 'user_online_time'
                        // "username": "MusicLover99",
                        // "nickname": "音乐爱好者",
                        // "avatar_url": "http://example.com/avatar1.jpg"
                    }
                },
                {
                    "rank": 2,
                    "entity_id": 456,
                    "score_value": 5500,
                    "display_data": {
                        "song_title": "卡农",
                        "artist_name": "帕赫贝尔",
                        "cover_image_url": "http://example.com/cover2.jpg"
                    }
                }
                // ...更多排名条目
            ],
            "pagination": {
                "total_items": 100, // 榜单总条目数 (例如Top100)
                "total_pages": 5,
                "current_page": 1,
                "limit": 20
            }
        }
        ```
    *   后端逻辑：
        1.  根据 `board_type`, `period_type`, `period_key` (如果未提供则计算默认值) 查询 `leaderboards` 表。
        2.  应用分页。
        3.  返回结果。
    *   错误响应：`400 Bad Request` (参数无效)，`404 Not Found` (指定榜单数据不存在)。

### 5. 分类接口 (`/api/v1/categories`)

#### 5.1 获取所有谱曲分类列表 (公开)
*   `GET /`
    *   描述：获取所有公开的谱曲分类，支持层级结构。
    *   查询参数：`parent_id` (int, optional): 获取指定父分类下的子分类。
    *   响应 (成功 `200 OK`)：分类对象列表。
        每个对象包含：`id`, `name`, `slug`, `parent_category_id`, `description`, `icon_url`, `score_count` (该分类下的谱曲数量)。
        如果支持层级，可以返回树状结构或扁平结构并指明父子关系。
        ```json
        // 示例：树状结构
        [
            {
                "id": 1,
                "name": "古典",
                "slug": "classical",
                "children": [
                    {"id": 5, "name": "巴洛克", "slug": "baroque", "children": []}
                ]
            }
        ]
        ```

#### 4.2 获取指定分类详情 (公开)
*   `GET /{category_id_or_slug}`
    *   描述：获取单个分类的详细信息。
    *   响应 (成功 `200 OK`)：分类对象。

#### 管理员操作分类接口 (移至管理员接口部分)
*   `(管理员) POST /`：创建新分类。
*   `(管理员) PUT /{category_id}`：更新分类信息。
*   `(管理员) DELETE /{category_id}`：删除分类 (需考虑分类下有谱曲的情况)。

### 5. 工单接口 (`/api/v1/tickets`) - 需要JWT认证

#### 5.1 获取当前用户的工单列表
*   `GET /`
    *   查询参数：`page`, `limit`, `status` (enum), `priority` (enum), `sort_by` (`created_at`, `updated_at`), `order`。
    *   响应 (成功 `200 OK`)：工单对象列表，包含工单号、标题、状态、优先级、创建时间、最后更新时间。

#### 5.2 用户提交新工单
*   `POST /`
    *   请求体：
        ```json
        {
            "title": "无法登录我的账户",
            "description": "详细描述问题...",
            "priority": "high", // "low", "medium", "high", "urgent"
            "category": "账号问题", // 可选，预定义的工单分类
            "attachments": [ // 可选, 附件信息 (可能需要先上传附件获取ID)
                {"file_id": "attachment_uuid_1", "filename": "screenshot1.png"}
            ]
        }
        ```
    *   响应 (成功 `201 Created`)：新创建的工单对象，包含 `ticket_number`。

#### 5.3 获取指定工单详情及回复
*   `GET /{ticket_id_or_number}`
    *   描述：获取工单详细信息以及所有回复列表 (分页)。用户只能查看自己的工单。
    *   响应 (成功 `200 OK`)：工单对象，内含 `replies` 列表 (回复者信息、内容、时间、附件)。

#### 5.4 用户回复工单
*   `POST /{ticket_id_or_number}/replies`
    *   请求体：
        ```json
        {
            "content": "这是我的补充说明...",
            "attachments": [] // 可选
        }
        ```
    *   响应 (成功 `201 Created`)：新创建的回复对象。
    *   注意：回复后可能需要更新工单的 `status` (例如改为 `awaiting_reply` 等待客服处理) 和 `last_reply_at`。

#### 5.5 用户关闭自己的工单
*   `POST /{ticket_id_or_number}/close`
    *   描述：用户认为问题已解决，可以关闭工单。
    *   响应 (成功 `200 OK`)：更新后的工单对象。

#### 5.6 上传工单附件 (通用附件接口)
*   参考下面的 `/api/v1/attachments` 接口。

### 6. 代理接口 (`/api/v1/resellers`) - 需要JWT认证，且用户角色为`reseller` (或`admin`)

#### 代理自身信息管理 (`/me/...`)
*   `GET /me/dashboard`：代理查看自己的仪表盘。
    *   响应：统计信息，如总销售额、总佣金、已售卡密数量、下级代理数量、当前余额、待结算佣金等。
*   `GET /me/profile`：获取代理详细信息。
   *   响应：包含 `user_id`, `parent_reseller_user_id`, `commission_rate` (特定覆盖值), `balance`, `reseller_level_value`, 关联的 `reseller_levels` 的详细信息 (如 `level_name`, `level_description`), `consignment_limit_count`, `consignment_limit_value`, `status`, `total_sales_amount`, `total_commission_earned`, `remarks`, `created_at` 等。
*   `PUT /me/profile`：代理更新自己的部分信息 (例如提现账户等，敏感信息由管理员修改)。

#### 代理卡密管理 (`/me/licenses`)
*   `GET /me/licenses`：代理查看自己生成/拥有的卡密列表（包括购买的和寄售的）。
    *   查询参数：`page`, `limit`, `license_type_code` (或 `license_type_id`), `status` (可包含 `available`, `used`, `expired`, `disabled`, `consigned_available`), `batch_id`, `key_string` (模糊搜索), `is_consigned` (boolean, 用于筛选是否为寄售卡密), `settlement_status` (筛选寄售卡密的结算状态), `sort_by`, `order`。
    *   响应：卡密列表，每个卡密对象中应明确标识是否为寄售 (`is_consigned: true/false`)，以及寄售相关的 `consignment_reseller_id` (虽然是自己，但可用于一致性), `settlement_status`。包含卡密状态、类型、有效期、激活用户信息 (如果已激活) 等。
        ```json
        // 响应中卡密对象示例
        {
            "key_string": "CONSIGN-XXXX-AAAA",
            "license_type_code": "MONTHLY_STD",
            "license_type_name": "标准月卡",
            "status": "consigned_available",
            "user_id": null, // 未激活
            "activated_at": null,
            "expires_at": null,
            "issued_by_user_id": 1, // 卡密最初发放者
            "batch_id": "batch_abc",
            "is_consigned": true, // 新增字段
            "consignment_reseller_id": 55, // 当前代理ID
            "settlement_status": "pending", // 新增字段
            "created_at": "...",
            "updated_at": "..."
        }
        ```
*   `POST /me/licenses`：代理生成卡密（购买模式）。
    *   请求体：
        ```json
        {
            "license_type_code": "MONTHLY_STD", // 或者 license_type_id
            "quantity": 10,
            // "duration_days" 已移除, 时长由 license_type 定义
            "notes": "给VIP客户的月卡"
        }
        ```
    *   响应 (成功 `201 Created`)：生成的卡密列表或批次ID。
        ```json
        {
            "batch_id": "uuid_batch_id",
            "generated_keys": [
                {"key_string": "...", "license_type_code": "MONTHLY_STD", ...},
                ...
            ],
            "message": "成功生成10张月卡"
        }
        ```
*   **后端拿货价计算逻辑说明**：
        *   当代理生成卡密时，后端会根据代理的 `user_id`、`reseller_level_value` 和请求的 `license_type_code` (或对应的 `license_type_id`)，去 `reseller_discounts` 表中查找最合适的折扣规则（基于优先级，并考虑规则的有效期和激活状态）。
        *   查找顺序：特定代理对特定卡密类型 -> 特定代理对所有卡密类型 -> 特定代理级别对特定卡密类型 -> 特定代理级别对所有卡密类型。
        *   如果找到匹配规则：
            *   获取该卡密类型的 `license_types.price` (建议零售价) 作为基准。
            *   若规则类型为 `percentage`，则实际拿货价 = `license_types.price` * (1 - `reseller_discounts.discount_value`)。
            *   若规则类型为 `fixed_price`，则实际拿货价 = `reseller_discounts.discount_value`。
        *   如果未找到任何适用的 `reseller_discounts` 规则，则使用 `license_types.reseller_cost` 作为最终拿货价。
        *   系统会根据计算出的实际拿货价扣减代理的预存款余额（如果采用预存款模式）。
    *   错误响应：`400 Bad Request` (数量超出限制/额度不足/无效的license_type_code), `403 Forbidden` (无权限生成该类型卡密)。
*   `GET /me/licenses/quotas`：查看代理的卡密生成额度 (如果启用额度管理)。额度将按 `license_type_id` 或 `license_type_code` 区分。

#### 代理寄售卡密管理 (`/me/licenses/consign`) - 新增

*   `POST /me/licenses/consign`
    *   描述：代理申请领用寄售卡密。领用的卡密会计入代理的寄售额度，但此时不会扣除代理余额。成本将在卡密被最终用户激活时结算。
    *   请求体：
        ```json
        {
            "license_type_code": "MONTHLY_STD", // 或 license_type_id, 必须是允许寄售的类型
            "quantity": 5,
            "notes": "淘宝店备货 (寄售)" // 可选备注
        }
        ```
    *   后端逻辑：
        1.  验证代理身份和权限。
        2.  检查代理的 `consignment_limit_count` 和 `consignment_limit_value` 是否足够支持本次领用。
            *   计算本次领用卡密的总价值 (数量 * 该卡密类型的代理拿货价)。拿货价的计算逻辑同 `POST /me/licenses`。
            *   如果数量超出 `consignment_limit_count` 剩余额度，或总价值超出 `consignment_limit_value` 剩余额度，则拒绝请求。
        3.  从 `license_keys` 表中查找状态为 `available` 且 `license_type_id` 匹配的卡密，或者按需生成新的卡密，数量为请求的 `quantity`。
        4.  对于每一张被领用的卡密：
            *   将其 `status` 更新为 `consigned_available`。
            *   将其 `consignment_reseller_id` 更新为当前代理的 `user_id`。
            *   将其 `settlement_status` 保持或设置为 `pending`。 (虽然激活才算pending，但此处可先标记)
            *   `issued_by_user_id` 仍记录最初生成此卡密的管理员或（购买此卡密批次的）源头。
        5.  (可选) 如果系统中有 `reseller_licenses_quotas` 并且寄售也计入某种额度，则相应更新。
    *   响应 (成功 `201 Created`)：
        ```json
        {
            "message": "成功领用5张寄售卡密。",
            "consigned_keys": [
                {"key_string": "CONSIGN-XXXX-AAAA", "license_type_code": "MONTHLY_STD", "expires_at": null}, // 寄售卡密在激活前通常没有expires_at
                // ... 其他卡密
            ],
            "consignment_status": {
                "current_consigned_count": 15, // 领用后总寄售数量
                "remaining_consignment_count_limit": 35,
                "current_consigned_value": 150.00, // 领用后总寄售价值
                "remaining_consignment_value_limit": 350.00
            }
        }
        ```
    *   错误响应：`400 Bad Request` (无效的 `license_type_code`，数量错误，超出寄售额度，系统无足够卡密)，`403 Forbidden`。

#### 代理查看下级业绩 (新增)
*   `GET /api/v1/resellers/me/subordinates/performance`
    *   描述：允许当前登录的代理查看其直接下级代理的汇总业绩，包括这些下级为其贡献的间接佣金。
    *   权限：需要JWT认证，且用户角色为 `reseller`。
    *   查询参数：
        *   `page` (int, optional, default 1): 当前页码。
        *   `limit` (int, optional, default 10): 每页数量。
        *   `sort_by` (string, optional, default `total_commission_contributed_to_me`): 排序字段, 可选值如 `sub_reseller_username`, `total_sales_by_sub_count`, `total_sales_by_sub_value`, `total_commission_contributed_to_me`。
        *   `order` (string, optional, enum: `asc`, `desc`, default `desc`): 排序方式。
        *   `start_date` (string, optional, format `YYYY-MM-DD`): 业绩统计开始日期。
        *   `end_date` (string, optional, format `YYYY-MM-DD`): 业绩统计结束日期。
        *   `sub_reseller_username_search` (string, optional): 按下级代理用户名模糊搜索。
    *   成功响应 (`200 OK`):
        ```json
        {
            "data": [
                {
                    "sub_reseller_user_id": 101,
                    "sub_reseller_username": "agent_L2_Alice",
                    "sub_reseller_level_name": "二级代理", // 从 sub_reseller_user_id 关联的 reseller_levels 获取
                    "total_direct_sales_count_by_sub": 50, // 此L2代理直接销售（或其L3下级销售）的卡密数量
                    "total_direct_sales_value_by_sub": 2500.00, // 这些卡密的总售价（如果可统计）或总拿货成本 (对L2而言)
                    "total_commission_contributed_to_me": 125.00, // 此L2代理（及其团队）为当前登录代理（L1）贡献的间接佣金总额
                    "period_start_date": "2025-05-01", // 如果查询参数提供了
                    "period_end_date": "2025-05-31"  // 如果查询参数提供了
                },
                {
                    "sub_reseller_user_id": 102,
                    "sub_reseller_username": "agent_L2_Bob",
                    "sub_reseller_level_name": "二级代理",
                    "total_direct_sales_count_by_sub": 30,
                    "total_direct_sales_value_by_sub": 1500.00,
                    "total_commission_contributed_to_me": 75.00,
                    "period_start_date": "2025-05-01",
                    "period_end_date": "2025-05-31"
                }
            ],
            "pagination": {
                "total_items": 2,
                "total_pages": 1,
                "current_page": 1,
                "limit": 10
            },
            "summary_for_me": { // 当前登录代理从所有直接下级获得的总业绩概览
                "total_direct_subordinates_count": 2,
                "overall_commission_from_direct_subordinates": 200.00, // 当前查询条件下，所有直接下级贡献的总佣金
                "overall_sales_count_by_direct_subordinates": 80,     // 当前查询条件下，所有直接下级团队的总销售数量
                "overall_sales_value_by_direct_subordinates": 4000.00, // 当前查询条件下，所有直接下级团队的总销售额
                "period_start_date": "2025-05-01",
                "period_end_date": "2025-05-31"
            }
        }
        ```
    *   后端逻辑概要:
        1.  获取当前登录代理的 `user_id` (设为 `current_agent_id`)。
        2.  查询 `resellers` 表，找到所有 `parent_reseller_user_id = current_agent_id` 的直接下级代理ID列表 (`direct_sub_ids`)。
        3.  对于每个 `direct_sub_id` (L2代理):
            *   **计算其为 `current_agent_id` (L1) 贡献的佣金**:
                *   查询 `commission_records` 表，条件为:
                    *   `beneficiary_reseller_user_id = current_agent_id`
                    *   `commission_type = 'indirect_level_1'` 且 `source_reseller_user_id` 是 `direct_sub_id` (即L2的直接销售产生的佣金给L1)
                    *   **OR** `commission_type = 'indirect_level_2'` 且 `source_reseller_user_id` 是 `direct_sub_id` 的下级 (即L3的销售产生的佣金给L1，此时 `direct_sub_id` 是中间的L2)。更准确地说，应该是 `source_license_id` 关联的 `license_keys.consignment_reseller_id` (L3) 的 `parent_reseller_user_id` 是 `direct_sub_id`。
                    *   简单来说：`beneficiary_reseller_user_id = current_agent_id` 且 `source_reseller_user_id` 的上级是 `current_agent_id` (对应 `indirect_level_1` from L2) 或者 `source_reseller_user_id` 的上上级是 `current_agent_id` (对应 `indirect_level_2` from L3, where L2 is `direct_sub_id`).
                    *   更精确的筛选方式: 查找 `commission_records` 中 `beneficiary_reseller_user_id = current_agent_id`。然后，对于每一条这样的记录，检查 `source_reseller_user_id` (L3) 的 `parent_reseller_user_id` (L2)。如果这个L2等于 `direct_sub_id`，则这条佣金计入。
                    *   如果 `commission_records` 中 `beneficiary_reseller_user_id = current_agent_id` 且 `source_reseller_user_id = direct_sub_id` (这意味着 `direct_sub_id` 直接销售，`current_agent_id` 是其直接上级)，那么 `commission_type` 应该是 `indirect_level_1`。
                *   汇总这些佣金的 `commission_amount` 得到 `total_commission_contributed_to_me`。
            *   **计算该 `direct_sub_id` (L2) 及其团队的销售业绩 (作为参考)**:
                *   查询 `license_keys` 表，条件是：
                    *   `consignment_reseller_id = direct_sub_id` (L2直接寄售并激活)
                    *   **OR** `consignment_reseller_id` 是 `direct_sub_id` 的下级代理 (L3寄售并激活)。
                    *   并且卡密状态为 `used` (已激活)。
                    *   (如果也考虑非寄售模式) `issued_by_user_id = direct_sub_id` 或 `direct_sub_id` 的下级，并且状态为 `used`。
                *   汇总这些卡密的数量 (`total_direct_sales_count_by_sub`) 和总价值 (例如按 `license_types.price` 计算 `total_direct_sales_value_by_sub`)。
        4.  应用分页和排序。
        5.  计算 `summary_for_me` 中的总计值。
    *   错误响应：`401 Unauthorized`, `403 Forbidden`。

#### 代理佣金与提现 (`/me/commissions`, `/me/withdrawals`)
*   `GET /me/commissions`：代理查看自己的佣金记录列表。
    *   查询参数：`page`, `limit`, `status` (`pending`, `settled`, `frozen`, `cancelled`), `sort_by` (`created_at`, `commission_amount`), `order`, `commission_type` (enum: `'direct'`, `'indirect_level_1'`, `'indirect_level_2'`), `start_date`, `end_date`。
    *   响应 (成功 `200 OK`): 佣金记录列表，每个记录包含 `id`, `beneficiary_reseller_user_id` (通常是自己), `source_license_id`, `source_reseller_user_id` (销售源头代理), `source_reseller_username` (销售源头代理用户名), `commission_amount`, `commission_rate`, `commission_base_amount`, `commission_type`, `depth_from_source`, `status`, `settled_at`, `related_user_id` (最终激活用户), `remarks`, `created_at`。
        ```json
        // 响应中佣金记录对象示例
        {
            "id": 123,
            "beneficiary_reseller_user_id": 55, // 当前代理ID
            "source_license_id": 789,
            "source_license_key_string": "KEY-L3-XYZ", // 关联卡密号
            "source_reseller_user_id": 105, // L3代理ID
            "source_reseller_username": "agent_L3_Charlie",
            "commission_amount": 15.75,
            "commission_rate": 0.0500, // L1从L3获得的佣金率
            "commission_base_amount": 315.00, // L3对此卡密的拿货成本
            "commission_type": "indirect_level_2",
            "depth_from_source": 2,
            "status": "pending",
            "settled_at": null,
            "related_user_id": 201, // 最终激活此卡密的用户ID
            "related_username": "final_user_David", // 最终激活此卡密用户名
            "remarks": "来自下下级代理Charlie的月卡销售",
            "created_at": "2025-06-01T12:00:00Z"
        }
        ```
*   `GET /me/withdrawals`：代理查看自己的提现申请记录列表。
*   `POST /me/withdrawals`：代理提交提现申请。
    *   请求体：`{"amount": 100.00, "payment_method": "alipay", "payment_account": "<EMAIL>", "remarks": "请尽快处理"}`
    *   响应 (成功 `201 Created`)：提现申请详情。
    *   错误响应：`400 Bad Request` (余额不足，金额过小/过大)。

#### 下级代理管理 (如果支持) (`/me/sub-resellers`)
*   `GET /me/sub-resellers`：代理查看自己的下级代理列表。
*   `POST /me/sub-resellers`：代理邀请/创建下级代理 (具体流程根据业务定)。
    *   请求体：`{"username": "sub_agent_username", "email": "...", "initial_commission_rate": 0.05}`

#### 管理员操作代理接口 (移至管理员接口部分)

### 7. 管理员接口 (`/api/v1/admin`) - 需要JWT认证，且用户角色为`admin`

#### 7.1 用户管理 (`/users`)
*   `GET /users`：获取用户列表 (分页、搜索、筛选角色/状态)。
*   `GET /users/{user_id}`：获取指定用户详情。
*   `PUT /users/{user_id}`：管理员更新用户信息 (如昵称、邮箱-可能需用户确认)。
*   `PUT /users/{user_id}/status`：修改用户状态（`active`, `inactive`, `banned`）。
    *   请求体：`{"status": "banned", "ban_reason": "违规操作"}`
*   `PUT /users/{user_id}/role`：修改用户角色 (`user`, `reseller`, `admin`)。
    *   请求体：`{"role": "reseller"}`
*   `DELETE /users/{user_id}`：删除用户 (逻辑删除/物理删除)。
*   `POST /users/{user_id}/impersonate`：(高危权限) 管理员模拟登录某用户账户进行操作。
*   `PUT /users/{user_id}/max-devices`：管理员修改用户的最大同时在线设备数。
    *   请求体：`{"max_concurrent_devices": 3}`
    *   响应 (成功 `200 OK`): 更新后的用户信息或简单成功消息。
*   `GET /users/{user_id}/bound-devices`：管理员查看指定用户已绑定的设备列表。
*   响应 (成功 `200 OK`): 包含 `user_bound_devices` 表相关字段的列表，例如 `device_id`, `device_alias`, `bound_at`, `last_active_at`, `ip_address`, `user_agent`, `session_token` (如果存在且活跃)。
*   `DELETE /users/{user_id}/bound-devices/{deviceId}/kick`：管理员强制结束（踢掉）用户在某个设备上的活动会话，并可选择同时解绑该设备。
*   URL参数: `{deviceId}` - 要操作的设备ID。
*   请求体 (可选, JSON):
*     ```json
*     {
*         "unbind_device": false // boolean, 可选, 默认为 false。如果为 true，则在踢出会话的同时解绑该设备。
*     }
*     ```
*   后端逻辑:
*     1.  根据 `user_id` 和 `deviceId` 从 `user_bound_devices` 表中找到对应的设备记录。
*     2.  如果找到记录，清除其 `session_token` (设为 NULL 或空字符串) 以实现踢出效果。
*     3.  如果请求体中 `unbind_device` 为 `true`:
*         *   从 `user_bound_devices` 表中删除该设备记录。
*         *   在 `user_device_unbind_logs` 中记录此次解绑操作，`unbind_reason` 可标记为 "管理员踢出并解绑"。管理员操作的解绑不触发用户侧的惩罚逻辑（如扣时或计次），但应记录。
*   响应 (成功 `204 No Content`)。

#### 7.2 卡密类型管理 (`/admin/license-types`) - 新增
*   `GET /license-types`
    *   描述：获取所有卡密类型定义列表。
    *   查询参数：`page`, `limit`, `is_active` (boolean), `sort_by` (`name`, `created_at`), `order`.
    *   响应 (成功 `200 OK`)：卡密类型对象列表，包含 `id`, `name`, `code`, `description`, `duration_value`, `duration_unit`, `max_concurrent_devices`, `price`, `reseller_cost`, `is_active`, `is_publicly_available`, `created_at`, `updated_at`。
*   `POST /license-types`
*   `PUT /resellers/{user_id}/consignment-limits`：管理员设置或更新指定代理的寄售额度。
    *   描述：允许管理员为特定代理配置其可以领用的寄售卡密的最大数量和总价值。
    *   URL参数: `{user_id}` - 目标代理的用户ID。
    *   请求体：
        ```json
        {
            "consignment_limit_count": 50,  // 允许持有的最大寄售卡密数量
            "consignment_limit_value": 500.00 // 允许持有的最大寄售卡密总价值 (按拿货价计)
        }
        ```
    *   后端逻辑：
        1.  验证管理员身份和权限。
        2.  查找 `user_id` 对应的代理是否存在。
        3.  更新 `resellers` 表中该代理的 `consignment_limit_count` 和 `consignment_limit_value` 字段。
    *   响应 (成功 `200 OK`)：
        ```json
        {
            "message": "代理寄售额度更新成功。",
            "user_id": 123,
            "consignment_limit_count": 50,
            "consignment_limit_value": 500.00
        }
        ```
    *   错误响应：`400 Bad Request` (参数无效), `404 Not Found` (代理用户不存在)。
    *   描述：管理员创建新的卡密类型。
    *   请求体：
        ```json
        {
            "name": "7天畅玩卡",
            "code": "PLAY_7D", // 必须全局唯一
            "description": "连续7天无限制访问",
            "duration_value": 7,
            "duration_unit": "days", // 'days', 'hours', 'minutes', 'permanent'
            "max_concurrent_devices": 2,
            "price": 10.00, // 可选
            "reseller_cost": 6.00, // 可选
            "is_active": true,
            "is_publicly_available": true
        }
        ```
    *   响应 (成功 `201 Created`)：新创建的卡密类型对象。
    *   错误响应：`400 Bad Request` (参数校验失败, code已存在)。
*   `GET /license-types/{type_id_or_code}`
    *   描述：获取指定ID或code的卡密类型详情。
    *   响应 (成功 `200 OK`)：卡密类型对象。
    *   错误响应：`404 Not Found`。
*   `PUT /license-types/{type_id_or_code}`
    *   描述：管理员更新卡密类型信息。部分字段（如 `code`）可能不允许修改，或修改后有级联影响。
    *   请求体：同创建，但只包含需要更新的字段。
    *   响应 (成功 `200 OK`)：更新后的卡密类型对象。
    *   错误响应：`400 Bad Request`, `404 Not Found`。
*   `DELETE /license-types/{type_id_or_code}`
    *   描述：管理员删除卡密类型。如果该类型已被卡密使用，可能需要先将其关联的卡密处理掉，或者只允许逻辑删除 (标记为 `is_active: false`)。
    *   响应 (成功 `204 No Content` 或 `200 OK` 如果是逻辑删除)。
    *   错误响应：`404 Not Found`, `409 Conflict` (如果类型仍在使用中且不允许直接删除)。

#### 7.3 卡密管理 (`/admin/licenses`)
*   `GET /licenses`：管理所有卡密列表 (分页、搜索、筛选 `license_type_id` 或 `license_type_code`/状态/使用者/发放者)。
*   `POST /licenses`：管理员生成卡密 (权限更大，可指定任意参数)。
*   请求体：`{"license_type_code": "PERMANENT_VIP", "quantity": 1, "issued_to_user_id": 123 (可选), "notes": "赠送给xx"}` (确保 `license_type_code` 存在且有效)
*   `GET /licenses/{license_id_or_key}`：获取卡密详情，响应中应包含关联的 `license_type` 的详细信息或关键代码/名称。
*   `PUT /licenses/{license_id_or_key}`：修改卡密信息 (如状态 `disabled`, 备注)。不建议通过此接口直接修改卡密的类型或有效期，这些应由 `license_types` 定义或通过专门的续期/升级流程处理。
*   请求体：`{"status": "disabled", "notes": "用户退款"}`
*   `DELETE /licenses/{license_id_or_key}`：作废/删除卡密。

#### 7.4 谱曲管理 (`/scores`)
*   `GET /scores`：获取所有谱曲列表 (包括待审核、私有等，分页、搜索、筛选状态/分类/上传者)。
*   `GET /scores/{score_id}`：获取任意谱曲详情。
*   `PUT /scores/{score_id}`：管理员修改任意谱曲信息。
*   `DELETE /scores/{score_id}`：管理员删除任意谱曲。
*   `GET /scores/pending-approval`：获取待审核谱曲列表。
*   `POST /scores/{score_id}/approve`：审核通过谱曲。
    *   请求体 (可选): `{"approval_notes": "符合规范"}`
*   `POST /scores/{score_id}/reject`：审核拒绝谱曲。
    *   请求体 (必填): `{"rejection_reason": "谱曲内容不清晰/版权问题"}`
*   `GET /scores/{score_id}/comments`：查看谱曲的所有评论。
*   `PUT /scores/{score_id}/comments/{comment_id}/status`：修改评论状态 (`visible`, `hidden`)。
*   `DELETE /scores/{score_id}/comments/{comment_id}`：删除评论。

#### 7.4 分类管理 (`/categories`)
*   `GET /categories` (同公开接口，但可能包含非激活分类)
*   `POST /categories`：创建新分类。
    *   请求体：`{"name": "电子琴", "slug": "electronic-keyboard", "parent_category_id": null, "description": "...", "icon_url": "...", "sort_order": 10, "is_active": true}`
*   `PUT /categories/{category_id}`：更新分类信息。
*   `DELETE /categories/{category_id}`：删除分类 (需要处理分类下有谱曲的情况，例如提示先转移谱曲)。

#### 7.5 工单管理 (`/tickets`)
*   `GET /tickets`：管理所有工单列表 (分页、搜索、筛选状态/优先级/负责人)。
*   `GET /tickets/{ticket_id_or_number}`：获取任意工单详情及回复。
*   `POST /tickets/{ticket_id_or_number}/replies`：管理员回复工单。
    *   请求体：`{"content": "我们正在处理您的问题。", "is_internal": false}`
*   `PUT /tickets/{ticket_id_or_number}/status`：管理员更新工单状态。
    *   请求体：`{"status": "in_progress"}`
*   `PUT /tickets/{ticket_id_or_number}/assign`：分配工单给客服/其他管理员。
    *   请求体：`{"assigned_to_user_id": 5}`

#### 7.6 代理管理 (`/resellers`)
*   `GET /resellers`：管理员获取所有代理列表 (分页、搜索、筛选`reseller_level_value`/状态)。
    *   响应中每个代理对象应包含其 `reseller_level_value` 及关联的级别名称 (来自 `reseller_levels.name`)。
*   `GET /resellers/{user_id}`：获取指定代理的详细信息。
    *   响应中应包含代理的 `user_id`, `parent_reseller_user_id`, `commission_rate` (特定覆盖值), `balance`, `reseller_level_value`, 关联的 `reseller_levels` 的详细信息 (如 `level_name`, `level_description`), `status`, `total_sales_amount`, `total_commission_earned`, `remarks`, `created_at`, `updated_at`。
*   `POST /resellers`：管理员添加新代理 (关联到一个已存在的用户，或创建新用户并设为代理)。
    *   请求体：`{"user_id": 10, "commission_rate": 0.15 (可选, 覆盖级别默认), "reseller_level_value": 1 (关联到 reseller_levels.level_value), "parent_reseller_user_id": null, "status": "active", "consignment_limit_count": 0 (可选, 默认0), "consignment_limit_value": 0.00 (可选, 默认0.00)}`
*   `PUT /resellers/{user_id}`：管理员更新代理信息 (佣金比例、`reseller_level_value`、状态、余额调整-需谨慎并记录、寄售额度)。
    *   请求体示例: `{"commission_rate": 0.18, "reseller_level_value": 2, "status": "active", "consignment_limit_count": 50, "consignment_limit_value": 500.00}`
*   `DELETE /resellers/{user_id}`：管理员移除代理资格 (注意处理其下级代理和未结算佣金)。
*   `GET /resellers/{user_id}/licenses`：查看指定代理生成的卡密。
*   `GET /resellers/{user_id}/commissions`：查看指定代理的佣金记录。
*   `GET /resellers/{user_id}/sub-resellers`：查看指定代理的下级代理。
*   `GET /resellers/withdrawal-requests`：查看所有代理提现请求。
    *   查询参数：`status` (`pending`, `approved`, etc.)
*   `POST /resellers/withdrawal-requests/{request_id}/approve`：批准提现。
*   `POST /resellers/withdrawal-requests/{request_id}/reject`：拒绝提现。
    *   请求体：`{"admin_notes": "账户信息不符"}`
*   `POST /resellers/withdrawal-requests/{request_id}/complete`：标记提现完成 (已打款)。

#### 7.6a 代理级别管理 (`/admin/reseller-levels`)

*   `GET /reseller-levels`
    *   描述：获取所有代理级别定义列表。
    *   查询参数：`page`, `limit`, `is_active` (boolean), `sort_by` (`name`, `level_value`), `order`.
    *   响应 (成功 `200 OK`)：代理级别对象列表，包含 `id`, `level_value`, `name`, `description`, `upgrade_threshold_sales`, `upgrade_threshold_sub_resellers`, `default_commission_rate`, `default_sub_commission_rate`, `default_second_tier_sub_commission_rate`, `is_active`, `created_at`, `updated_at`。
*   `POST /reseller-levels`
    *   描述：管理员创建新的代理级别。
    *   请求体：
        ```json
        {
            "level_value": 1,
            "name": "普通代理",
            "description": "初级代理级别",
            "upgrade_threshold_sales": 1000.00,
            "upgrade_threshold_sub_resellers": 0,
            "default_commission_rate": 0.1000, // 直接销售佣金率 (平台额外奖励，如果适用)
            "default_sub_commission_rate": 0.0500, // 从直接下级(L2)获得的间接佣金率
            "default_second_tier_sub_commission_rate": 0.0200, // 从下两级(L3)获得的间接佣金率
            "is_active": true
        }
        ```
    *   响应 (成功 `201 Created`)：新创建的代理级别对象，包含所有请求的字段。
    *   错误响应：`400 Bad Request` (参数校验失败, `level_value` 或 `name` 已存在)。
*   `GET /reseller-levels/{level_id_or_value}`
    *   描述：获取指定ID或`level_value`的代理级别详情。
    *   响应 (成功 `200 OK`)：代理级别对象，包含所有字段，包括 `default_second_tier_sub_commission_rate`。
    *   错误响应：`404 Not Found`。
*   `PUT /reseller-levels/{level_id_or_value}`
    *   描述：管理员更新代理级别信息。
    *   请求体：同创建，但只包含需要更新的字段。确保可以更新 `default_second_tier_sub_commission_rate`。
    *   响应 (成功 `200 OK`)：更新后的代理级别对象。
    *   错误响应：`400 Bad Request`, `404 Not Found`。
*   `DELETE /reseller-levels/{level_id_or_value}`
    *   描述：管理员删除代理级别。如果该级别已被代理使用，可能需要先处理关联的代理，或者只允许逻辑删除 (标记为 `is_active: false`)。
    *   响应 (成功 `204 No Content` 或 `200 OK` 如果是逻辑删除)。
    *   错误响应：`404 Not Found`, `409 Conflict` (如果级别仍在使用中且不允许直接删除)。

#### 7.6b 代理折扣规则管理 (`/admin/reseller-discounts`)

*   `GET /reseller-discounts`
    *   描述：获取所有代理折扣规则列表。
    *   查询参数：`page`, `limit`, `reseller_level_value`, `reseller_user_id`, `license_type_id`, `is_active`, `sort_by` (`priority`, `created_at`), `order`.
    *   响应 (成功 `200 OK`)：折扣规则对象列表，包含所有 `reseller_discounts` 表的字段。
*   `POST /reseller-discounts`
    *   描述：管理员创建新的代理折扣规则。
    *   请求体：
        ```json
        {
            "description": "金牌代理购买年卡享固定价",
            "reseller_level_value": 3, // 假设3代表金牌代理
            "reseller_user_id": null, // null 表示适用于此级别的所有代理
            "license_type_id": 5, // 假设5是年卡类型ID
            "discount_type": "fixed_price",
            "discount_value": 288.00,
            "priority": 10,
            "start_date": "2025-01-01T00:00:00Z",
            "end_date": "2025-12-31T23:59:59Z",
            "is_active": true
        }
        ```
    *   响应 (成功 `201 Created`)：新创建的折扣规则对象。
    *   错误响应：`400 Bad Request` (参数校验失败)。
*   `GET /reseller-discounts/{discount_id}`
    *   描述：获取指定ID的代理折扣规则详情。
    *   响应 (成功 `200 OK`)：折扣规则对象。
    *   错误响应：`404 Not Found`。
*   `PUT /reseller-discounts/{discount_id}`
    *   描述：管理员更新代理折扣规则信息。
    *   请求体：同创建，但只包含需要更新的字段。
    *   响应 (成功 `200 OK`)：更新后的折扣规则对象。
    *   错误响应：`400 Bad Request`, `404 Not Found`。
*   `DELETE /reseller-discounts/{discount_id}`
    *   描述：管理员删除代理折扣规则。
    *   响应 (成功 `204 No Content`)。
    *   错误响应：`404 Not Found`。
#### 7.7 系统设置管理 (`/settings`)
*   `GET /settings`：获取所有系统设置项。
*   `GET /settings/{setting_key}`：获取指定设置项。
*   `PUT /settings/{setting_key}`：更新指定系统设置项。
    *   请求体：`{"setting_value": "new_value"}`
*   `POST /settings`：(如果允许动态添加设置项) 创建新的系统设置项。

#### 7.8 审计日志 (`/audit-logs`)
*   `GET /audit-logs`：查看操作审计日志 (分页、筛选用户/操作类型/时间范围)。

#### 7.9 系统统计/仪表盘 (`/dashboard`)
*   `GET /dashboard/stats`：获取系统核心统计数据 (例如总用户数、日活、总谱曲数、待审核谱曲、总收入等)。

### 8. 通用附件上传接口 (`/api/v1/attachments`) - 需要JWT认证

*   `POST /uploads`
    *   描述：上传单个文件，返回文件ID或URL，用于关联到其他对象（如工单回复、谱曲封面）。
    *   请求体 (multipart/form-data)：
        *   `file`: 文件本身。
        *   `related_object_type` (string, optional): 预期的关联对象类型，例如 `ticket_reply`, `user_avatar`, `score_cover`。
        *   `related_object_id` (string/int, optional): 预期的关联对象ID。
    *   响应 (成功 `201 Created`):
        ```json
        {
            "file_id": "uuid_or_int_file_id",
            "filename": "original_filename.jpg",
            "url": "/path/to/stored/file.jpg", // 或完整的云存储URL
            "file_type": "image/jpeg",
            "file_size_bytes": 102400
        }
        ```
    *   错误响应：`400 Bad Request` (无文件或文件类型不支持), `413 Payload Too Large`。
*   `GET /{file_id_or_path}`：(可选) 获取/下载附件，需要权限校验。
*   `DELETE /{file_id}`：(可选) 删除附件，需要权限校验。

### 9. MIDI 处理与文件存放相关考量

#### 9.1 MIDI 转谱曲弹奏 (TXT)
*   **客户端处理方案**：
    *   用户在客户端选择MIDI文件。
    *   客户端调用本地的MIDI转换工具，生成TXT格式的谱曲内容。
    *   客户端通过 `/api/v1/scores/uploads/txt` 接口，将TXT内容及其他元数据上传。
    *   优点：服务器压力小，转换逻辑可由客户端维护。
    *   缺点：依赖客户端能力，不同客户端可能需要不同实现，转换工具安全性/一致性难保证。
*   **服务端处理方案 (推荐)**：
    *   用户通过 `/api/v1/scores/uploads/midi` 接口上传原始MIDI文件。
    *   API后端接收文件后：
        1.  **存储MIDI文件**：将MIDI文件保存到服务器指定目录 (如 `/var/www/pianomaster_data/midi_files/`) 或云存储 (如S3, OSS)。文件名建议使用UUID或内容哈希重命名，以避免冲突和安全问题。在 `scores` 表中记录 `midi_storage_type` 和 `midi_path_or_url`，以及 `original_midi_filename`。
        2.  **异步转换**：将MIDI文件路径和谱曲ID放入消息队列 (如Celery + Redis/RabbitMQ)。
        3.  **后台任务处理**：Worker进程从队列中获取任务，调用部署在服务器上的MIDI转换工具，将MIDI文件转换为TXT格式。
        4.  **更新数据库**：转换成功后，将TXT内容更新到 `scores.txt_content` 字段。如果转换失败，记录错误信息，谱曲状态可能保持为 `MIDI_REF` 且无TXT内容。
    *   优点：用户体验好 (只需上传MIDI)，转换逻辑服务端统一维护和更新，谱曲数据更完整。
    *   缺点：服务器需要处理转换任务，可能消耗较多资源；需要后台任务系统。

#### 9.2 MIDI 文件存放策略
*   **数据库字段**：
    *   `scores.type` = `MIDI_REF`
    *   `scores.midi_storage_type`: ENUM('local', 's3', 'oss') - 指明存储位置。
    *   `scores.midi_path_or_url`: VARCHAR - 存储相对路径 (local) 或完整URL (s3, oss)。
    *   `scores.original_midi_filename`: VARCHAR - 存储用户上传时的原始文件名。
*   **存储位置**：
    *   **本地服务器目录**：例如 `/var/www/pianomaster_data/midi_files/`。需要规划好目录结构，考虑备份和扩展性。
    *   **对象存储服务 (推荐)**：AWS S3, 阿里云OSS, 七牛云Kodo等。具有高可用、易扩展、可配置CDN加速等优点。此时 `midi_path_or_url` 存储文件的URL。
*   **文件名**：上传的MIDI文件应重命名 (例如使用UUID + 后缀或文件内容哈希)，防止文件名冲突和潜在的路径遍历等安全问题。原始文件名可以单独存储用于显示。

### 10. 其他重要技术补充 (API 相关)

#### 10.1 安全性
*   **HTTPS强制**：所有API通信必须通过HTTPS。
*   **输入验证**：对所有用户输入（请求体、查询参数、路径参数）进行严格的类型、格式、长度、范围验证。
*   **SQL注入防护**：使用ORM (如SQLAlchemy, Django ORM) 或参数化查询。
*   **XSS防护**：API输出的字符串如果要在HTML中展示，需要进行HTML转义。对于存储型XSS，输入时也需要清理。
*   **CSRF防护**：对于Web前端调用API，如果使用Cookie进行会话管理，需要CSRF Token。JWT通常存储在LocalStorage或HttpOnly Cookie中，后者配合SameSite属性可缓解CSRF。
*   **权限控制 (RBAC - Role-Based Access Control)**：
    *   基于用户角色 (`users.role`) 和具体资源所有权 (例如，用户只能修改自己的谱曲) 进行细粒度权限校验。
    *   使用装饰器或中间件在API处理函数前执行权限检查。
*   **敏感信息处理**：
    *   密码绝不能明文存储，使用强哈希 (Argon2, bcrypt)。
    *   API响应中避免泄露不必要的敏感数据。
    *   日志中避免记录明文密码、Token等。
*   **依赖库安全**：定期扫描和更新项目依赖的第三方库，防止已知漏洞。
*   **API密钥管理**：如果提供给第三方开发者API，需要安全的API Key生成和验证机制。

#### 10.2 文件存储与CDN
*   **MIDI文件、谱曲封面、用户头像等静态资源**：
    *   首选对象存储 (S3, OSS) + CDN (Content Delivery Network)。
    *   CDN可以加速全球用户访问，减轻源服务器压力。
    *   API返回资源的CDN URL。
*   **访问控制**：对于私有文件（如未付费用户的付费MIDI），对象存储可以配置私有访问，通过API生成有时效性的预签名URL供客户端下载。

#### 10.3 后台任务 (Asynchronous Tasks)
*   使用 Celery + Redis/RabbitMQ 或类似的后台任务队列处理耗时操作，避免阻塞API请求：
    *   MIDI文件转换。
    *   发送邮件/短信通知 (注册验证、密码重置、卡密到期提醒、工单更新等)。
    *   批量生成卡密。
    *   数据统计与报告生成。
    *   定期清理过期数据 (如未激活的验证码、过期的session等)。
    *   定期清理 `user_bound_devices` 表中 `last_active_at` 长时间未更新的无效会话。

#### 10.4 日志记录
*   **访问日志**：记录每个API请求的基本信息 (时间、IP、用户ID、请求路径、方法、状态码、耗时)。
*   **错误日志**：详细记录API发生的错误和异常，包括堆栈信息。
*   **操作审计日志**：(见数据库设计 `audit_logs` 表) 记录关键业务操作 (用户登录、注册、卡密生成/激活、权限变更、谱曲审核、资金变动等)。
*   **日志级别**：DEBUG, INFO, WARNING, ERROR, CRITICAL。
*   **日志存储与分析**：使用ELK Stack (Elasticsearch, Logstash, Kibana) 或类似方案进行日志聚合、存储和分析。

#### 10.5 API 文档
*   使用 OpenAPI (Swagger) 规范自动生成和维护API文档。
*   FastAPI 内建支持 OpenAPI。
*   Flask 可通过 `flasgger` 等库集成。
*   文档应包含每个接口的描述、URL、HTTP方法、请求参数 (类型、是否必需、示例)、响应格式 (成功/失败示例)、所需权限等。

#### 10.6 API 测试
*   **单元测试**：测试单个函数或模块的逻辑。
*   **集成测试**：测试多个组件协同工作的情况，例如API调用到数据库操作。
*   **端到端测试 (E2E)**：模拟真实用户场景测试整个流程。
*   使用 `pytest`, `unittest` 等测试框架。

## 总结

此API设计为"弹奏大师"项目提供了一套相对完整的接口规范。在开发过程中，应持续根据实际需求和用户反馈进行迭代和优化。务必重视API的安全性、稳定性和可维护性。 