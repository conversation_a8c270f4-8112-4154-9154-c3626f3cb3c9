[tool.poetry]
name = "tanzoudashiapi"
version = "0.1.0"
description = "弹奏大师项目的后端API服务"
authors = ["白云"] # 请替换为您的信息
readme = "README.md"
packages = [{include = "app", from = "."}]

[tool.poetry.dependencies]
python = "^3.9" # 根据您的Python版本调整
fastapi = "^0.100.0" # 请检查最新稳定版
uvicorn = {extras = ["standard"], version = "^0.22.0"} # 请检查最新稳定版
sqlalchemy = "^2.0.0" # 请检查最新稳定版
# psycopg2-binary = "^2.9.5" # 如果使用 PostgreSQL, 取消注释
# pysqlite3-binary = "^0.5.0" # 如果使用 SQLite (通常用于开发/测试) - 通常不需要，Python 内建 sqlite3
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
pydantic = {extras = ["email"], version = "^2.0.0"} # Pydantic V2.x, 如果使用 V1.x 请调整
pydantic-settings = "^2.0.0" # 用于配置管理
python-dotenv = "^1.0.0"
alembic = "^1.11.1" # 请检查最新稳定版
pymysql = {extras = ["rsa"], version = "^1.1.0"} # 添加 MySQL 驱动 (pymysql)
bcrypt = "==3.2.0" # 固定 bcrypt 版本以兼容 passlib 1.7.4

[tool.poetry.dev-dependencies]
pytest = "^7.0"
mypy = "^1.0"
ruff = "^0.1.0" # 代码风格检查和格式化

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"